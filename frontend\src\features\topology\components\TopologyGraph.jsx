import React, { memo } from "react";
import { <PERSON><PERSON>, Card, Flex, Select, Tooltip } from "antd";
import {
  FileAddOutlined,
  FileSyncOutlined,
  PlusOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import { Graph } from "../topology-graph";
import { useTopology } from "../hooks/useTopology";
import { useAppStore } from "../../../store/store";

/**
 * Topology Graph component that displays the network topology visualization
 * @returns {JSX.Element} Topology Graph component
 */
const TopologyGraph = () => {
  const {
    data,
    isFetching,
    error,
    selectedServices,
    handleGraphRender,
    handleServiceChange,
    handleRefresh,
    exportTopology,
  } = useTopology();
  const { openDialogs } = useAppStore();

  // Memoize the service options
  const serviceOptions = React.useMemo(() => {
    return (
      data?.services?.map((item) => ({
        value: item,
        label: item,
      })) || []
    );
  }, [data?.services]);

  if (error) {
    return (
      <Card>
        <div>Error loading topology: {error.message}</div>
        <Button
          type="primary"
          onClick={handleRefresh}
          style={{ marginTop: 16 }}
        >
          Try Again
        </Button>
      </Card>
    );
  }

  return (
    <Card
      variant="borderless"
      title="Device Topology"
      loading={isFetching && !data}
      styles={{
        body: {
          position: "relative",
          padding: 5,
          height: `calc(100vh - 145px)`,
        },
      }}
      extra={
        <Flex gap={10}>
          {serviceOptions.length > 0 && (
            <Select
              defaultValue="All Network Service"
              style={{ width: 240 }}
              value={selectedServices}
              onChange={handleServiceChange}
              options={serviceOptions}
              disabled={isFetching}
              placeholder="Select network service"
            />
          )}
          <Tooltip title="Refresh topology">
            <Button
              type="text"
              onClick={handleRefresh}
              icon={<ReloadOutlined spin={isFetching} />}
              disabled={isFetching}
            />
          </Tooltip>
          <Tooltip title="Add manual topology">
            <Button
              type="text"
              onClick={() => openDialogs({ id: "addTopology", data: null })}
              icon={<PlusOutlined />}
            />
          </Tooltip>
          <Tooltip title="Save manual topology">
            <Button
              type="text"
              onClick={() => {
                openDialogs({
                  id: "saveRestoreTopology",
                  data: { actionType: "save" },
                });
              }}
              icon={<FileAddOutlined />}
            />
          </Tooltip>
          <Tooltip title="Restore manual topology">
            <Button
              type="text"
              onClick={() => {
                openDialogs({
                  id: "saveRestoreTopology",
                  data: { actionType: "restore" },
                });
              }}
              icon={<FileSyncOutlined />}
            />
          </Tooltip>
          <Button
            type="primary"
            onClick={exportTopology}
            disabled={isFetching || !data}
            title="Export topology as SVG"
          >
            Export Topology
          </Button>
        </Flex>
      }
    >
      <Graph
        options={{
          data,
        }}
        onDestroy={handleRefresh}
        onRender={handleGraphRender}
      />
    </Card>
  );
};

export default memo(TopologyGraph);
