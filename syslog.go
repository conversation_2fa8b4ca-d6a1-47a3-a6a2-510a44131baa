package mnms

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/icza/backscanner"
	"github.com/influxdata/go-syslog/v3"
	"github.com/influxdata/go-syslog/v3/rfc3164"
	"github.com/influxdata/go-syslog/v3/rfc5424"
	"github.com/qeof/q"
	"gopkg.in/natefinch/lumberjack.v2"
)

var (
	TotalLogsReceived int
	TotalLogsSent     int
	TotalLogsDropped  int
)

var errorNoRemoteForward = errors.New("will not forward to remote syslog, missing remote syslog server address")

const chunkSize = 1024 * 5

/*
const severityMask = 0x07
const facilityMask = 0xf8
*/
/*
Severity: RFC3164 page 9 -- numerical code 0 through 7

	0       Emergency: system is unusable
	1       Alert: action must be taken immediately
	2       Critical: critical conditions
	3       Error: error conditions
	4       Warning: warning conditions
	5       Notice: normal but significant condition
	6       Informational: informational messages
	7       Debug: debug-level messages
*/
const (
	LOG_EMERG int = iota
	LOG_ALERT
	LOG_CRIT
	LOG_ERR
	LOG_WARNING
	LOG_NOTICE
	LOG_INFO
	LOG_DEBUG
)

/*
Facility: RFC3164 page 7  -- numerical code 0 through 23

	 0             kernel messages
	 1             user-level messages
	 2             mail system
	 3             system daemons
	 4             security/authorization messages (note 1)
	 5             messages generated internally by syslogd
	 6             line printer subsystem
	 7             network news subsystem
	 8             UUCP subsystem
	 9             clock daemon (note 2)
	10             security/authorization messages (note 1)
	11             FTP daemon
	12             NTP subsystem
	13             log audit (note 1)
	14             log alert (note 1)
	15             clock daemon (note 2)
	16             local use 0  (local0)
	17             local use 1  (local1)
	18             local use 2  (local2)
	19             local use 3  (local3)
	20             local use 4  (local4)
	21             local use 5  (local5)
	22             local use 6  (local6)
	23             local use 7  (local7)
*/
const (
	LOG_KERN int = iota << 3
	LOG_USER
	LOG_MAIL
	LOG_DAEMON
	LOG_AUTH
	LOG_SYSLOG
	LOG_LPR
	LOG_NEWS
	LOG_UUCP
	LOG_CRON
	LOG_AUTHPRIV
	LOG_FTP
	_ // unused
	_ // unused
	_ // unused
	_ // unused
	LOG_LOCAL0
	LOG_LOCAL1
	LOG_LOCAL2
	LOG_LOCAL3
	LOG_LOCAL4
	LOG_LOCAL5
	LOG_LOCAL6
	LOG_LOCAL7
)

type SyslogSeverityRange struct {
	MinSeverity int `json:"min_severity"` // 0-7, -1 means no min
	MaxSeverity int `json:"max_severity"` // 0-7, -1 means no max
}

type SyslogQuery struct {
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
	MaxLine   int    `json:"max_line"`
	Filter    string `json:"filter"`
	Page      int    `json:"page"`
}

func SyslogCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo
	}
	if QC.Kind == "syslog" {
		if strings.HasPrefix(cmd, "syslog config path") {
			return SyslogConfigPathCmd(cmdinfo)
		}
		if strings.HasPrefix(cmd, "syslog config maxsize") {
			return SyslogConfigMaxSizeCmd(cmdinfo)
		}
		if strings.HasPrefix(cmd, "syslog config compress") {
			return SyslogConfigCompressCmd(cmdinfo)
		}
		if strings.HasPrefix(cmd, "syslog list") {
			return SyslogListLogFilesCmd(cmdinfo)
		}
		if strings.HasPrefix(cmd, "syslog geturl") {
			return SyslogGetUrlCmd(cmdinfo)
		}
		if strings.HasPrefix(cmd, "syslog files list") {
			return SyslogGeneratedListCmd(cmdinfo)
		}
		if strings.HasPrefix(cmd, "syslog file rm") {
			return SyslogGeneratedRemoveCmd(cmdinfo)
		}
		if strings.HasPrefix(cmd, "syslog config remote") {
			return SyslogConfigRemoteCmd(cmdinfo)
		}
		if strings.HasPrefix(cmd, "syslog config backup-after-forward") {
			return SyslogConfigBakAfterFwdCmd(cmdinfo)
		}
		if strings.HasPrefix(cmd, "syslog config severity-range-forward") {
			return SyslogConfigSeverityRangeForwardCmd(cmdinfo)
		}
		if strings.HasPrefix(cmd, "syslog introduce") {
			return SyslogIntroduceCmd(cmdinfo)
		}
		if strings.HasPrefix(cmd, "syslog config introduce interval") {
			return SyslogConfigIntroduceIntervalCmd(cmdinfo)
		}
		if strings.HasPrefix(cmd, "syslog config get") {
			return SyslogConfigGetCmd(cmdinfo)
		}
	}

	q.Q("unrecognized", cmd)
	cmdinfo.Status = "error: invalid command"
	return cmdinfo
}

func parseMessageAndSaveLog(msg, raddr string) {
	if QC.IsRoot || QC.Kind == "syslog" {
		_, _, err := parsingDataofSyslog(msg)
		// if format error,do this
		if err != nil {
			f, b, err := SyslogParsePriority(msg)
			if err != nil {
				return
			}
			p := fmt.Sprintf("<%v>", (f*8)+b)
			m := strings.ReplaceAll(msg, p, "")
			message := fmt.Sprintf("%v%v %v %v", p, time.Now().Format(time.Stamp), raddr, m)
			SaveLog(message)
		} else {
			SaveLog(msg)
		}
	}
}

type Chunk struct {
	Seq   int
	Total int
	Data  string
}

var buffers = make(map[string]map[int]string)

func StartSyslogServer() {
	// q.Q(QC.SyslogServerAddr)
	udpsock, err := net.ListenPacket("udp4", QC.SyslogServerAddr)
	if err != nil {
		q.Q(err)
		fmt.Fprintf(os.Stderr, "error: syslog server can't start at %v\n", QC.SyslogServerAddr)
		return
	}
	defer udpsock.Close()
	// TODO when need:  tcp syslog service
	lastForwardMessage := ""
	buf := make([]byte, chunkSize+100)
	for {
		n, clientAddr, err := udpsock.ReadFrom(buf)
		// q.Q(len(buf))
		if err != nil {
			q.Q(err)
			continue
		}
		msg := string(buf[:n])
		q.Q("msg length", len(msg))
		parts := strings.SplitN(msg, "|", 3)
		if len(parts) != 3 {
			q.Q("Invalid message format")
			return
		}
		id := parts[0]
		seqInfo := strings.Split(parts[1], "/")
		if len(seqInfo) != 2 {
			q.Q("Invalid sequence format")
			return
		}

		var seqNum, totalChunks int
		fmt.Sscanf(seqInfo[0], "%d", &seqNum)
		fmt.Sscanf(seqInfo[1], "%d", &totalChunks)

		if _, exists := buffers[id]; !exists {
			buffers[id] = make(map[int]string)
		}
		buffers[id][seqNum] = parts[2]
		if len(buffers[id]) == totalChunks {
			var result strings.Builder
			for i := 1; i <= totalChunks; i++ {
				result.WriteString(buffers[id][i])
			}
			if lastForwardMessage == string(result.String()) {
				continue
			}
			q.Q("syslog input", clientAddr, n)
			err = syslogInput(result.String())
			// if err!=nil, it means we did not forward the incoming syslog
			// if we did not forward the incoming syslog or SyslogBakAfterFwd is true, we save the incoming syslog
			if err != nil {
				// Implement saving and rotating logs locally. Currently
				// if there is no remote syslog server specified we drop the logs.

				if clientAddr != nil {
					parseMessageAndSaveLog(result.String(), clientAddr.String())
				}
				q.Q(err)
			} else {
				// we forwarded syslog input
				lastForwardMessage = result.String()

				// save log after forward any way
				if QC.SyslogBakAfterFwd {
					parseMessageAndSaveLog(result.String(), clientAddr.String())
				}
			}
			delete(buffers, id)
		}

	}
}

// InitRemoteSyslog init remote server
func InitRemoteSyslog() error {
	if QC.RemoteSyslogServerAddr == "" {
		return errorNoRemoteForward
	}

	if QC.RemoteSyslogServer != nil {
		QC.RemoteSyslogServer.Close()
	}
	udpsock, err := net.Dial("udp4", QC.RemoteSyslogServerAddr)
	if err != nil {
		q.Q(err)
		return err
	}
	QC.RemoteSyslogServer = udpsock
	return nil
}

// ExtractTagName extracts the tag (program name) from syslog lines like:
// <6>Apr 18 17:15:41 nms InsertDev: new device: ...
func ExtractTagName(syslogLine string) (string, error) {
	// Regex to capture the tag name before the first colon
	re := regexp.MustCompile(`^<\d+>\w+\s+\d+\s+\d+:\d+:\d+\s+\S+\s+([^:\s]+):`)

	matches := re.FindStringSubmatch(syslogLine)
	if len(matches) < 2 {
		return "", fmt.Errorf("tag name not found in syslog line")
	}
	return matches[1], nil
}

// SendMessageToRemoteSyslog send message to remote server
func SendMessageToRemoteSyslog(message []byte) error {
	id, err := ExtractTagName(string(message))
	if err != nil {
		id = "runCmd"
	}
	if len(QC.RemoteSyslogServerAddr) == 0 {
		return errorNoRemoteForward
	}

	totalChunks := (len(message) + chunkSize - 1) / chunkSize
	for i := 0; i < totalChunks; i++ {
		start := i * chunkSize
		end := start + chunkSize
		if end > len(message) {
			end = len(message)
		}
		chunk := message[start:end]
		payload := fmt.Sprintf("%s|%d/%d|%s", id, i+1, totalChunks, chunk)
		q.Q("chunk payload", payload)
		if QC.RemoteSyslogServer == nil {
			err := InitRemoteSyslog()
			if err != nil {
				return err
			}
		}
		_, err := QC.RemoteSyslogServer.Write([]byte(payload))
		if err != nil {
			return fmt.Errorf("failed to send chunk %d: %w", i, err)
		}
		time.Sleep(100 * time.Millisecond) // small delay between chunks
	}
	return nil
}

func SyslogParsePriority(buf string) (int, int, error) {
	if !strings.HasPrefix(buf, "<") {
		return 0, 0, fmt.Errorf("no syslog priority start character")
	}
	ix := strings.Index(buf, ">")
	if ix < 0 {
		return 0, 0, fmt.Errorf("no syslog priority end character")
	}
	priority, err := strconv.Atoi(buf[1:ix])
	if err != nil {
		return 0, 0, err
	}
	facility := priority / 8
	severity := priority % 8
	return facility, severity, nil
}

func checkSeverityRange(severity int) bool {
	// -1 & -1: send nothing
	if QC.SyslogSeverityRngFwd.MinSeverity == -1 && QC.SyslogSeverityRngFwd.MaxSeverity == -1 {
		return false
	}
	// -1 & 0-7: send all
	if QC.SyslogSeverityRngFwd.MinSeverity == -1 && severity <= QC.SyslogSeverityRngFwd.MaxSeverity {
		return true
	}
	// 0-7 & -1: send all
	if QC.SyslogSeverityRngFwd.MaxSeverity == -1 && severity >= QC.SyslogSeverityRngFwd.MinSeverity {
		return true
	}
	// 0-7 & 0-7: send in range
	if severity >= QC.SyslogSeverityRngFwd.MinSeverity && severity <= QC.SyslogSeverityRngFwd.MaxSeverity {
		return true
	}
	return false
}

func notifyRoot(message string) error {
	apiEndpoint := fmt.Sprintf("%s/api/v1/ws/post", QC.RootURL)
	type request struct {
		Message string `json:"message"`
	}

	msg := request{
		Message: message,
	}
	josnBytes, err := json.Marshal(msg)
	if err != nil {
		return err
	}
	res, err := PostWithToken(apiEndpoint, QC.AdminToken, bytes.NewBuffer(josnBytes))
	if err != nil {
		return err
	}
	if res != nil {
		defer res.Body.Close()
	}
	bodytext, err := io.ReadAll(res.Body)
	if err != nil {
		return err
	}
	if res.StatusCode != 200 {
		return fmt.Errorf("post message fail, %d, %s", res.StatusCode, bodytext)
	}
	return nil
}

func syslogInput(bufStr string) error {
	TotalLogsReceived++
	_, severity, err := SyslogParsePriority(bufStr)
	if err != nil {
		q.Q(err)
		return err
	}

	// check if the severity is in the range
	if send := checkSeverityRange(severity); !send {
		q.Q("severity not in range, do not forward")
		return nil
	}

	// if severity is 0-1, notify root
	if QC.Kind == "syslog" && severity < 2 {
		q.Q("severity is 0-1, notify root")
		err = notifyRoot(bufStr)
		if err != nil {
			q.Q(err)
			return err
		}
	}

	// root receive alert or error
	if QC.IsRoot && (severity == LOG_ALERT || severity == LOG_ERR) {
		SendSocketMessage(severity, bufStr)
	}

	err = SendMessageToRemoteSyslog([]byte(bufStr))
	if err != nil {
		q.Q(err)
		return err
	}

	TotalLogsSent++
	return nil
}

func SendSyslog(priority int, tag string, msg string) error {
	timestamp := time.Now().Format(time.Stamp) // XXX not RFC3339
	var name string
	if len(QC.Name) == 0 {
		name, _ = os.Hostname()
	} else {
		name = QC.Name
	}
	syslogmsg := fmt.Sprintf("<%d>%s %s %s: %s", priority, timestamp, name, tag, msg)

	err := SendMessageToRemoteSyslog([]byte(syslogmsg))
	if err != nil {
		rootSaveLog(syslogmsg)
		return err
	}
	TotalLogsSent++
	// q.Q("sent syslog", string(msg))
	return nil
}

func rootSaveLog(syslogmsg string) {
	if QC.IsRoot {
		_, severity, err := SyslogParsePriority(syslogmsg)
		if err != nil {
			q.Q(err)
		}
		SendSocketMessage(severity, syslogmsg)
		SaveLog(syslogmsg)
	} else {
		TotalLogsDropped++
		q.Q(TotalLogsDropped)
		if TotalLogsDropped%1000 == 0 {
			alertMsg := fmt.Sprintf("%d logs dropped while forwarding to remote syslog server %s", TotalLogsDropped, QC.RemoteSyslogServerAddr)
			if QC.RemoteSyslogServerAddr == "" {
				alertMsg = fmt.Sprintf("%d logs dropped, no remote syslog server(-rs) configured", TotalLogsDropped)
			}
			timestamp := time.Now().Format(time.Stamp) // XXX not RFC3339
			alertSyslogMsg := fmt.Sprintf("<%d>%s %s %s: %s", LOG_ALERT, timestamp, QC.Name, "syslog", alertMsg)
			notifyRoot(alertSyslogMsg)
		}
	}
}

func initLogger() *lumberjack.Logger {
	logger := &lumberjack.Logger{
		Filename:   QC.SyslogLocalPath,
		MaxSize:    int(QC.SyslogFileSize),
		MaxBackups: 10,
		Compress:   QC.SyslogCompress,
		LocalTime:  true,
	}
	return logger
}

var locallogger *lumberjack.Logger

// Save syslog to file
func SaveLog(data string) {
	if locallogger == nil {
		locallogger = initLogger()
	} else {
		if locallogger.Filename != QC.SyslogLocalPath || locallogger.MaxSize != int(QC.SyslogFileSize) || locallogger.Compress != QC.SyslogCompress {
			locallogger.Close()
			locallogger.Filename = QC.SyslogLocalPath
			locallogger.MaxSize = int(QC.SyslogFileSize)
			locallogger.Compress = QC.SyslogCompress
		}
	}
	data = data + "\n"
	_, err := locallogger.Write([]byte(data))
	if err != nil {
		q.Q(err)
		// remind user if file error
		SendSocketMessage(LOG_ERR, fmt.Sprintf("can open:%v, please check file", locallogger.Filename))
		return
	}
}

// Configure local syslog path.
//
// Usage : config local syslog path [path]
//
//	[path]        : local syslog path
//
// Example :
//
//	config local syslog path tmp/log
func SyslogSetPathCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) < 5 {
		q.Q("error", len(ws))
		cmdinfo.Status = "error: invalid command"
		return cmdinfo
	} else if len(ws) > 5 {
		cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 5 but got %d", len(ws))
		return cmdinfo
	}
	var path string
	Unpack(ws[4:], &path)
	QC.SyslogLocalPath = path
	cmdinfo.Status = "ok"
	return cmdinfo
}

// Configure local syslog file maximum size.
//
// Usage : config local syslog maxsize [maxsize]
//
//	[maxsize]     : local syslog file maxsize size
//
// Example :
//
//	config local syslog maxsize 100
func SyslogSetMaxSizeCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) < 5 {
		q.Q("error", len(ws))
		cmdinfo.Status = "error: invalid command"
		return cmdinfo
	} else if len(ws) > 5 {
		cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 5 but got %d", len(ws))
		return cmdinfo
	}
	var size string
	Unpack(ws[4:], &size)
	s, err := strconv.Atoi(size)
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	QC.SyslogFileSize = uint(s)
	cmdinfo.Status = "ok"
	return cmdinfo
}

// Whether to configure local syslog files to be compressed
//
// Usage : config local syslog compress [compress]
//
//	[compress]     : would be compressed
//
// Example :
//
//	config local syslog compress true
func SyslogSetCompressCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) < 5 {
		q.Q("error", len(ws))
		cmdinfo.Status = "error: invalid command"
		return cmdinfo
	} else if len(ws) > 5 {
		cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 5 but got %d", len(ws))
		return cmdinfo
	}
	var enable string
	Unpack(ws[4:], &enable)
	boolValue, err := strconv.ParseBool(enable)
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	QC.SyslogCompress = boolValue
	cmdinfo.Status = "ok"
	return cmdinfo
}

const syslog_time_format = "2006/01/02 15:04:05"

// Read local syslog.
//
// Usage : config local syslog read [start date] [start time] [end date] [end time] [max line]
//
//	[start date]   : search syslog start date
//	[start time]   : search syslog start time
//	[end date]     : search syslog end date
//	[end time]     : search syslog end time
//	[max line]     : max lines, if without max line, that mean read all of lines
//
// Example :
//
//	config local syslog read 2023/02/21 22:06:00 2023/02/22 22:08:00
//	config local syslog read 5
//	config local syslog read 2023/02/21 22:06:00 2023/02/22 22:08:00 5
func ReadSyslogCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	maxline := -1
	ws := strings.Split(cmd, " ")
	if len(ws) < 4 {
		q.Q("error", len(ws))
		cmdinfo.Status = fmt.Sprintf("error: too few arguments, expected atleast 5 but got %d", len(ws))
		return cmdinfo
	}
	var start, end string
	if len(ws) == 8 || len(ws) == 9 {
		start = strings.Join(ws[4:6], " ")
		end = strings.Join(ws[6:8], " ")
		// if inlcude max line paramters
		if len(ws) == 9 {
			v, err := strconv.Atoi(ws[8])
			if err != nil {
				q.Q(err)
				cmdinfo.Status = "error: " + err.Error()
				return cmdinfo
			} else {
				maxline = v
				q.Q("read syslog max line=", maxline)
			}
		}
	}
	// if inlcude max line paramters
	if len(ws) == 5 {
		v, err := strconv.Atoi(ws[4])
		if err != nil {
			q.Q(err)
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		} else {
			maxline = v
			q.Q("read syslog max line=", maxline)
		}
	}

	files := []string{QC.SyslogLocalPath}
	logs, err := getSyslogsWithTime(files, start, end, maxline)
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	b, err := json.Marshal(&logs)
	if err != nil {
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	cmdinfo.Result = string(b)
	q.Q(cmdinfo.Result)
	cmdinfo.Status = "ok"
	return cmdinfo
}

/*
Configure local syslog remote server address.

Usage : config local syslog remote [server address]

	[server address] : remote syslog server address

Example :

	config local syslog remote :5514
	config local syslog remote ***************:5514
*/
func SyslogSetRemoteCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) < 5 {
		q.Q("error", len(ws))
		cmdinfo.Status = "error: invalid command"
		return cmdinfo
	} else if len(ws) > 5 {
		cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 5 but got %d", len(ws))
		return cmdinfo
	}
	var addr string
	Unpack(ws[4:], &addr)
	QC.RemoteSyslogServerAddr = addr
	err := InitRemoteSyslog()
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	cmdinfo.Status = "ok"
	return cmdinfo
}

/*
Configure local syslog files to be back after forward to remote syslog server.

Usage : config local syslog backup-after-forward [enable]

	[enable]     : enable back after forward to remote syslog server, true or false

Example :

	config local syslog backup-after-forward true
	config local syslog backup-after-forward false
*/
func SyslogSetBakAfterFwdCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) < 5 {
		q.Q("error", len(ws))
		cmdinfo.Status = "error: invalid command"
		return cmdinfo
	} else if len(ws) > 5 {
		cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 5 but got %d", len(ws))
		return cmdinfo
	}
	var enable string
	Unpack(ws[4:], &enable)
	boolValue, err := strconv.ParseBool(enable)
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	QC.SyslogBakAfterFwd = boolValue
	cmdinfo.Status = "ok"
	return cmdinfo
}

func parsingDataofSyslog(message string) (syslog.Base, time.Time, error) {
	p := rfc3164.NewParser(rfc3164.WithYear(rfc3164.CurrentYear{}))
	m, err := p.Parse([]byte(message))
	if err != nil {
		p = rfc5424.NewParser()
		m, err = p.Parse([]byte(message))
		if err != nil {
			q.Q(err)
			return syslog.Base{}, time.Time{}, err
		}
	}

	switch v := m.(type) {
	case *rfc5424.SyslogMessage:

		return v.Base, *v.Timestamp, nil
	case *rfc3164.SyslogMessage:
		return v.Base, *v.Timestamp, nil
	}

	return syslog.Base{}, time.Time{}, errors.New("not support type yet")
}

// compareTime compare time size with start time and end time
func compareTime(start, end, target string) (bool, error) {
	s, err := time.Parse(syslog_time_format, start)
	if err != nil {
		return false, err
	}
	e, err := time.Parse(syslog_time_format, end)
	if err != nil {
		return false, err
	}
	if e.Before(s) {
		return false, fmt.Errorf("end time: %v should than start time: %v ", end, s)
	}
	t, err := time.Parse(syslog_time_format, target)
	if err != nil {
		return false, err
	}

	if (s.Before(t) || s.Equal(t)) && (e.After(t) || e.Equal(t)) {
		return true, nil
	}

	return false, nil
}

// SendWebSocket send message to websocket
func SendWebSocket(msg *WebSocketMessage) {
	if !QC.IsRoot {
		return
	}
	QC.WebSocketMessageBroadcast <- *msg
}

// SendSocketMessageWithoutCheck send message to websocket without check
func SendSocketMessageWithoutCheck(severity int, bufStr string) {
	wsMessage := WebSocketMessage{
		Kind:    "nimbl_syslog",
		Level:   1,
		Message: strings.TrimSpace(bufStr),
	}
	QC.WebSocketMessageBroadcast <- wsMessage
	q.Q("forward to ws", wsMessage)
}

func SendSocketMessage(severity int, bufStr string) {
	if !QC.IsRoot {
		return
	}
	if severity < 2 {
		ix := strings.Index(bufStr, ">")
		wsMessage := WebSocketMessage{
			Kind:    "nimbl_syslog",
			Level:   severity,
			Message: strings.TrimSpace(bufStr[ix+1:]),
		}
		QC.WebSocketMessageBroadcast <- wsMessage
		q.Q("forward to ws", wsMessage)
	}
}

// set syslog file path where log will be stored.
//
// Usage : syslog config path [path]
//
//	[path]        : syslog file path
//
// Example :
//
//	syslog config path tmp/log
func SyslogConfigPathCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Fields(cmd)
	if len(ws) < 4 {
		q.Q("error", len(ws))
		cmdinfo.Status = "error: invalid command"
		return cmdinfo
	} else if len(ws) > 4 {
		cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 4 but got %d", len(ws))
		return cmdinfo
	}
	path := ws[3]
	QC.SyslogLocalPath = path
	cmdinfo.Status = "ok"
	return cmdinfo
}

// set syslog files maximum size.
//
// Usage : syslog config maxsize [maxsize]
//
//	[maxsize]     : syslog file maxsize size in MB
//
// Example :
//
//	syslog config maxsize 100
func SyslogConfigMaxSizeCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Fields(cmd)
	if len(ws) < 4 {
		q.Q("error", len(ws))
		cmdinfo.Status = "error: invalid command"
		return cmdinfo
	} else if len(ws) > 4 {
		cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 4 but got %d", len(ws))
		return cmdinfo
	}
	size := ws[3]
	s, err := strconv.Atoi(size)
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	QC.SyslogFileSize = uint(s)
	cmdinfo.Status = "ok"
	return cmdinfo
}

// set syslog files to be compressed or not
//
// Usage : syslog config compress [compress]
//
//	[compress]     : enable compress file of backup syslog
//
// Example :
//
//	syslog config compress true
func SyslogConfigCompressCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Fields(cmd)
	if len(ws) < 4 {
		q.Q("error", len(ws))
		cmdinfo.Status = "error: invalid command"
		return cmdinfo
	} else if len(ws) > 4 {
		cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 4 but got %d", len(ws))
		return cmdinfo
	}
	enabled := ws[3]
	boolValue, err := strconv.ParseBool(enabled)
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	QC.SyslogCompress = boolValue
	cmdinfo.Status = "ok"
	return cmdinfo
}

// export log to storage, return the file url
// if storage is bblogsvc, that means local, otherwise remote storage url
func ExportLogToStorage(logs []syslog.Base, storage string) (string, error) {
	filename := fmt.Sprintf("syslog_%v.log", time.Now().Unix())
	fileDir, err := EnsureStaticFilesFolderExist()
	if err != nil {
		q.Q(err)
		return "", err
	}
	fileDir = path.Join(fileDir, "syslogs")
	err = os.MkdirAll(fileDir, 0o755)
	if err != nil {
		q.Q(err)
		return "", err
	}
	filePath := path.Join(fileDir, filename)
	f, err := os.Create(filePath)
	if err != nil {
		q.Q(err)
		return "", err
	}
	defer f.Close()
	for _, log := range logs {
		if !log.Valid() {
			continue
		}
		p := *log.Priority
		t := log.Timestamp.Format(time.Stamp)
		h := *log.Hostname
		a := ""
		if log.Appname != nil {
			a = *log.Appname
		}
		m := ""
		if log.Message != nil {
			m = *log.Message
		}
		var msg string
		if a == "" {
			msg = fmt.Sprintf("<%d>%s %s %s", p, t, h, m)
		} else {
			msg = fmt.Sprintf("<%d>%s %s %s: %s", p, t, h, a, m)
		}
		_, err := f.WriteString(msg)
		if err != nil {
			q.Q(err)
			return "", err
		}
		// add new line if not logs end
		if logs[len(logs)-1] != log {
			_, err := f.WriteString("\n")
			if err != nil {
				q.Q(err)
				return "", err
			}
		}

		// check if the file size is over 10MB
		fileInfo, err := f.Stat()
		if err != nil {
			q.Q(err)
			return "", err
		}
		if fileInfo.Size() > 10*1024*1024 {
			break
		}
	}

	if storage == "bblogsvc" {
		return QC.NmsServiceURL + "/api/v1/files/syslogs/" + filename, nil
	} else {
		// TODO: upload to remote storage and return the url
		return "", fmt.Errorf("not support remote storage yet")
	}
}

// getSyslogFiles get all syslog files' name in the same directory
func getSyslogFiles() ([]string, error) {
	fileName := filepath.Base(QC.SyslogLocalPath)
	fileNamePrefix := strings.TrimSuffix(fileName, filepath.Ext(fileName))
	fileNamePostfix := strings.TrimPrefix(fileName, fileNamePrefix)

	files := []string{}
	// get all files in the same directory
	fileDir := filepath.Dir(QC.SyslogLocalPath)
	fileEntries, err := os.ReadDir(fileDir)
	if err != nil {
		q.Q(err)
		return files, err
	}
	for _, file := range fileEntries {
		if file.IsDir() || strings.HasSuffix(file.Name(), ".gz") {
			continue
		}

		prefix := strings.TrimSuffix(file.Name(), filepath.Ext(file.Name()))
		postfix := strings.TrimPrefix(file.Name(), prefix)

		// Check if the file name matches the prefix and postfix
		if (file.Name() == fileName) || (strings.HasPrefix(prefix, fileNamePrefix) && postfix == fileNamePostfix) {
			files = append(files, file.Name())
		}
	}
	return files, nil
}

// sortSyslogFileByModTime sort syslog files by modified time and order
func sortSyslogFileByModTime(files []string, direction string) ([]string, error) {
	type fileTime struct {
		name string
		time time.Time
	}
	fts := []fileTime{}
	for _, file := range files {
		f, err := os.Open(file)
		if err != nil {
			return nil, err
		}
		defer f.Close()
		fi, err := f.Stat()
		if err != nil {
			return nil, err
		}
		fts = append(fts, fileTime{name: file, time: fi.ModTime()})
	}
	files = []string{}
	if direction == "asc" || direction == "" || direction == "ascending" {
		sort.Slice(fts, func(i, j int) bool {
			return fts[i].time.Before(fts[j].time)
		})
	} else if direction == "desc" || direction == "descending" {
		sort.Slice(fts, func(i, j int) bool {
			return fts[i].time.After(fts[j].time)
		})
	} else {
		return nil, fmt.Errorf("invalid direction:%v", direction)
	}
	for _, ft := range fts {
		files = append(files, ft.name)
	}

	return files, nil
}

// getSyslogsWithTime get syslog with time from files,
// if start and end is empty, that means no compare time
// if number is -1, that means all
func getSyslogsWithTime(files []string, start, end string, number int) ([]syslog.Base, error) {
	return getSyslogWithQuery(files, SyslogQuery{
		StartTime: start,
		EndTime:   end,
		MaxLine:   number,
	})
}

func getSyslogWithQuery(files []string, query SyslogQuery) ([]syslog.Base, error) {
	q.Q(query.StartTime, query.EndTime, query.MaxLine, query.Filter)
	logs := []syslog.Base{}
	if query.MaxLine < -1 {
		return logs, fmt.Errorf("invalid number: %v", query.MaxLine)
	}
	if query.MaxLine == 0 {
		return logs, nil
	}

	if query.Page < 1 {
		q.Q("page is less than 1, set to 1")
		query.Page = 1
	}

	// sort files by modified time
	files, err := sortSyslogFileByModTime(files, "asc")
	if err != nil {
		return nil, err
	}

	for _, file := range files {
		q.Q("read log", file)
		var err error
		logs, err = processFile(file, query, logs)
		if err != nil {
			return nil, err
		}
	}

	if query.MaxLine == -1 {
		query.MaxLine = len(logs)
	}

	// Apply pagination: calculate offset and slice.
	if query.Page > 0 {
		offset := (query.Page - 1) * query.MaxLine
		if offset < len(logs) {
			end := offset + query.MaxLine
			if end > len(logs) {
				end = len(logs)
			}
			logs = logs[offset:end]
		} else {
			// No logs for the requested page
			logs = []syslog.Base{}
		}
	}

	return logs, nil
}

func processFile(file string, query SyslogQuery, logs []syslog.Base) ([]syslog.Base, error) {
	readFile, err := os.Open(file)
	if err != nil {
		return nil, err
	}
	defer readFile.Close()

	fileStatus, err := readFile.Stat()
	if err != nil {
		return nil, err
	}

	filtertime := false
	if query.StartTime != "" && query.EndTime != "" {
		filtertime = true
	}

	// Calculate total lines needed for pagination: page size * page number.
	totalNeeded := query.MaxLine
	if query.Page > 0 {
		totalNeeded = query.MaxLine * query.Page
	}

	scanner := backscanner.New(readFile, int(fileStatus.Size()))
	for {
		if query.MaxLine != -1 && len(logs) >= totalNeeded {
			break
		}

		line, _, err := scanner.LineBytes()
		if err != nil {
			if err == io.EOF {
				return logs, nil
			}
			return nil, err
		}

		b, t, err := parsingDataofSyslog(string(line[:]))
		if err != nil {
			continue
		}
		if filtertime {
			r, _ := compareTime(query.StartTime, query.EndTime, t.Format(syslog_time_format))
			if !r {
				continue
			}
		}
		if query.Filter != "" {
			if !strings.Contains(string(line[:]), query.Filter) {
				continue
			}
		}

		logs = append(logs, b)
	}

	return logs, nil
}

func getSyslogsWithLast(files []string, last int) ([]syslog.Base, error) {
	logs := []syslog.Base{}
	if last < -1 {
		return logs, fmt.Errorf("invalid number: %v", last)
	}
	if last == 0 {
		return logs, nil
	}

	// sort files by modified time
	files, err := sortSyslogFileByModTime(files, "desc")
	if err != nil {
		return nil, err
	}
	q.Q(files)

	for _, file := range files {
		q.Q("read log", file)
		var err error
		logs, err = processFileTail(file, last, logs)
		if err != nil {
			return nil, err
		}
		if last != -1 && len(logs) >= last {
			break
		}
	}

	// reverse logs to make it in order
	for i := len(logs)/2 - 1; i >= 0; i-- {
		opp := len(logs) - 1 - i
		logs[i], logs[opp] = logs[opp], logs[i]
	}

	return logs, nil
}

func processFileTail(file string, number int, logs []syslog.Base) ([]syslog.Base, error) {
	readFile, err := os.Open(file)
	if err != nil {
		return nil, err
	}
	defer readFile.Close()

	line := ""
	var cursor int64 = 0
	stat, _ := readFile.Stat()
	filesize := stat.Size()
	for {
		cursor -= 1
		readFile.Seek(cursor, io.SeekEnd)

		char := make([]byte, 1)
		readFile.Read(char)

		if cursor != -1 && (char[0] == 10 || char[0] == 13) { // stop if we find a line
			if line != "" {
				// lines = append([]string{line}, lines...)
				b, _, err := parsingDataofSyslog(line)
				if err != nil {
					continue
				}
				logs = append(logs, b)
				line = ""
			}
		} else {
			line = fmt.Sprintf("%s%s", string(char), line) // there is more efficient way
		}

		if cursor == -filesize { // stop if we are at the beginning
			if line != "" {
				// lines = append([]string{line}, lines...)
				b, _, err := parsingDataofSyslog(line)
				if err == nil {
					logs = append(logs, b)
				}
			}
			break
		}
		if len(logs) >= number {
			break
		}
	}

	return logs, nil
}

/*
SyslogListLogFilesCmd list log files in the syslog path, result is a json array of file names.
Note that the compressed files are not listed

Usage : syslog list
*/
func SyslogListLogFilesCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Fields(cmd)
	if len(ws) < 2 {
		q.Q("error", len(ws))
		cmdinfo.Status = "error: invalid command"
		return cmdinfo
	} else if len(ws) > 2 {
		cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 2 but got %d", len(ws))
		return cmdinfo
	}

	// list log files
	files, err := getSyslogFiles()
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}

	cmdinfo.Status = "ok"
	// convert to json and set to CmdInfo.Result
	b, err := json.Marshal(files)
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: " + err.Error()
	}
	cmdinfo.Result = string(b)
	return cmdinfo
}

/*
SyslogGetURLCmd get the URL of the log file to download according to the source file and spec. Result is the URL of the log file to download.
The new log file is generated by the log file in the syslog path according to the spec and stored in the file server directory.
Note that the compressed files are not listed and the file size is limited to 10MB.

Usage : syslog geturl [source filename] [spec]

	[source filename]     : Log file name in the syslog path, can be listed by syslog list, or use all to query all log files
	[spec]                : Spec of log file, can be start date, start time, end date, end time, max line, if without max line,
							that mean read all of lines.
							Or use with last to query the last n lines of the log file

Example :

	syslog geturl syslog.log
	syslog geturl syslog.log 5
	syslog geturl syslog.log 2023/02/21 22:06:00 2023/02/22 22:08:00
	syslog geturl syslog.log 2023/02/21 22:06:00 2023/02/22 22:08:00 5
	syslog geturl all 2023/02/21 22:06:00 2023/02/22 22:08:00
	syslog geturl all 2023/02/21 22:06:00 2023/02/22 22:08:00 100
	syslog geturl syslog.log last 5
*/
func SyslogGetUrlCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Fields(cmd)
	if len(ws) < 3 || len(ws) == 6 {
		q.Q("error", len(ws))
		cmdinfo.Status = "error: invalid command"
		return cmdinfo
	}

	var files []string
	if ws[2] == "all" {
		allfiles, err := getSyslogFiles()
		if err != nil {
			q.Q(err)
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		for _, file := range allfiles {
			fileDir := filepath.Dir(QC.SyslogLocalPath)
			files = append(files, path.Join(fileDir, file))
		}
	} else {
		files = append(files, ws[2])
	}
	q.Q(files)

	logs := []syslog.Base{}
	if len(ws) > 4 && ws[3] == "last" {
		v, err := strconv.Atoi(ws[4])
		if err != nil {
			q.Q(err)
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		logs, err = getSyslogsWithLast(files, v)
		if err != nil {
			q.Q(err)
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
	} else {
		maxline := -1
		var start, end string
		if len(ws) == 7 || len(ws) == 8 {
			start = strings.Join(ws[3:5], " ")
			end = strings.Join(ws[5:7], " ")
			if len(ws) == 8 {
				v, err := strconv.Atoi(ws[7])
				if err != nil {
					q.Q(err)
					cmdinfo.Status = "error: " + err.Error()
					return cmdinfo
				} else {
					maxline = v
				}
			}
		}
		if len(ws) == 4 {
			v, err := strconv.Atoi(ws[3])
			if err != nil {
				q.Q(err)
				cmdinfo.Status = "error: " + err.Error()
				return cmdinfo
			} else {
				maxline = v
			}
		}

		var err error
		logs, err = getSyslogsWithTime(files, start, end, maxline)
		if err != nil {
			q.Q(err)
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
	}
	fileUrl, err := ExportLogToStorage(logs, "bblogsvc")
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: " + err.Error()
	}

	cmdinfo.Status = "ok"
	cmdinfo.Result = fileUrl
	return cmdinfo
}

/*
SyslogGeneratedListCmd get list of log files generated by geturl command, result is a json array
*/
func SyslogGeneratedListCmd(cmdinfo *CmdInfo) *CmdInfo {
	// list all files in /files/syslogs
	fileDir, err := EnsureStaticFilesFolderExist()
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	fileDir = path.Join(fileDir, "syslogs")
	fileEntries, err := os.ReadDir(fileDir)
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	files := []string{}
	for _, file := range fileEntries {
		if file.IsDir() {
			continue
		}
		files = append(files, file.Name())
	}
	b, err := json.Marshal(&files)
	if err != nil {
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	cmdinfo.Result = string(b)
	cmdinfo.Status = "ok"
	return cmdinfo
}

/*
SyslogGeneratedRemoveCmd remove the log file generated by geturl command.
*/
func SyslogGeneratedRemoveCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Fields(cmd)
	if len(ws) < 4 {
		q.Q("error", len(ws))
		cmdinfo.Status = "error: invalid command"
		return cmdinfo
	}
	fileDir, err := EnsureStaticFilesFolderExist()
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	fileDir = path.Join(fileDir, "syslogs")

	filename := ws[3]
	if filename == "all" {
		fileEntries, err := os.ReadDir(fileDir)
		if err != nil {
			q.Q(err)
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		for _, file := range fileEntries {
			err = os.Remove(path.Join(fileDir, file.Name()))
			if err != nil {
				q.Q(err)
				cmdinfo.Status = "error: " + err.Error()
				return cmdinfo
			}
		}
	}

	err = os.Remove(path.Join(fileDir, filename))
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	cmdinfo.Status = "ok"
	return cmdinfo
}

/*
SyslogConfigRemoteCmd

Usage : syslog config remote [server address]

	[server address] : remote syslog server address

Example :

	syslog config remote :5514
	syslog config remote www.example.com:5514
*/
func SyslogConfigRemoteCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Fields(cmd)
	if len(ws) < 4 {
		q.Q("error", len(ws))
		cmdinfo.Status = "error: invalid command"
		return cmdinfo
	} else if len(ws) > 4 {
		cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 4 but got %d", len(ws))
		return cmdinfo
	}
	addr := ws[3]
	QC.RemoteSyslogServerAddr = addr
	err := InitRemoteSyslog()
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	cmdinfo.Status = "ok"
	return cmdinfo
}

/*
SyslogConfigBakAfterFwdCmd

Usage : syslog config backup-after-forward [enable]

	[enable]     : enable back after forward to remote syslog server

Example :

	syslog config backup-after-forward true
*/
func SyslogConfigBakAfterFwdCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Fields(cmd)
	if len(ws) < 4 {
		q.Q("error", len(ws))
		cmdinfo.Status = "error: invalid command"
		return cmdinfo
	} else if len(ws) > 4 {
		cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 4 but got %d", len(ws))
		return cmdinfo
	}
	enabled := ws[3]
	boolValue, err := strconv.ParseBool(enabled)
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	QC.SyslogBakAfterFwd = boolValue
	cmdinfo.Status = "ok"
	return cmdinfo
}

/*
SyslogConfigSeverityRangeForwardCmd

Usage : syslog config severity-range-forward [min severity] [max severity]

	[min severity] : min severity to forward to remote syslog server
	[max severity] : max severity to forward to remote syslog server

Example :

	syslog config severity-range-forward 0 1 // send emergency and alert
	syslog config severity-range-forward -1 5 // send all except notice and debug
	syslog config severity-range-forward -1 7 // send all
	syslog config severity-range-forward -1 -1 // send nothing
*/
func SyslogConfigSeverityRangeForwardCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Fields(cmd)
	if len(ws) < 5 {
		q.Q("error", len(ws))
		cmdinfo.Status = "error: invalid command"
		return cmdinfo
	} else if len(ws) > 5 {
		cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 5 but got %d", len(ws))
		return cmdinfo
	}
	min, err := strconv.Atoi(ws[3])
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	max, err := strconv.Atoi(ws[4])
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	if min > max {
		cmdinfo.Status = "error: min severity should less than max severity"
		return cmdinfo
	}

	QC.SyslogSeverityRngFwd.MaxSeverity = max
	QC.SyslogSeverityRngFwd.MinSeverity = min
	cmdinfo.Status = "ok"
	return cmdinfo
}

func IntroduceSyslog() error {
	err := SendSyslog(LOG_INFO, "bblogsvc", "log service is forwarding")
	if err != nil {
		q.Q(err)
		return err
	}
	return nil
}

/*
SyslogIntroduceCmd syslog service sends introduction message to the remote syslog server immediately
*/
func SyslogIntroduceCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Fields(cmd)

	if len(ws) < 2 {
		q.Q("error", len(ws))
		cmdinfo.Status = "error: invalid command"
		return cmdinfo
	}

	err := IntroduceSyslog()
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}

	cmdinfo.Status = "ok"
	return cmdinfo
}

/*
SyslogConfigIntroduceIntervalCmd set the interval of sending introduction message to the remote syslog server
*/
func SyslogConfigIntroduceIntervalCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Fields(cmd)

	if len(ws) < 4 {
		q.Q("error", len(ws))
		cmdinfo.Status = "error: invalid command"
		return cmdinfo
	}

	interval, err := strconv.Atoi(ws[3])
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}

	if interval <= 0 {
		cmdinfo.Status = "error: interval should be greater than 0"
		return cmdinfo
	}

	QC.SyslogIntroduceInterval = interval
	cmdinfo.Status = "ok"
	return cmdinfo
}

/*
SyslogConfigGetCmd get the current configuration of syslog

	RemoteSyslogServerAddr    string                             `json:"remote_syslog_server_addr"`
	SyslogLocalPath           string                             `json:"syslog_local_path"`
	SyslogFileSize            uint                               `json:"syslog_file_size"`
	SyslogCompress            bool                               `json:"syslog_compress"`
	SyslogBakAfterFwd         bool                               `json:"syslog_keep_copy"`
	SyslogSeverityRngFwd      SyslogSeverityRange                `json:"syslog_severity_rng_fwd"`
*/
func SyslogConfigGetCmd(cmdinfo *CmdInfo) *CmdInfo {
	type Config struct {
		SyslogLocalPath      string              `json:"syslog_local_path"`
		SyslogFileSize       uint                `json:"syslog_file_size"`
		SyslogCompress       bool                `json:"syslog_compress"`
		RemoteSyslogServer   string              `json:"remote_syslog_server"`
		SyslogBakAfterFwd    bool                `json:"syslog_keep_copy"`
		SyslogSeverityRngFwd SyslogSeverityRange `json:"syslog_severity_rng_fwd"`
	}

	config := Config{
		SyslogLocalPath:      QC.SyslogLocalPath,
		SyslogFileSize:       QC.SyslogFileSize,
		SyslogCompress:       QC.SyslogCompress,
		RemoteSyslogServer:   QC.RemoteSyslogServerAddr,
		SyslogBakAfterFwd:    QC.SyslogBakAfterFwd,
		SyslogSeverityRngFwd: QC.SyslogSeverityRngFwd,
	}

	b, err := json.Marshal(&config)
	if err != nil {
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}

	cmdinfo.Result = string(b)
	cmdinfo.Status = "ok"
	return cmdinfo
}

// CheckServerPortValid validates that the server port is within the valid range (1-65535).
func CheckServerPortValidity(serverport string) error {
	// Convert serverPort to integer
	port, err := strconv.Atoi(serverport)
	if err != nil {
		return fmt.Errorf("invalid server port value: %v", err)
	}
	if port < 1 || port > 65535 {
		return fmt.Errorf("invalid server port: %d, port must be between 1 and 65535", port)
	}
	return nil
}

// CheckLoglevelValidity validates that the log level is an integer between 0 and 7.
func CheckSyslogLoglevelValidity(loglevel string) error {
	// Convert serverLevel to integer
	level, err := strconv.Atoi(loglevel)
	if err != nil {
		return fmt.Errorf("invalid log level value: %v", err)
	}
	if level < 0 || level > 7 {
		return fmt.Errorf("invalid log level: must be between 0 and 7")
	}
	return nil
}
