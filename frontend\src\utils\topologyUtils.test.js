import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import {
  getSavedPositions,
  filteredTopologyData,
  getFilename,
  createManualTpologyData,
  createEdge,
} from "./topologyUtils";

describe("topologyUtils", () => {
  // Mock localStorage
  const localStorageMock = (() => {
    let store = {};
    return {
      getItem: vi.fn((key) => store[key] || null),
      setItem: vi.fn((key, value) => {
        store[key] = value.toString();
      }),
      clear: vi.fn(() => {
        store = {};
      }),
    };
  })();

  beforeEach(() => {
    // Setup localStorage mock
    Object.defineProperty(window, "localStorage", {
      value: localStorageMock,
      writable: true,
    });

    // Reset console.error mock
    vi.spyOn(console, "error").mockImplementation(() => {});

    // Clear localStorage
    localStorageMock.clear();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("getSavedPositions", () => {
    it("should return empty object when no saved positions exist", () => {
      // Act
      const result = getSavedPositions();

      // Assert
      expect(result).toEqual({});
      expect(localStorageMock.getItem).toHaveBeenCalledWith(
        "nimbl-topologyPositions"
      );
    });

    it("should return parsed positions when saved positions exist", () => {
      // Arrange
      const savedPositions = {
        service1: [
          { id: "node1", style: { x: 100, y: 200 } },
          { id: "node2", style: { x: 300, y: 400 } },
        ],
      };
      localStorageMock.getItem.mockReturnValue(JSON.stringify(savedPositions));

      // Act
      const result = getSavedPositions();

      // Assert
      expect(result).toEqual(savedPositions);
    });

    it("should handle JSON parse errors", () => {
      // Arrange
      localStorageMock.getItem.mockReturnValue("invalid-json");

      // Act
      const result = getSavedPositions();

      // Assert
      expect(result).toEqual({});
      expect(console.error).toHaveBeenCalledWith(
        "Failed to retrieve saved positions:",
        expect.any(Error)
      );
    });
  });

  describe("filteredTopologyData", () => {
    it("should return empty nodes and edges when data is null", () => {
      // Act
      const result = filteredTopologyData(null, "service1");

      // Assert
      expect(result).toEqual({ nodes: [], edges: [] });
    });

    it("should return empty nodes and edges when data is empty array", () => {
      // Act
      const result = filteredTopologyData([], "service1");

      // Assert
      expect(result).toEqual({ nodes: [], edges: [] });
    });

    it("should process topology data with saved positions", () => {
      // Arrange
      const savedPositions = {
        service1: [{ id: "node1", style: { x: 100, y: 200 } }],
      };
      localStorageMock.getItem.mockReturnValue(JSON.stringify(savedPositions));

      const data = [
        {
          id: "node1",
          name: "Node 1",
          linkData: [
            {
              edge: "node1_node2",
              source: "node1",
              target: "node2",
              blockedPort: "false",
            },
          ],
        },
        { id: "node2", name: "Node 2", linkData: [] },
      ];

      // Act
      const result = filteredTopologyData(data, "service1");

      // Assert
      expect(result.nodes).toHaveLength(2);
      expect(result.edges).toHaveLength(1);

      // Check that saved position was applied
      const node1 = result.nodes.find((n) => n.id === "node1");
      expect(node1).toHaveProperty("style.x", 100);
      expect(node1).toHaveProperty("style.y", 200);
      expect(node1).toHaveProperty("macAddress", "node1");
    });

    it("should handle devices without linkData", () => {
      // Arrange
      const data = [
        { id: "node1", name: "Node 1" }, // No linkData
        { id: "node2", name: "Node 2", linkData: [] },
      ];

      // Act
      const result = filteredTopologyData(data, "service1");

      // Assert
      expect(result.nodes).toHaveLength(2);
      expect(result.edges).toHaveLength(0);
    });

    it("should deduplicate links and prioritize blocked ports", () => {
      // Arrange
      const data = [
        {
          id: "node1",
          name: "Node 1",
          linkData: [
            {
              edge: "node1_node2",
              source: "node1",
              target: "node2",
              blockedPort: "false",
            },
          ],
        },
        {
          id: "node2",
          name: "Node 2",
          linkData: [
            {
              edge: "node1_node2",
              source: "node2",
              target: "node1",
              blockedPort: "true",
            }, // Same edge, but blocked
          ],
        },
      ];

      // Act
      const result = filteredTopologyData(data, "service1");

      // Assert
      expect(result.nodes).toHaveLength(2);
      expect(result.edges).toHaveLength(1);

      // Check that the blocked port version was kept
      expect(result.edges[0].blockedPort).toBe("true");
    });

    it("should filter out links to non-existent nodes", () => {
      // Arrange
      const data = [
        {
          id: "node1",
          name: "Node 1",
          linkData: [
            {
              edge: "node1_node2",
              source: "node1",
              target: "node2",
              blockedPort: "false",
            },
            {
              edge: "node1_node3",
              source: "node1",
              target: "node3",
              blockedPort: "false",
            }, // node3 doesn't exist
          ],
        },
        { id: "node2", name: "Node 2", linkData: [] },
      ];

      // Act
      const result = filteredTopologyData(data, "service1");

      // Assert
      expect(result.nodes).toHaveLength(2);
      expect(result.edges).toHaveLength(1);
      expect(result.edges[0].edge).toBe("node1_node2");
    });

    it("should handle errors during processing", () => {
      // Arrange
      const data = {}; // Invalid data format to cause error

      // Mock console.error to throw an error
      console.error.mockImplementation(() => {
        throw new Error("Mocked error");
      });

      // Act
      const result = filteredTopologyData(data, "service1");

      // Assert
      expect(result).toEqual({ nodes: [], edges: [] });
    });
  });

  describe("getFilename", () => {
    it("should generate filename with title and current date/time", () => {
      // Arrange
      const mockDate = new Date("2023-01-15T14:30:45");
      const originalDate = global.Date;
      global.Date = class extends Date {
        constructor() {
          return mockDate;
        }
        static now() {
          return mockDate.getTime();
        }
      };

      // Act
      const result = getFilename("Topology");

      // Assert
      expect(result).toBe("Topology_15-01-2023_14-30-45");

      // Restore Date
      global.Date = originalDate;
    });
  });

  describe("createManualTpologyData", () => {
    it("should create manual topology data from form values", () => {
      // Arrange
      const values = {
        mac: "00:11:22:33:44:55 ",
        linkData: [
          {
            target: "66:77:88:99:AA:BB ",
            sourcePort: "eth0 ",
            targetPort: "eth1 ",
          },
        ],
      };

      // Act
      const result = createManualTpologyData(values);

      // Assert
      expect(result).toEqual({
        id: "00:11:22:33:44:55",
        topoType: "manual",
        linkData: [
          {
            source: "00:11:22:33:44:55",
            target: "66:77:88:99:AA:BB",
            sourcePort: "eth0",
            targetPort: "eth1",
            blockedPort: "false",
            linkType: "manual",
            edge: "00:11:22:33:44:55_66:77:88:99:AA:BB",
          },
        ],
      });
    });
  });

  describe("createEdge", () => {
    it("should create edge ID with source first when source < target", () => {
      // Act
      const result = createEdge("A", "B");

      // Assert
      expect(result).toBe("A_B");
    });

    it("should create edge ID with target first when target < source", () => {
      // Act
      const result = createEdge("B", "A");

      // Assert
      expect(result).toBe("A_B");
    });
  });
});
