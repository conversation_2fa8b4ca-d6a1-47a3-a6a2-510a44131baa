package node

import (
	"context"
	"encoding/json"
	"fmt"
	"mnms/llm"
	"mnms/llm/history"
)

type RunNodeOutput struct {
	Format      string      `json:"format"` // "text" or "json"
	Data        interface{} `json:"data"`
	FlowControl string      `json:"flow-control"`
}

// String
func (r *RunNodeOutput) String() string {
	if r.Data == nil {
		return ""
	}
	switch r.Format {
	case "text":
		text, ok := r.Data.(string)
		if !ok {
			return fmt.Sprintf("%v type should be string but %T", r.Data, r.Data)
		}
		return text
	case "json":
		// check Data type
		switch r.Data.(type) {
		case string:
			return r.Data.(string)
		case []byte:
			return string(r.Data.([]byte))
		case map[string]any:
			jsonBytes, _ := json.Marshal(r.Data)
			return string(jsonBytes)
		}
		return ""

	default:
		return ""
	}
}

// Runner is an interface for a node that can run
type Runner interface {
	Run(ctx context.Context, llmClient llm.Completer, relevantDoc string, query string) (*RunNodeOutput, error)
	RunPrompt(
		ctx context.Context,
		provider llm.Provider,
		relevantDoc string,
		query string,
		messages *[]history.HistoryMessage,
		msgCallback func(kind, msg string),
		autoMode bool,
	) (string, error)
	ProcessToolCalls(ctx context.Context, approvedToolCalls []llm.ToolCall, rejectToolCalls []llm.ToolCall, callback func(kind, msg string)) ([]history.ContentBlock, []history.ContentBlock)
}

type NodeRelevantDocInfo struct {
	RelevantDocCategories []string `json:"relevantDocCategories"`
	NumberRelevantDocs    int      `json:"number_relevant_docs,omitempty"`
	RelevantSimilarity    float32  `json:"relevant_similarity,omitempty"`
}

// RelevantDocumentInfoer is an interface for a node that can provide relevant document information
type RelevantDocumentInfoer interface {
	GetRelevantDocumentInfo() NodeRelevantDocInfo
}

type NodeInfo struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
}

// Infoer is an interface for a node that can provide information
type Infoer interface {
	Info() NodeInfo
	Json() []byte
}

// MetadataManager is an interface for a node that can provide configuration
type MetadataManager interface {
	SetMeta(settings map[string]any) error
	GetMeta() map[string]any
}

// NodeProvider is an interface for a node that can provide all the above
type NodeProvider interface {
	Runner
	Infoer
	MetadataManager
	RelevantDocumentInfoer
	RunFunctions() []string
	Output(input string, output RunNodeOutput) RunNodeOutput
}

type Position struct {
	X float32 `json:"x"`
	Y float32 `json:"y"`
}
