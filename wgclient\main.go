package main

import (
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/qeof/q"
)

type Config struct {
	Name                string   `json:"name"`
	Interface           string   `json:"interface"`
	RootUrl             string   `json:"root_url"`
	RootName            string   `json:"root_name"`
	ClientName          string   `json:"client_name"`
	Address             string   `json:"address"`
	DNS                 []string `json:"dns"`
	Endpoint            string   `json:"endpoint"`
	PersistentKeepalive uint16   `json:"persistent_keepalive"`
	PreUp               []string `json:"pre_up"`
	PostUp              []string `json:"post_up"`
	PreDown             []string `json:"pre_down"`
	PostDown            []string `json:"post_down"`
}

func main() {
	// flags
	config := flag.String("config", "", "wgclient config file")
	flag.Parse()
	// args
	args := flag.Args()
	CheckArgs(args)
	cmd := strings.Join(args, " ")

	if len(*config) == 0 {
		fmt.Println("config file is required")
		os.Exit(1)
	}

	// read from config file
	var c Config
	jsonbytes, err := os.ReadFile(*config)
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
	err = json.Unmarshal(jsonbytes, &c)
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
	// default values
	if len(c.Name) == 0 {
		c.Name = "wgclient"
	}
	if len(c.Interface) == 0 {
		c.Interface = "wg0"
	}
	// pretty print
	jsonbytes, err = json.MarshalIndent(c, "", "  ")
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
	fmt.Println(string(jsonbytes))

	switch cmd {
	case "start":
		// check if already started
		status, _ := WgStatus(c.Interface)
		if len(status.Interface) > 0 {
			fmt.Println("already started")
			os.Exit(1)
		}
		// check if wireguard config file exists
		_, err = os.Stat(c.Interface + ".conf")
		if err == nil {
			fmt.Println("wireguard config file already exists")
			fmt.Println("run wireguard with this config, if you want to renew config, run 'wgclient -config wgclient.json clean' first")
			err = WgStart(c.Interface)
			if err != nil {
				fmt.Println(err)
			}
			os.Exit(1)
		}
		// mnms login
		fmt.Println("auto joining mnms wireguard network...")
		token, err := GetToken("admin")
		if err != nil {
			fmt.Println(err)
			os.Exit(1)
		}
		// generate wireguard config
		wgc := NewWgConfig()
		wgi := WgInfo{
			Enabled: true,
			Name:    c.Interface,
			Config:  *wgc,
		}
		autoFillWgConfig(&wgi, c, token)

		// notify mnms, block until mnms is ready
		notifyNms(&wgi, c, token)

		// write wireguard config to file if above successed
		wgi.Save(c.Interface)

		// start wireguard
		err = WgStart(c.Interface)
		if err != nil {
			fmt.Println(err)
			os.Exit(1)
		}
		fmt.Println("wireguard started")
	case "stop":
		// stop wireguard
		err = WgStop(c.Interface)
		if err != nil {
			fmt.Println(err)
			os.Exit(1)
		}
		fmt.Println("wireguard stopped")
	case "clean":
		// stop wireguard
		_ = WgStop(c.Interface)
		// delete wireguard config file
		err = os.Remove(c.Interface + ".conf")
		if err != nil {
			fmt.Println("remove wireguard config file failed", err)
			os.Exit(1)
		}
		fmt.Println("config cleaned")
	default:
		fmt.Println("unknown command")
		HelpCmd()
		os.Exit(1)
	}
}

func insertcmd(cmd string, cmdinfo *map[string]CmdInfo) {
	ci := CmdInfo{
		Timestamp: time.Now().Format(time.RFC3339),
		Command:   cmd,
	}
	(*cmdinfo)[cmd] = ci
}

func insertclientcmd(cmd, client string, cmdinfo *map[string]CmdInfo) {
	if len(client) == 0 {
		insertcmd(cmd, cmdinfo)
		return
	}

	kcmd := "@" + client + " " + cmd
	ci := CmdInfo{
		Timestamp: time.Now().Format(time.RFC3339),
		Command:   cmd,
		Client:    client,
	}
	(*cmdinfo)[kcmd] = ci
}

func autoFillWgConfig(wgi *WgInfo, c Config, token string) {
	// get wg config from mnms /api/v1/wg
	if len(c.RootName) == 0 {
		fmt.Println("mnms root name is required")
		os.Exit(1)
	}
	resp, err := GetWithToken(c.RootUrl+"/api/v1/wg", token)
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
	wgData := make(map[string]WgInfo)
	err = json.NewDecoder(resp.Body).Decode(&wgData)
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
	resp.Body.Close()
	rootwg, ok := wgData[c.RootName]
	if !ok {
		fmt.Println("root wg not found")
		os.Exit(1)
	}
	if !rootwg.Enabled || len(rootwg.Status.PublicKey) == 0 {
		fmt.Println("root wg is not enabled")
		os.Exit(1)
	}
	clientwg, ok := wgData[c.ClientName]
	if !ok {
		fmt.Println("client wg not found")
		os.Exit(1)
	}
	if !clientwg.Enabled || len(clientwg.Status.PublicKey) == 0 {
		fmt.Println("client wg is not enabled")
		os.Exit(1)
	}

	wgi.Config.Interface.Addresses = []string{c.Address}
	if len(c.Address) == 0 {
		rootcidrs := rootwg.Config.Interface.Addresses
		clientip := clientwg.Config.Interface.Addresses[0]
		ip, ipnet, err := net.ParseCIDR(clientip)
		if err != nil {
			fmt.Println(err)
			os.Exit(1)
		}

		q.Q(ip, ipnet)
		for {
			// increment ip
			ip = ip.To4()
			ip = ip.Mask(ipnet.Mask)
			ip[3]++
			fmt.Println("trying ip", ip.String())

			// check if ip is in root cidrs
			valid := false
			for _, cidr := range rootcidrs {
				_, rootnet, err := net.ParseCIDR(cidr)
				if err != nil {
					fmt.Println(err)
					os.Exit(1)
				}
				if rootnet.Contains(ip) {
					valid = true
					ipnet = rootnet
					break
				}
			}
			if !valid {
				fmt.Println("ip not in root cidrs")
				os.Exit(1)
			}

			used := false
			// check if ip is used in root wg interface
			for _, addr := range rootwg.Config.Interface.Addresses {
				if addr == ip.String() {
					used = true
					break
				}
			}
			if used {
				fmt.Println("ip used in root wg interface")
				continue
			}
			// check if ip is used in root wg config peers
			for _, peer := range rootwg.Config.Peers {
				if peer.PublicKey == wgi.Config.Interface.PublicKey {
					// self peer, assign ip and exit
					allowedip := peer.AllowedIPs[0]
					registeredip, _, err := net.ParseCIDR(allowedip)
					if err != nil {
						fmt.Println(err)
						os.Exit(1)
					}
					ip = registeredip
					used = false
					break
				}
				for _, ipnet := range peer.AllowedIPs {
					_, rootnet, err := net.ParseCIDR(ipnet)
					if err != nil {
						fmt.Println(err)
						os.Exit(1)
					}
					if rootnet.Contains(ip) {
						used = true
						break
					}
				}
				if used {
					fmt.Println("ip used in root wg config peers")
					break
				}
			}

			if valid && !used {
				break
			}
		}
		wgi.Config.Interface.Addresses = []string{ip.String() + "/32"}
	}
	if len(c.DNS) > 0 {
		wgi.Config.Interface.DNS = c.DNS
	}
	allowedips := []string{}
	endpoint := ""
	var persistentkeepalive uint16 = 0
	for _, peer := range clientwg.Config.Peers {
		if peer.PublicKey == rootwg.Config.Interface.PublicKey {
			allowedips = append(allowedips, peer.AllowedIPs...)
			endpoint = peer.Endpoint
			persistentkeepalive = peer.PersistentKeepalive
			break
		}
	}
	if c.Endpoint != "" {
		endpoint = c.Endpoint
	}
	if c.PersistentKeepalive > 0 {
		persistentkeepalive = c.PersistentKeepalive
	}
	// add root peer
	wgi.Config.Peers = append(wgi.Config.Peers, WgPeer{
		PublicKey:           rootwg.Config.Interface.PublicKey,
		AllowedIPs:          allowedips,
		Endpoint:            endpoint,
		PersistentKeepalive: persistentkeepalive,
	})
}

func notifyNms(wgi *WgInfo, c Config, token string) {
	fmt.Println("updating mnms wg config...")
	// 1. post wgclient config to mnms /api/v1/wg
	wgPub(wgi, c, token)
	// 2. add wgclient peer information to wg server (root) to join the network
	wgJoin(wgi, c, token)
}

func wgPub(wgi *WgInfo, c Config, token string) {
	wgData := make(map[string]WgInfo)
	status, _ := WgStatus(c.Interface)
	wgi.Status = status
	wgData[c.Name] = *wgi
	jsonBytes, err := json.Marshal(wgData)
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
	resp, err := PostWithToken(c.RootUrl+"/api/v1/wg", token, bytes.NewBuffer(jsonBytes))
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
	if resp.StatusCode != 200 {
		fmt.Println("post wg config failed")
		os.Exit(1)
	}
	defer resp.Body.Close()
}

func wgJoin(wgi *WgInfo, c Config, token string) {
	// check if root wg config contains wgclient peer
	resp, err := GetWithToken(c.RootUrl+"/api/v1/wg", token)
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
	wgData := make(map[string]WgInfo)
	err = json.NewDecoder(resp.Body).Decode(&wgData)
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
	resp.Body.Close()
	rootwg := wgData[c.RootName]
	found := false
	idx := -1
	for i, peer := range rootwg.Config.Peers {
		if peer.PublicKey == wgi.Config.Interface.PublicKey {
			found = true
			idx = i
			break
		}
	}
	cmdinfo := make(map[string]CmdInfo)
	if !found {
		cmd := "wg root config peer add " + wgi.Config.Interface.PublicKey + " " + wgi.Config.Interface.Addresses[0]
		insertcmd(cmd, &cmdinfo)
	} else {
		cmd := "wg root config peer set allowedips " + strconv.Itoa(idx) + " " + wgi.Config.Interface.Addresses[0]
		insertcmd(cmd, &cmdinfo)
	}
	insertcmd("wg root stop", &cmdinfo)
	insertclientcmd("wg stop", c.ClientName, &cmdinfo)
	jsonBytes, err := json.Marshal(cmdinfo)
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
	resp, err = PostWithToken(c.RootUrl+"/api/v1/commands", token, bytes.NewBuffer(jsonBytes))
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
	if resp.StatusCode != 200 {
		fmt.Println("post cmd failed")
		os.Exit(1)
	}
	resp.Body.Close()

	fmt.Println("waiting for mnms to update wg config...")
	// check if wg is updated every 5 seconds, count 5 times
	i := 0
	for {
		resp, err := GetWithToken(c.RootUrl+"/api/v1/wg", token)
		if err != nil {
			fmt.Println(err)
			os.Exit(1)
		}
		if resp.StatusCode != 200 {
			fmt.Println("get wg config failed")
			os.Exit(1)
		}
		wgdata := make(map[string]WgInfo)
		err = json.NewDecoder(resp.Body).Decode(&wgdata)
		if err != nil {
			fmt.Println(err)
			os.Exit(1)
		}
		resp.Body.Close()

		wginfo := wgdata[c.RootName]
		rootupdated := false
		for _, peer := range wginfo.Config.Peers {
			if peer.PublicKey == wgi.Config.Interface.PublicKey {
				rootupdated = true
				break
			}
		}
		if rootupdated {
			break
		}
		i++
		if i >= 5 {
			fmt.Println("wg is not updated")
			os.Exit(1)
		}
		fmt.Println("waiting for wg to be updated, retry", i)
		time.Sleep(5 * time.Second)
	}

	fmt.Println("mnms wg restarting...")
	cmdinfo = make(map[string]CmdInfo)
	insertcmd("wg root config generate", &cmdinfo)
	jsonBytes, err = json.Marshal(cmdinfo)
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
	resp, err = PostWithToken(c.RootUrl+"/api/v1/commands", token, bytes.NewBuffer(jsonBytes))
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
	if resp.StatusCode != 200 {
		fmt.Println("post cmd failed")
		os.Exit(1)
	}
	resp.Body.Close()

	//
	time.Sleep(5 * time.Second)
	cmdinfo = make(map[string]CmdInfo)
	insertcmd("wg root start", &cmdinfo)
	insertclientcmd("wg start", c.ClientName, &cmdinfo)
	jsonBytes, err = json.Marshal(cmdinfo)
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
	resp, err = PostWithToken(c.RootUrl+"/api/v1/commands", token, bytes.NewBuffer(jsonBytes))
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
	if resp.StatusCode != 200 {
		fmt.Println("post cmd failed")
		os.Exit(1)
	}
	resp.Body.Close()
}
