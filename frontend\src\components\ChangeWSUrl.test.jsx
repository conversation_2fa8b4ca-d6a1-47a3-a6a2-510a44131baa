import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen } from '@testing-library/react';
import ChangeWSUrl from './ChangeWSUrl';
import { renderWithProviders, userEvent } from '../tests/test-utils';
import { useSettingStore } from '../store/setting-store';

// Mock the setting store
vi.mock('../store/setting-store', () => ({
  useSettingStore: vi.fn(),
}));

describe('ChangeWSUrl', () => {
  const mockChangeWsURL = vi.fn();
  
  beforeEach(() => {
    mockChangeWsURL.mockClear();
    useSettingStore.mockReturnValue({
      wsURL: 'ws://localhost:27182',
      changeWsURL: mockChangeWsURL,
    });
  });
  
  it('renders with the current WebSocket URL', () => {
    renderWithProviders(<ChangeWSUrl />);
    
    // Check if the input has the current WebSocket URL
    const input = screen.getByTestId('ws-url-input');
    expect(input).toHaveValue('ws://localhost:27182');
  });
  
  it('updates the input value when typing', async () => {
    renderWithProviders(<ChangeWSUrl />);
    
    // Get the input and type a new URL
    const input = screen.getByTestId('ws-url-input');
    await userEvent.clear(input);
    await userEvent.type(input, 'wss://new-server:8080');
    
    // Check if the input value was updated
    expect(input).toHaveValue('wss://new-server:8080');
  });
  
  it('shows an error when trying to save an empty URL', async () => {
    renderWithProviders(<ChangeWSUrl />);
    
    // Clear the input and click save
    const input = screen.getByTestId('ws-url-input');
    await userEvent.clear(input);
    
    const saveButton = screen.getByTestId('save-ws-url-button');
    await userEvent.click(saveButton);
    
    // Check if the error message is displayed
    const errorMessage = screen.getByTestId('ws-url-error');
    expect(errorMessage).toHaveTextContent('WebSocket URL cannot be empty');
    
    // Check that changeWsURL was not called
    expect(mockChangeWsURL).not.toHaveBeenCalled();
  });
  
  it('shows an error when trying to save an invalid WebSocket URL', async () => {
    renderWithProviders(<ChangeWSUrl />);
    
    // Type an invalid URL and click save
    const input = screen.getByTestId('ws-url-input');
    await userEvent.clear(input);
    await userEvent.type(input, 'http://invalid-ws-url');
    
    const saveButton = screen.getByTestId('save-ws-url-button');
    await userEvent.click(saveButton);
    
    // Check if the error message is displayed
    const errorMessage = screen.getByTestId('ws-url-error');
    expect(errorMessage).toHaveTextContent('Please enter a valid WebSocket URL (ws:// or wss://)');
    
    // Check that changeWsURL was not called
    expect(mockChangeWsURL).not.toHaveBeenCalled();
  });
  
  it('calls changeWsURL when saving a valid WebSocket URL', async () => {
    renderWithProviders(<ChangeWSUrl />);
    
    // Type a valid WebSocket URL and click save
    const input = screen.getByTestId('ws-url-input');
    await userEvent.clear(input);
    await userEvent.type(input, 'wss://new-server:8080');
    
    const saveButton = screen.getByTestId('save-ws-url-button');
    await userEvent.click(saveButton);
    
    // Check that changeWsURL was called with the new URL
    expect(mockChangeWsURL).toHaveBeenCalledWith('wss://new-server:8080');
    
    // Check that no error message is displayed
    expect(screen.queryByTestId('ws-url-error')).not.toBeInTheDocument();
  });
});
