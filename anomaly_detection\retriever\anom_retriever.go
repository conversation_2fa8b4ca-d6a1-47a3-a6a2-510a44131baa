package retriever

import (
	"context"
	"mnms/anomaly_detection/def"
	"mnms/llm"
)

type LocalRetriever struct {
	s def.Store
	m llm.CompleteEmbedder
}

func (l LocalRetriever) AddDocument(ctx context.Context, content string, meta map[string]interface{}) (int64, error) {
	emb, err := l.m.Embedding(ctx, content)
	if err != nil {
		return -1, err
	}

	return l.s.Add(content, meta, emb)
}

func (l LocalRetriever) GetReleventDocuments(ctx context.Context, query string, count int) ([]def.Document, error) {
	// check ctx
	vec, err := l.m.Embedding(context.Background(), query)
	if err != nil {
		return nil, err
	}
	return l.s.Search(ctx, vec, count)
}

func (l LocalRetriever) GetReleventDocumentsWithThreshold(ctx context.Context, query string, count int, threshold float32) ([]def.Document, error) {
	// check ctx
	vec, err := l.m.Embedding(context.Background(), query)
	if err != nil {
		return nil, err
	}
	var docs []def.Document
	candidates, err := l.s.Search(ctx, vec, count)
	if err != nil {
		return nil, err
	}
	for _, c := range candidates {
		if c.Score < threshold {
			docs = append(docs, c)
		}
	}
	return docs, nil
}

func (l LocalRetriever) Add(ctx context.Context, content string, meta map[string]interface{}) (int64, error) {
	emb, err := l.m.Embedding(ctx, content)
	if err != nil {
		return -1, err
	}
	// TODO: define a score means same
	return l.s.Upsert(ctx, content, meta, emb, 0.1)
}

func (l LocalRetriever) AddWithThreshold(ctx context.Context, content string, meta map[string]interface{}, threshold float32) (int64, error) {
	emb, err := l.m.Embedding(ctx, content)
	if err != nil {
		return -1, err
	}
	return l.s.Upsert(ctx, content, meta, emb, threshold)
}

// NewRetriever creates a new LocalRetriever.
func NewRetriever(store def.Store, m llm.LLMClient) def.Retriever {
	return &LocalRetriever{
		s: store,
		m: m,
	}
}

func GetReleventDocuments(ctx context.Context, query string, count int, r def.Retriever) ([]def.Document, error) {
	return r.GetReleventDocuments(ctx, query, count)
}
