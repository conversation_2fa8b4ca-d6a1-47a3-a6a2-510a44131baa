import { App, Form, Input, InputNumber, Modal } from "antd";
import React, { useCallback, useEffect } from "react";
import { FORM_FIELDS, VALIDATION_RULES } from "../../../constants/trap-setting";
import { useSendCommand } from "../../../services/mutations";
import { generateCommand } from "../../../utils/generate-commands";

const TrapSettingDialog = ({ data, onClose }) => {
  const { notification } = App.useApp();
  const sendCommand = useSendCommand();
  const [form] = Form.useForm();

  const handleSubmit = useCallback(
    (values) => {
      let commands = [];
      if (Array.isArray(data)) {
        data.map((item) => {
          const command = generateCommand(item, "", values, "trap");
          commands.push(command);
        });
        commands = commands.flat();
      } else {
        commands = generateCommand(data.mac, "", values, "trap");
      }
      handleCommandExecution(commands, sendCommand, notification);
      onClose();
    },
    [data, data?.mac, sendCommand, notification, onClose]
  );

  useEffect(() => {
    form.setFieldsValue({
      [FORM_FIELDS.SERVER_IP]: "",
      [FORM_FIELDS.SERVER_PORT]: 5162,
      [FORM_FIELDS.COM_STRING]: "",
    });
  }, [form]);

  return (
    <Modal
      title="Trap Setting"
      maskClosable={false}
      open
      destroyOnClose
      onCancel={onClose}
      okButtonProps={{ autoFocus: true, htmlType: "submit" }}
      style={{ top: 20 }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            handleSubmit(values);
            form.resetFields();
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form
        form={form}
        layout="vertical"
        name="trap_setting_form"
        clearOnDestroy
      >
        <Form.Item
          name={FORM_FIELDS.SERVER_IP}
          label="Server IP"
          rules={VALIDATION_RULES[FORM_FIELDS.SERVER_IP]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          name={FORM_FIELDS.SERVER_PORT}
          label="Server Port"
          rules={VALIDATION_RULES[FORM_FIELDS.SERVER_PORT]}
        >
          <InputNumber style={{ width: "100%" }} />
        </Form.Item>
        <Form.Item
          name={FORM_FIELDS.COM_STRING}
          label="Community String"
          rules={VALIDATION_RULES[FORM_FIELDS.COM_STRING]}
        >
          <Input />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default React.memo(TrapSettingDialog);
