package realtime

import (
	"fmt"
	"mnms"

	"mnms/anomaly_detection/loganalyse"
	"mnms/anomaly_detection/statistic"
	"mnms/llm"
	"net"
	"os"
	"regexp"
	"strings"
	"time"

	"github.com/qeof/q"
)

// ////////////////////////////////////////////
// For websoket
// sendRealtimeError send error message to root
func sendRealtimeError(msg string, err error) {
	// mnms.SendWebSocket(&mnms.WebSocketMessage{
	// 	Kind: "realtime-detection",

	// 	Level:   5,
	// 	Message: fmt.Sprintf("%s: %s", msg, err),
	// })
	mnms.SendSyslog(mnms.LOG_ALERT, mnms.QC.Name, fmt.Sprintf("%s: %s", msg, err))
}

// genSyslog greate syslog message
func genSyslog(priority int, msg string) string {
	timestamp := time.Now().Format(time.Stamp) // XXX not RFC3339
	return fmt.Sprintf("<%d>%s %s %s: %s", priority, timestamp, mnms.QC.Name, "bbanomsvc", msg)
}

// PostWsMessageToRoot post message to root server
func PostWsMessageToRoot(msg string) error {
	mnms.SendSyslog(mnms.LOG_ALERT, mnms.QC.Name, msg)
	return nil
}

// FIFOQueue represents a simple message FIFO queue
type FIFOQueue struct {
	MaxSize int
	items   []string
}

// Enqueue adds a message to the queue
func (q *FIFOQueue) Enqueue(msg string) error {
	if len(q.items) >= q.MaxSize {
		return fmt.Errorf("queue is full")
	}
	q.items = append(q.items, msg)
	return nil
}

// Dequeue removes a message from the queue
func (q *FIFOQueue) Dequeue() (string, error) {
	if len(q.items) == 0 {
		return "", fmt.Errorf("queue is empty")
	}
	msg := q.items[0]
	q.items = q.items[1:]
	return msg, nil
}

// Peek returns the first message in the queue
func (q *FIFOQueue) Peek() (string, error) {
	if len(q.items) == 0 {
		return "", fmt.Errorf("queue is empty")
	}
	return q.items[0], nil
}

// ChangeMaxSize changes the maximum size of the queue
func (q *FIFOQueue) ChangeMaxSize(newSize int) {
	q.MaxSize = newSize
}

// Size
func (q *FIFOQueue) Size() int {
	return len(q.items)
}

// IsEmpty
func (q *FIFOQueue) IsEmpty() bool {
	return len(q.items) == 0
}

// NewFIFOQueue creates a new FIFOQueue
func NewFIFOQueue(maxSize int) *FIFOQueue {
	return &FIFOQueue{
		MaxSize: maxSize,
		items:   make([]string, 0),
	}
}

// Realtime instance for real time detection
type RealtimeInstance struct {
	bufferingQueue FIFOQueue
	running        bool
	ListenPort     string
}

// IsRunning return the status of the instance
func (r *RealtimeInstance) IsRunning() bool {
	return r.running
}

// SetMaxMessages set the message per min
func (r *RealtimeInstance) SetMaxMessages(messagesPerMin int) {
	r.bufferingQueue.ChangeMaxSize(messagesPerMin)
}

// NewRealtimeInstance create a new RealtimeInstance
func NewRealtimeInstance(maxMessages int) *RealtimeInstance {
	return &RealtimeInstance{
		bufferingQueue: *NewFIFOQueue(maxMessages),
		running:        false,
	}

}

var instance *RealtimeInstance

// GetInstance get the instance of RealtimeInstance
func GetInstance() *RealtimeInstance {
	if instance == nil {
		instance = NewRealtimeInstance(statistic.LocalStatistic.Settings.RealTimeDetection.BufferSize)
	}
	return instance
}

// Resume resume the real time detection
func (r *RealtimeInstance) Resume() {
	if r.running {
		return
	}
	r.running = true
}

// InitSyslogServer init the syslog server
func (r *RealtimeInstance) InitSyslogServer() {
	go r.listenSyslog()
	go r.processJob()
}

// Start start the real time detection
func (r *RealtimeInstance) Start() {
	q.Q("start real time detection")
	if r.running {
		return
	}
	r.running = true

}

// Resart
func (r *RealtimeInstance) Restart(messagesPermin int) {
	if r.running {
		r.Stop()
	}

	r.bufferingQueue.ChangeMaxSize(messagesPermin)
	q.Q("restart real time detection", r)
	r.Start()
}

// Stop
func (r *RealtimeInstance) Stop() {
	if !r.running {
		return
	}
	q.Q("stop real time detection")
	r.running = false
	time.Sleep(1 * time.Second)
}

// checkSyslogSayHello check syslog server is alive
func checkSyslogSayHello(msg string) string {
	p := regexp.MustCompile(`^<(\d+)>(\w+\s+\d+\s+\d+:\d+:\d+)\s+(\S+)\s+bblogsvc: log service is forwarding$`)
	matches := p.FindStringSubmatch(msg)
	if len(matches) == 0 {
		return ""
	}
	return strings.TrimSpace(matches[3])
}

// listenSyslog
func (r *RealtimeInstance) listenSyslog() error {
	r.ListenPort = mnms.QC.SyslogServerAddr

	if r.ListenPort == "" {
		availablePort, err := mnms.GetAvailablePort()
		if err != nil {
			q.Q(err)
			return err
		}
		r.ListenPort = fmt.Sprintf(":%d", availablePort)
		statistic.LocalStatistic.Settings.RealTimeDetection.SyslogAddr = r.ListenPort
	}
	fmt.Printf("listen syslog at %s \n", r.ListenPort)
	// listen and reuse the port

	udpsock, err := net.ListenPacket("udp4", r.ListenPort)
	if err != nil {
		q.Q(err)
		fmt.Fprintf(os.Stderr, "error: syslog server can't start at %v\n", r.ListenPort)
		return err
	}
	defer udpsock.Close()

	lastForwardMessage := ""
	for {
		if !r.running {
			time.Sleep(1 * time.Second)
			continue
		}
		buf := make([]byte, 1024*2)
		mlen, _, err := udpsock.ReadFrom(buf)

		if err != nil {
			q.Q(err)
		}
		// avoid loop
		if lastForwardMessage == string(buf[:mlen]) {
			continue
		}

		// push syslog to job queue
		msg := string(buf[:mlen])
		err = r.bufferingQueue.Enqueue(msg)
		if err != nil {
			// full, busy
			msg := genSyslog(mnms.LOG_ALERT, "Real time detection queue is full, drop syslog")
			err := PostWsMessageToRoot(msg)
			if err != nil {
				q.Q("PostWsMessageToRoot fail: ", err)
				mnms.SendSyslog(mnms.LOG_ERR, "bbanomsvc", fmt.Sprintf("Post %s to root fail: %s", msg, err))
			}
		}
		lastForwardMessage = msg

	}

}

// processJob
func (r *RealtimeInstance) processJob() {
	type output struct {
		Message string
		Reason  string
	}
	llmSettings, err := statistic.GetLargeLanguageModelFromAnomalyStatistics()
	if err != nil {
		q.Q("get llm settings error: ", err)
		return
	}
	mod, err := llm.NewLLMClient(llmSettings)
	if err != nil {
		msg := fmt.Sprintf("connect to LLM error: %s", err)
		mnms.SendSyslog(mnms.LOG_ERR, "bbanomsvc", msg)
		PostWsMessageToRoot(msg)
		return
	}

	for {
		if !r.running {
			time.Sleep(1 * time.Second)
			continue
		}

		if r.bufferingQueue.IsEmpty() {
			time.Sleep(1 * time.Second)
			continue
		}

		msg, err := r.bufferingQueue.Dequeue()
		if err != nil {
			q.Q("Dequeue fail: ", err)
			continue
		}
		q.Q("Dequeue", msg)

		// check is message is a syslog special message
		logsvc := checkSyslogSayHello(msg)
		if logsvc != "" {
			q.Q("logsvc", logsvc)
			statistic.UpdateUpstreamLogSvc(logsvc)
			continue
		}
		// statistic.LocalStatistic.Realtime.IncMessage()
		score := statistic.LocalStatistic.Settings.Score

		result, err := loganalyse.LogAnomalyDetect(mod, msg, score)
		if err != nil {
			msg := fmt.Sprintf("Detect %s fail: %s", msg, err)
			mnms.SendSyslog(mnms.LOG_ERR, "bbanomsvc", msg)
			PostWsMessageToRoot(msg)
			if result != nil {
				statistic.AddRealtimeAnalyseResult(result, err)
			}
			continue
		}
		if !result.Normal {
			// send to root
			q.Q("alert", msg, result.Reason)
			err := PostWsMessageToRoot(msg)
			if err != nil {
				mnms.SendSyslog(mnms.LOG_ERR, "bbanomsvc", fmt.Sprintf("Post %s to root fail: %s", msg, err))
			}
		}
		statistic.AddRealtimeAnalyseResult(result, err)

	}
}
