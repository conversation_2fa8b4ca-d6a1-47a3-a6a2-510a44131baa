package llm

import (
	"encoding/json"
	"testing"
)

// TestGenerateSchema tests the GenJSONSchema function
func TestGenerateSchema(t *testing.T) {

	type RunAPIParam struct {
		URL         string `json:"url"`
		Method      string `json:"method"`
		RequestBody any    `json:"request_body,omitempty" jsonschema:"type=object"`
	}

	type NeedFetchData struct {
		NeedFetch bool          `json:"need_fetch"`
		RunAPIs   []RunAPIParam `json:"run_apis"`
	}
	var m NeedFetchData
	schema := GenOpenAIResponseFormatJSONSchema("A team memeber struct", &m)

	if schema == nil {
		t.Fatal("schema is nil")
	}

	jsonByte, err := json.MarshalIndent(schema, "", "  ")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(string(jsonByte))

	s := GenerateSchema(m)
	jsonByte, err = json.MarshalIndent(s, "", "  ")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(string(jsonByte))

	// Testing nested struct
	type Team struct {
		Name string `json:"name"`

		Children struct {
			Child1 string `json:"child1"`
			Child2 string `json:"child2"`
		} `json:"children"`
		ExtraBody map[string]any `json:"extra_body"`
	}

	var t1 Team

	s = GenerateSchema(t1)
	jsonByte, err = json.MarshalIndent(s, "", "  ")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(string(jsonByte))

}

// TestIsPointerToSTruct
func TestIsPointerToSTruct(t *testing.T) {
	type MyStruct struct {
		Field1 string
		Field2 int
	}
	var x MyStruct
	var y *MyStruct
	var z int
	var m map[string]int
	var n *map[string]int
	var l []string
	var pl *[]string

	if !isPointerToSpecificTypes(&x) {
		t.Fatal("x is a pointer to struct")
	}
	if !isPointerToSpecificTypes(y) {
		t.Fatal("y is a pointer to struct")
	}
	if isPointerToSpecificTypes(&z) {
		t.Fatal("z is not a pointer to struct")
	}
	if !isPointerToSpecificTypes(m) {
		t.Fatal("m is not a pointer to struct")
	}
	if !isPointerToSpecificTypes(n) {
		t.Fatal("n is not a pointer to struct")
	}
	if !isPointerToSpecificTypes(l) {
		t.Fatal("l is not a pointer to struct")
	}
	if !isPointerToSpecificTypes(pl) {
		t.Fatal("pl is a pointer to struct")
	}

}
