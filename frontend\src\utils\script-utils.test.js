import { describe, it, expect } from 'vitest';
import { processCommands } from './script-utils';

describe('script-utils', () => {
  describe('processCommands', () => {
    it('should return empty array string when params is null', () => {
      // Act
      const result = processCommands(null, {});
      
      // Assert
      expect(result).toBe('[]');
    });
    
    it('should return empty array string when params is undefined', () => {
      // Act
      const result = processCommands(undefined, {});
      
      // Assert
      expect(result).toBe('[]');
    });
    
    it('should return empty array string when params is not a string', () => {
      // Act
      const result = processCommands(123, {});
      
      // Assert
      expect(result).toBe('[]');
    });
    
    it('should process a single command without flags', () => {
      // Arrange
      const params = 'show version';
      const flags = {};
      
      // Act
      const result = processCommands(params, flags);
      
      // Assert
      expect(result).toEqual([
        {
          command: 'show version',
          client: undefined,
          tag: undefined,
          kind: undefined,
          nooverwrite: undefined,
          nosyslog: undefined,
        },
      ]);
    });
    
    it('should process a single command with flags', () => {
      // Arrange
      const params = 'show version';
      const flags = {
        cc: 'client1',
        ct: 'tag1',
        ck: 'kind1',
        cno: true,
        cns: false,
      };
      
      // Act
      const result = processCommands(params, flags);
      
      // Assert
      expect(result).toEqual([
        {
          command: 'show version',
          client: 'client1',
          tag: 'tag1',
          kind: 'kind1',
          nooverwrite: true,
          nosyslog: false,
        },
      ]);
    });
    
    it('should process multiple commands with Unix line endings', () => {
      // Arrange
      const params = 'show version\nshow interfaces\nshow ip route';
      const flags = { cc: 'client1' };
      
      // Act
      const result = processCommands(params, flags);
      
      // Assert
      expect(result).toEqual([
        {
          command: 'show version',
          client: 'client1',
          tag: undefined,
          kind: undefined,
          nooverwrite: undefined,
          nosyslog: undefined,
        },
        {
          command: 'show interfaces',
          client: 'client1',
          tag: undefined,
          kind: undefined,
          nooverwrite: undefined,
          nosyslog: undefined,
        },
        {
          command: 'show ip route',
          client: 'client1',
          tag: undefined,
          kind: undefined,
          nooverwrite: undefined,
          nosyslog: undefined,
        },
      ]);
    });
    
    it('should process multiple commands with Windows line endings', () => {
      // Arrange
      const params = 'show version\r\nshow interfaces\r\nshow ip route';
      const flags = { cc: 'client1' };
      
      // Act
      const result = processCommands(params, flags);
      
      // Assert
      expect(result).toEqual([
        {
          command: 'show version',
          client: 'client1',
          tag: undefined,
          kind: undefined,
          nooverwrite: undefined,
          nosyslog: undefined,
        },
        {
          command: 'show interfaces',
          client: 'client1',
          tag: undefined,
          kind: undefined,
          nooverwrite: undefined,
          nosyslog: undefined,
        },
        {
          command: 'show ip route',
          client: 'client1',
          tag: undefined,
          kind: undefined,
          nooverwrite: undefined,
          nosyslog: undefined,
        },
      ]);
    });
    
    it('should ignore empty lines and comments', () => {
      // Arrange
      const params = 'show version\n\n# This is a comment\nshow interfaces';
      const flags = {};
      
      // Act
      const result = processCommands(params, flags);
      
      // Assert
      expect(result).toEqual([
        {
          command: 'show version',
          client: undefined,
          tag: undefined,
          kind: undefined,
          nooverwrite: undefined,
          nosyslog: undefined,
        },
        {
          command: 'show interfaces',
          client: undefined,
          tag: undefined,
          kind: undefined,
          nooverwrite: undefined,
          nosyslog: undefined,
        },
      ]);
    });
    
    it('should trim whitespace from commands', () => {
      // Arrange
      const params = '  show version  \n  show interfaces  ';
      const flags = {};
      
      // Act
      const result = processCommands(params, flags);
      
      // Assert
      expect(result).toEqual([
        {
          command: 'show version',
          client: undefined,
          tag: undefined,
          kind: undefined,
          nooverwrite: undefined,
          nosyslog: undefined,
        },
        {
          command: 'show interfaces',
          client: undefined,
          tag: undefined,
          kind: undefined,
          nooverwrite: undefined,
          nosyslog: undefined,
        },
      ]);
    });
  });
});
