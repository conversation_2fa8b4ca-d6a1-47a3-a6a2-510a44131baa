import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen } from "@testing-library/react";
import { App } from "antd";
import IdpsRecordList from "./IdpsRecordList";

// Mock dayjs
vi.mock("dayjs", () => {
  const mockDayjs = vi.fn((date) => ({
    format: vi.fn(() => "2024-01-01"),
    isAfter: vi.fn(() => true),
    isBefore: vi.fn(() => true),
    startOf: vi.fn(() => mockDayjs()),
    endOf: vi.fn(() => mockDayjs()),
  }));
  return { default: mockDayjs };
});

// Mock react-apexcharts
vi.mock("react-apexcharts", () => ({
  default: ({ options, series, type, height }) => (
    <div data-testid="apex-chart">
      <div>Chart Type: {type}</div>
      <div>Height: {height}</div>
      <div>Series: {JSON.stringify(series)}</div>
    </div>
  ),
}));

describe("IdpsRecordList", () => {
  const mockData = [
    {
      date: "2024-01-01T12:00:00Z",
      files: ["file1.txt", "file2.txt"],
    },
    {
      date: "2024-01-02T12:00:00Z",
      files: ["file3.txt"],
    },
  ];

  const mockProps = {
    data: mockData,
    loading: false,
    selectedService: "test-service",
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders record list with data", () => {
    render(
      <App>
        <IdpsRecordList {...mockProps} />
      </App>
    );

    expect(screen.getByText("Record List")).toBeInTheDocument();
    expect(screen.getByText("Service: test-service")).toBeInTheDocument();
    expect(screen.getByText("Total Records:")).toBeInTheDocument();
    expect(screen.getByText("Total Files:")).toBeInTheDocument();
  });

  it("shows loading state", () => {
    render(
      <App>
        <IdpsRecordList {...mockProps} loading={true} />
      </App>
    );

    expect(screen.getByText("Record List")).toBeInTheDocument();
  });

  it("handles empty data", () => {
    render(
      <App>
        <IdpsRecordList {...mockProps} data={[]} />
      </App>
    );

    expect(screen.getByText("Record List")).toBeInTheDocument();
    expect(screen.getByText("No record data available")).toBeInTheDocument();
  });

  it("displays date picker", () => {
    render(
      <App>
        <IdpsRecordList {...mockProps} />
      </App>
    );

    expect(screen.getByText("Filter by date:")).toBeInTheDocument();
  });
});
