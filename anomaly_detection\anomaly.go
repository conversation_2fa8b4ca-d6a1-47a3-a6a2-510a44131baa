package anomaly_detection

// The anomaly_detection package provides severial anomaly detection related functions for bbrootsvc
// It also provides LanguageChain definition and implementation. Every package can use this package to
// get AI enhenced ability.
//
// For AI agent, please refer actionchain package. /chain/actionchain
// For anomaly analysis, please refer anomalychain package. /chain/anomalychain

// Package description for anomaly_detection
// service: A package that provides several functions to handle root's API and command. You can find
//    useful functions to communicate with the root service. (bbrootsvc)
// llm: This package encapsulates the logic for the LLM. including local LLM ollama and openAI, usually
//    used with languagechain package.
// def: The def package provides the definition of the data structure used in the anomaly_detection package.
// prompt: Encapsulates prompt object and related functions. Usually used with llm package complete function.
// chain: Consists of several chains, such as actionchain, anomalychain, etc. Each chain has its own target.
import (
	"mnms/anomaly_detection/report"
	"sync"
)

// reports from anomaly service
var AnomalyReports = make(map[string]report.Report)
var AnomalyReportsMutex = sync.Mutex{}
