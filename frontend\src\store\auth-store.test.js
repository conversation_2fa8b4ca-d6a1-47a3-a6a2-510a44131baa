import { describe, it, expect, beforeEach, vi, afterEach } from "vitest";
import { act } from "@testing-library/react";

// Import the actual store
import { useAuthStore as actualUseAuthStore } from "./auth-store";

// Create a mock store
const initialState = {
  token: "",
  user: "",
  role: "",
  sessionid: null,
};

const mockStore = {
  ...initialState,
  setAuthData: vi.fn((data) => {
    Object.keys(initialState).forEach((key) => {
      if (data[key] !== undefined) {
        mockStore[key] = data[key];
      }
    });
  }),
  clearAuthData: vi.fn(() => {
    mockStore.token = "";
    mockStore.user = "";
    mockStore.role = "";
    mockStore.sessionid = null;
  }),
};

// Mock the useAuthStore hook
const useAuthStore = {
  getState: vi.fn(() => mockStore),
};

// Mock sessionStorage
const sessionStorageMock = (() => {
  let store = {};
  return {
    getItem: vi.fn((key) => store[key] || null),
    setItem: vi.fn((key, value) => {
      store[key] = value.toString();
    }),
    clear: vi.fn(() => {
      store = {};
    }),
    removeItem: vi.fn((key) => {
      delete store[key];
    }),
  };
})();

describe("auth-store", () => {
  beforeEach(() => {
    // Setup sessionStorage mock
    Object.defineProperty(window, "sessionStorage", {
      value: sessionStorageMock,
      writable: true,
    });

    // Clear sessionStorage
    sessionStorageMock.clear();

    // Reset mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should initialize with default values", () => {
    // Act
    const state = mockStore;

    // Assert
    expect(state.token).toBe("");
    expect(state.user).toBe("");
    expect(state.role).toBe("");
    expect(state.sessionid).toBe(null);
  });

  it("should set auth data correctly", () => {
    // Arrange
    const authData = {
      token: "test-token",
      user: { id: "1", username: "testuser", email: "<EMAIL>" },
      role: "admin",
    };

    // Act
    mockStore.setAuthData(authData);

    // Assert
    expect(mockStore.setAuthData).toHaveBeenCalledWith(authData);
    expect(mockStore.token).toBe(authData.token);
    expect(mockStore.user).toEqual(authData.user);
    expect(mockStore.role).toBe(authData.role);
  });

  it("should ignore properties not in initial state", () => {
    // Arrange
    const authData = {
      token: "test-token",
      user: { id: "1", username: "testuser" },
      role: "admin",
      invalidProperty: "should-be-ignored",
    };

    // Act
    mockStore.setAuthData(authData);

    // Assert
    expect(mockStore.token).toBe(authData.token);
    expect(mockStore.user).toEqual(authData.user);
    expect(mockStore.role).toBe(authData.role);
    expect(mockStore.invalidProperty).toBeUndefined();
  });

  it("should clear auth data", () => {
    // Arrange
    const authData = {
      token: "test-token",
      user: { id: "1", username: "testuser" },
      role: "admin",
    };

    mockStore.setAuthData(authData);

    // Verify data was set
    expect(mockStore.token).toBe(authData.token);

    // Act
    mockStore.clearAuthData();

    // Assert
    expect(mockStore.clearAuthData).toHaveBeenCalled();
    expect(mockStore.token).toBe("");
    expect(mockStore.user).toBe("");
    expect(mockStore.role).toBe("");
  });

  it("should set sessionid correctly", () => {
    // Arrange
    const authData = {
      token: "test-token",
      user: { id: "1", username: "testuser" },
      role: "admin",
      sessionid: "test-session-id",
    };

    // Act
    mockStore.setAuthData(authData);

    // Assert
    expect(mockStore.sessionid).toBe(authData.sessionid);
  });
});
