import { Typography } from "antd";
import dayjs from "dayjs";
import React from "react";

export const useLogTableColumn = () => {
  return React.useMemo(
    () => [
      {
        title: "Timestamp",
        dataIndex: "Timestamp",
        key: "Timestamp",
        width: 250,
        exportable: true,
        render: (data) => {
          // RFC3339 format dayjs will judge wrong
          const tmp = data.replace("Z", "");
          return dayjs(tmp).format("YYYY/MM/DD HH:mm:ss");
        },
        sorter: (a, b) => (a.Timestamp > b.Timestamp ? 1 : -1),
      },
      {
        title: "Hostname",
        dataIndex: "Hostname",
        key: "Hostname",
        width: 150,
        exportable: true,
        sorter: (a, b) => (a.Hostname > b.Hostname ? 1 : -1),
      },
      {
        title: "Facility",
        width: 100,
        dataIndex: "Facility",
        key: "Facility",
        exportable: true,
        sorter: (a, b) => (a.Hostname > b.Hostname ? 1 : -1),
      },
      {
        title: "Severity",
        dataIndex: "Severity",
        key: "Severity",
        width: 100,
        exportable: true,
        sorter: (a, b) => (a.Severity > b.Severity ? 1 : -1),
      },
      {
        title: "Priority",
        dataIndex: "Priority",
        key: "Priority",
        width: 100,
        exportable: true,
        sorter: (a, b) => (a.Priority > b.Priority ? 1 : -1),
      },
      {
        title: "Appname",
        dataIndex: "Appname",
        key: "Appname",
        width: 100,
        exportable: true,
        sorter: (a, b) => (a.Appname > b.Appname ? 1 : -1),
      },
      {
        title: "Message",
        dataIndex: "Message",
        key: "Message",
        width: 450,
        exportable: true,
        render: (data) => {
          return (
            <Typography.Paragraph
              ellipsis={{
                rows: 5,
              }}
              style={{ overflow: "auto", maxHeight: "250px" }}
            >
              {data}
            </Typography.Paragraph>
          );
        },
      },
    ],
    []
  );
};
