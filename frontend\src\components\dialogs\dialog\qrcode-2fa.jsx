import { Alert, Form, Image, Modal, QRCode, Space } from "antd";
import React from "react";
import logo from "../../../assets/images/NIMBL_Logo.svg";

const GenerateQRCode = ({ data, onClose }) => {
  const [form] = Form.useForm();
  const otpAuthURL = `otpauth://totp/Atop_NIMBL:${data.account}?secret=${data.secret}&issuer=Atop_NIMBL`;
  return (
    <Modal
      title="Scan QR Code"
      maskClosable={false}
      open
      destroyOnClose
      onCancel={onClose}
      onOk={onClose}
    >
      <Space direction="vertical" align="center" size={15}>
        <Image height={56} src={logo} preview={false} />
        <Form name="2fa_user" size="large" autoComplete="off" form={form}>
          <Form.Item style={{ display: "flex", justifyContent: "center" }}>
            <QRCode errorLevel="H" size={260} value={otpAuthURL} />
          </Form.Item>
          <Alert
            type="warning"
            message="Scan QR code with Google Authenticator to get 2FA code for next time
            login!"
            showIcon
          />
        </Form>
      </Space>
    </Modal>
  );
};

export default React.memo(GenerateQRCode);
