import { useCallback, useRef, useState } from "react";
import { PORT_INFO_CONFIG } from "../config/port-info.config";

export const useTrafficData = () => {
  const prevTrafficDataRef = useRef(null);
  const [chartState, setChartState] = useState({
    inData: PORT_INFO_CONFIG.CHART.INITIAL_STATE.createInitialData().data,
    outData: PORT_INFO_CONFIG.CHART.INITIAL_STATE.createInitialData().data,
    category: PORT_INFO_CONFIG.CHART.INITIAL_STATE.createInitialData().labels,
    selectedPort: "",
    trafficData: [],
    lineChartState: PORT_INFO_CONFIG.CHART.CONFIG,
  });

  const updateTrafficData = useCallback((portData, currentPort) => {
    if (!portData?.length) return;

    const filteredPort = portData.filter(
      (item) => item.portName === currentPort
    )[0];
    if (!filteredPort) return;

    const now = new Date();
    const timeStr = now.toLocaleTimeString();

    if (prevTrafficDataRef.current) {
      const prevData = prevTrafficDataRef.current;
      const inData = (prev) =>
        [filteredPort.inOctets - prevData.inOctets, ...prev.inData].slice(
          0,
          PORT_INFO_CONFIG.CHART.INITIAL_STATE.DATA_POINTS
        );
      const outData = (prev) =>
        [filteredPort.outOctets - prevData.outOctets, ...prev.outData].slice(
          0,
          PORT_INFO_CONFIG.CHART.INITIAL_STATE.DATA_POINTS
        );
      const category = (prev) =>
        [timeStr, ...prev.category].slice(
          0,
          PORT_INFO_CONFIG.CHART.INITIAL_STATE.DATA_POINTS
        );

      setChartState((prev) => ({
        ...prev,
        inData: inData(prev),
        outData: outData(prev),
        category: category(prev),
        lineChartState: {
          ...prev.lineChartState,
          series: [
            { name: "in traffic data", data: inData(prev) },
            { name: "out traffic data", data: outData(prev) },
          ],
          options: {
            ...prev.lineChartState.options,
            xaxis: { categories: category(prev) },
          },
        },
      }));
    }

    prevTrafficDataRef.current = filteredPort;
  }, []);

  const resetChartState = useCallback(() => {
    setChartState((prev) => ({
      ...prev,
      selectedPort: "",
      lineChartState: PORT_INFO_CONFIG.CHART.CONFIG,
      inData: PORT_INFO_CONFIG.CHART.INITIAL_STATE.createInitialData().data,
      outData: PORT_INFO_CONFIG.CHART.INITIAL_STATE.createInitialData().data,
      category: PORT_INFO_CONFIG.CHART.INITIAL_STATE.createInitialData().labels,
      trafficData: [],
    }));
    prevTrafficDataRef.current = null;
  }, []);

  const setSelectedPort = useCallback((value) => {
    setChartState((prev) => ({
      ...prev,
      selectedPort: value,
    }));
  }, []);

  return {
    chartState,
    updateTrafficData,
    resetChartState,
    setSelectedPort,
    setChartState,
  };
};
