import {
  ApartmentOutlined,
  ApiOutlined,
  ClusterOutlined,
  CodeOutlined,
  DashboardOutlined,
  DesktopOutlined,
  GlobalOutlined,
  KeyOutlined,
  SnippetsOutlined,
  UsergroupAddOutlined,
} from "@ant-design/icons";

/**
 * @typedef {Object} RouteConfig
 * @property {string} path - Route path
 * @property {string} name - Display name
 * @property {React.ReactNode} [icon] - Route icon
 * @property {RouteConfig[]} [routes] - Nested routes
 */

/**
 * Dashboard sub-routes configuration
 * @param {Object} options
 * @param {boolean} options.anomalies - Enable anomalies route
 * @param {boolean} options.idps - Enable IDPS route
 * @returns {RouteConfig[]}
 */
const getDashboardRoutes = ({ anomalies, idps }) => {
  const routes = [
    {
      path: "/dashboard/device",
      name: "Device",
    },
  ];

  if (idps) {
    routes.push({
      path: "/dashboard/idps",
      name: "IDPS",
    });
  }

  return routes;
};

/**
 * Main route configuration
 * @param {Object} options
 * @param {boolean} [options.anomalies=false] - Enable anomalies features
 * @param {boolean} [options.idps=false] - Enable IDPS features
 * @returns {Object} Route configuration object
 */
const routes = ({ anomalies = false, idps = false }) => ({
  route: {
    path: "/",
    routes: [
      {
        path: "/dashboard",
        name: "Dashboard",
        icon: <DashboardOutlined />,
        routes: getDashboardRoutes({ anomalies, idps }),
      },
      {
        path: "/devices",
        name: "Devices",
        icon: <DesktopOutlined />,
      },
      {
        path: "/script",
        name: "Script",
        icon: <CodeOutlined />,
      },
      {
        path: "/topology",
        name: "Topology",
        icon: <ApartmentOutlined />,
      },
      {
        path: "/mib-browser",
        name: "MIB Browser",
        icon: <GlobalOutlined />,
      },
      {
        path: "/users-management",
        name: "User Management",
        icon: <UsergroupAddOutlined />,
      },
      {
        path: "/logs",
        name: "Logs",
        icon: <SnippetsOutlined />,
      },
      {
        path: "/clusterinfo",
        name: "Cluster Info",
        icon: <ClusterOutlined />,
      },
      {
        path: "/tunnels",
        name: "Tunnel",
        icon: <ApiOutlined />,
      },
      {
        path: "/key-store",
        name: "Key Store",
        icon: <KeyOutlined />,
      },
    ],
  },
  location: {
    pathname: "/",
  },
});

export default routes;
