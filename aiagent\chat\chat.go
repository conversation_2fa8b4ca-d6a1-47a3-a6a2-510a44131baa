package chat

import (
	"context"
	"fmt"

	"mnms/llm"
)

type ChatResponse struct {
	Content string `json:"content"`
	Error   error  `json:"error"`
}

// NewChatResponse creates a new instance of ChatResponse.
func NewChatResponse(err error, format string, a ...any) ChatResponse {
	text := fmt.Sprintf(format, a...)

	return ChatResponse{Content: text, Error: err}
}

type Chat interface {
	GetName() string
	GetDescription() string
	GetSystemPrompt() string
	Talk(input string) ChatResponse
}

type StructedOutputChat struct {
	BasicChat
	JSONSchema map[string]any       `json:"json_schema"`
	m          llm.CompleteEmbedder `json:"-"` // do not serialize
}

func NewStructedOutputChat(m llm.CompleteEmbedder, systemPrompt string, name string, description string, schema map[string]any) *StructedOutputChat {
	return &StructedOutputChat{
		BasicChat: BasicChat{
			SystemPrompt: systemPrompt,
			Name:         name,
			Description:  description,
		},
		JSONSchema: schema,
		m:          m,
	}
}

type BasicChat struct {
	SystemPrompt string `json:"system_prompt"`
	Description  string `json:"description"`
	Name         string `json:"name"`
	m            llm.CompleteEmbedder
}

// NewBasicChat creates a new instance of Session.
func NewBasicChat(m llm.CompleteEmbedder, systemPrompt string, name string, description string) *BasicChat {
	return &BasicChat{
		SystemPrompt: systemPrompt,
		m:            m,
		Name:         name,
		Description:  description,
	}
}

// Invoke
func (s *BasicChat) Invoke(ctx context.Context, input string) (string, error) {
	ret := s.Talk(ctx, input)
	return ret.Content, ret.Error
}

// GetName returns the name of the chat.
func (s *BasicChat) GetName() string {
	return s.Name
}

// GetDescription returns the description of the chat.
func (s *BasicChat) GetDescription() string {
	return s.Description
}

// Talk is a method that generates a completion based on the given prompt.
func (s *BasicChat) Talk(ctx context.Context, input string) ChatResponse {

	ans, err := s.m.Complete(ctx, []llm.Message{
		{
			Role:    "system",
			Content: s.SystemPrompt,
		},
		{
			Role:    "user",
			Content: input,
		},
	})
	return ChatResponse{Content: ans, Error: err}
}

// Doc2ActionDefine creates a new NimblDocumentChat that inputs a document and outputs a formatted action JSON string
// Nimble Document (Raw text from help.go or document) -> Action definition JSON (aiagent\actions ActionDefinition)
func Doc2ActionDefine(mod llm.LLMClient) *BasicChat {
	sysPrompt := `You are a RAG engineer, your job is understand input document and write a RAG document
- The input document is a CLI command document like this:

Help for command "anomaly":
Usage: anomaly detect [url] [distance]
    This command analyzes a specified log file for anomalies. The file
    to be analyzed can either be hosted on a root server or be a local
    file specified with a file:// URL. The command uses the provided
    anomaly distance as a threshold to determine significant anomalies.

Parameters:
    [client]    : Anomaly service client name.
    [url]       : The URL to the log file to be analyzed. This can be of two types:
                    - A URL managed by the root server. These URLs can be accessed
                      via rooturl/api/v1/files/{file name}. The {file name} should
                      be replaced with the actual name of the file you want to analyze.
                    - A local file URL with the file:// scheme.
    [distance]  : The distance threshold (optional, default is 0.4).

Examples:
   $ bbctl -cc an1 anomaly detect http://rooturl/api/v1/files/syslog.log 0.5
   $ bbctl -cc an1 anomaly detect file:///e:/syslog.log 0.5
   $ bbctl -cc an1 anomaly detect file://syslog.log

Usage: anomaly detect [msg]
		This command analyzes a specified log message for anomalies.

Examples:
	$ bbctl -cc an1 anomaly detect "<5>May 15 15:22:55 root alert: root offline"


Normally input document had description of what is this command can do and necessary parameters and there's description.

The output document is for LLM RAG, it should be clear and well know, we want to LLM refer output document then generate a JSON object that will pass to application as a parameters to execute some actions.
- Output only a JSON object(s) as plain JSON, without any additional text, commentary, or formatting.
- Each CLI command or API can be considered as an action, so the output document should have proper action name.
- If command has parameter MAC or mac address that means the command is for a netword device, the we want to verify please add "verify":"devInfo" to the output document.
- execute: The JSON object which contains CLI command or API execution details.
  - type: The type of the action CMD or REST
  - cmd: The CLI command to execute
  - parameters: The parameters for the action
    - name : The name of the parameter
    - description : A description of the parameter
  - uri: The URI for the API
  - method: The HTTP method for the API
  - body: The body for the API

The output document is a JSON object like this
{
  "action": "anomaly_detect",
  "description": "Detect anomalies",
  "execute":{
    "type": "CMD",
    "cmd": "anomaly detect [url] [distance]",
    "parameters":[
       {
          "name":"url",
          "description":"The URL to the log file to be analyzed",
        },
       {
         "name":"distance",
         "description":"The distance threshold (optional, default is 0.4)."
       }
    ]
  },
  "examples": [
    "bbctl -cc an1 anomaly detect http://rooturl/api/v1/files/syslog.log 0.5",
    "bbctl -cc an1 anomaly detect file:///e:/syslog.log 0.5",
    "bbctl -cc an1 anomaly detect file://syslog.log"]
}

`
	return &BasicChat{
		Name:         "docuemtn-to-action-define",
		Description:  "Input a API or Command document, output a JSON object that can be used as a action definition",
		m:            mod,
		SystemPrompt: sysPrompt,
	}
}

// NormalizeJSON creates a new JSONNormalizerChat that inputs a string may contains JSON object and outputs a formatted JSON object
// LLM output string (should be JSOn) -> JSON object string
func NormalizeJSON(mod llm.LLMClient) *BasicChat {
	sysPrompt := `You are a helpful assistant tasked with processing a string input. Follow these steps:

1. Identify any JSON objects within the provided string.
2. Inspect the fields (keys) within the JSON object.
3. If a field’s meaning is similar to any of the items in the following list, rename the field to match the corresponding name from the list:

- mac
- modelname
- timestamp
- scanproto (scan protocol)
- ipaddress (IP address)
- netmask
- gateway
- kernel
- isdhcp (Does DHCP function enable?)
- isonline (Is online?)
- username
- password
- tunneled_url (SSH tunnel URL)

Instructions:
- Ensure the updated JSON object retains its original structure and data integrity.
- Output only the modified JSON object(s) as plain JSON, without any additional text, commentary, or formatting.

Example:
Input string:
"Device log: {\"macAddress\": \"00:1A:2B:3C:4D:5E\", \"dhcp\": true, \"model\": \"X123\"}"

Output:
{"mac": "00:1A:2B:3C:4D:5E", "isdhcp": true, "modelname": "X123"}`

	return &BasicChat{
		m:            mod,
		SystemPrompt: sysPrompt,
		Name:         "normalize-json",
		Description:  "Input a JSON object, output a JSON object that has normalized keys. The output JSON keys should be match Nimbl DevInfo JSON keys",
	}
}

// ActionReference Give a JSON object, generate a text that properly used as a RAG content
// objectKey is the key of the JSON object that will be used for refer to original JSON object.
// For example, we have a JSON object like this:
//
//	{
//	  "action": "anomaly_detect",
//	  "description": "Detect anomalies",
//	  "example": "anomaly detect http://file 0.1",
//	 }
//	 If we use description or action as a RAG content to calculate the similarity, it will not easy to match with
//	 natural language input. So we need to generate a text that properly used as a RAG content.
//	 Following is a example of output:
func ActionReference(mod llm.LLMClient) *BasicChat {
	systemPrompt := `You are an advanced language model tasked with processing command data for Retrieval-Augmented Generation (RAG). 
Given a JSON object describing a command or API, transform it into a simplified JSON object for RAG storage. 
The resulting JSON must include:

1. A "queries" field, which is a list of concise, command-like statements or questions that a user might use to refer to this command. Each query must include an example of the actual command.
- Ensure the "queries" field contains clear and actionable statements derived from the "description," and "example,".
- Each query must explicitly reference or include one of the provided command examples.
- Output only a JSON object, without any additional text, commentary, or formatting.
2. The "action" field, which remains unchanged.

### Example Input
{
  "action": "wg_config_interface_set",
  "description": "Set wg's multiple interface attributes (address, listen port, MTU, DNS).",
  "examples": [
    "wg config interface set **********/32",
  ]
}

### Expected Output
{
  "action": "wg_config_interface_set",
  "queries": [
    "wg config interface set **********/32",
    "Set the WireGuard interface listen port with the command '-ck root wg config interface set **********/24 55820'",
    "Use 'wg config interface set **********/24 1400' to configure MTU for the WireGuard interface",
    "To assign DNS server *******, run '-ck root wg config interface set **********/24 55820 1400 *******'",
    "How do I configure a WireGuard interface with the command 'wg config interface set **********/32'?",
    "What is the command to set both IP and DNS for a WireGuard interface? Example: '-ck root wg config interface set **********/24 55820 1400 *******'"
  ]
}`
	return &BasicChat{
		m:            mod,
		SystemPrompt: systemPrompt,
		Name:         "action-to-action-reference",
		Description:  "Input a JSON object that describes a command or API, output a JSON object that can be used as a reference for RAG content",
	}
}

// ActionsConsultant creates a new NimblConsultantChat that inputs a string may contains JSON object and outputs a formatted JSON object
// nature language input -> answer
func ActionsConsultant(mod llm.LLMClient) *BasicChat {
	sysPrompt := `You are an intelligent system that maps natural language requests to relevant documentation and answer user's query. 
## Inputs
1. User Request: A natural language instruction, such as:
  - "Set 0E-1F-00-11-0A-DD IP to ************"
  - "How to enable WireGuard interface?"
2. Documentation: A structured JSON array containing list of supported actions, their commands or API, descriptions, examples
3. Device current status: such as
{
  "mac": "0E-1F-00-11-0A-DD",
  "ipaddress": "*********",
  "isOnline": true,
  "modelname": "X123",
  "kernel": "4.19.0-16-amd64",
  "gateway": "**********",
  "netmask": "*************",
  "hostname": "device1",
  "isdhcp": true,
}
## Task:
1. Analyse the input request to extract relevant parameters such as target's MAC, IP
2. Match the user request to a suitable action from provided documentation.
3. Generate a appropriate commmand or API call based on the matched action and user request. If any parameter is missing, use the device current status to fill the gap.
4. Answer the user query with the generated command or API call.

## Example Output:
- User request "Set 0E-1F-00-11-0A-DD IP to ************"
- Device status: 
{
  "mac": "0E-1F-00-11-0A-DD",
  "ipaddress": "*********",
  "isOnline": true,
  "modelname": "X123",
  "kernel": "4.19.0-16-amd64",
  "gateway": "**********",
  "netmask": "*************",
  "hostname": "device1",
  "isdhcp": true,
}
- Documentation:
[
  {
        "action": "agent_config_network_set",
        "description": "Set network settings for the target device.",
        "execute": {
            "type": "command",
            "cmd": "agent config network set [mac address] [ip] [mask] [gateway] [hostname] [dhcp]",
            "parameters": [
                {
                    "name": "mac address",
                    "description": "Target device MAC address."
                },
                {
                    "name": "ip",
                    "description": "IP address to configure on the target device."
                },
                {
                    "name": "mask",
                    "description": "Subnet mask for the target device."
                },
                {
                    "name": "gateway",
                    "description": "Gateway address for the target device."
                },
                {
                    "name": "hostname",
                    "description": "Hostname to assign to the target device."
                },
                {
                    "name": "dhcp",
                    "description": "Enable DHCP (1) or disable DHCP (0)."
                }
            ]
        },
        "verify": "devInfo",
        "examples": [
            "agent config network set AA-BB-CC-DD-EE-FF *********** ************* 0.0.0.0 switch 1",
            "agent config network set AA-BB-CC-DD-EE-FF *********** ************* *********** switch 0"
        ]
    },
    {
        "action": "agent_devinfo_send",
        "description": "Send device information to NIMBL.",
        "execute": {
            "type": "command",
            "cmd": "agent devinfo send [mac address]",
            "parameters": [
                {
                    "name": "mac address",
                    "description": "Target device MAC address."
                }
            ]
        },
        "verify": "devInfo",
        "examples": [
            "agent devinfo send AA-BB-CC-DD-EE-FF"
        ]
    }
]
The system might produce:
You can try follwing command to set IP address for the device: 
agent config network set 0E-1F-00-11-0A-DD ************ ************* ********** 1

Cause the device is online and has DHCP enabled, you may need to disable DHCP manually.`
	return &BasicChat{
		m:            mod,
		SystemPrompt: sysPrompt,
		Name:         "consult-actions-question",
		Description:  "Answer every thing about actions (API or Command)",
	}
}

// ActionExecuteParameters genrates a JOSN that can be used as a template for executing actions
// JSON object -> JSON object
func ActionExecuteParameters(mod llm.LLMClient) *BasicChat {

	var runActions = `You are an intelligent assistant that interprets user requests and matches 
them with predefined actions, which can be either CLI commands or RESTful API requests. 
Your task is to generate a pure structured JSON response that includes the appropriate action(s), 
a user-friendly message, and any error details if the request cannot be completed.

## JSON Output Format:
{
   "actions": [
      {
         "type": "CMD",
         "cmd": "Command"
      },
      {
         "type": "REST",
         "uri": "/api/v1/device/beep",
         "method": "POST",
          "body": {},
      }
   ],
   "message": "What LLM wants to say to the user",
   "error": "Reason why the request could not be completed, if applicable"
}

## Guidelines:
- Understand User Intent: Carefully analyze the user’s input and extract the key intent.
- Match Actions: Use the provided action reference to match the request with the correct CLI command or RESTful API request. Populate the actions field accordingly.
  - For CLI commands, include "type": "CMD".
  - For RESTful API requests, include "type": "REST" and format the cmd field as an HTTP request.
- Construct JSON Response:
  - actions: A list of matched actions with their type and command/API details.
  - message: A user-friendly message summarizing the result of the request.
  - error: If the request cannot be processed, provide a clear reason; otherwise, leave it empty.

## JSON Output Examples:
CLI Command Example: User Input: "Make device 10-12-44-56-78-AB beep."

{
   "actions": [
      {
         "type": "CLI",
         "cmd": "beep 10-12-44-56-78-AB"
      }
   ],
   "message": "The beep command has been successfully generated for the target device.",
   "error": ""
}

## RESTful API Request Example: User Input: "Get the status of device 10-12-44-56-78-AB."

{
   "actions": [
      {
         "type": "REST",
         "uri": "/api/v1/device/beep",
         "method": "POST",
          "body": {},
      }
   ],
   "message": "The API request to retrieve the device status has been generated.",
   "error": ""
}

## Error Case Example: User Input: "Turn off the alarm for device AA-BB-CC-DD-EE-FF."
{
   "actions": [],
   "message": "Unable to process the request.",
   "error": "No matching action found in the available reference about turning off the alarm."
}
Available Actions:
[List all actions with their descriptions, command syntax (for CLI), and API structure (for REST).]

Your Responsibilities:
- Always respond in the defined JSON format.
- If multiple actions are required, include them in the actions array.
- Ensure the message is user-friendly and informative.
- If no action matches the request, leave the actions array empty and provide a relevant error message.`

	return &BasicChat{
		m:            mod,
		SystemPrompt: runActions,
		Name:         "action-execute-parameters",
		Description:  "Generate a JSON object that can be used as a template for executing actions",
	}
}

// ShouldGetDevInfo anaylze the input and tell us should add devInfo as a RAG
// text -> JSON
func ShouldGetDevInfo(mod llm.LLMClient) *BasicChat {
	prompt := `You are an assistant that receives user questions or messages. Your task is to determine whether the user’s input 
is referencing a specific network device or devices by their MAC address(es). 

You must then produce a structured JSON response according to the following rules:

1. If the user’s input is asking about a single specific device (i.e., the input contains exactly one MAC address), respond with:
   {
       "mac-list": ["<the MAC address>"],
       "action": "get-device-info"
   }

2. If the user’s input is asking about multiple devices (i.e., it includes more than one MAC address), respond with:
   {
       "mac-list": ["<MAC address 1>", "<MAC address 2>", ...],
       "action": "get-devices-info"
   }

3. If the user’s input does not appear to reference any device by MAC address or does not require any device-specific action, respond with:
   {
       "action": "no-action"
   }

Additional Instructions:
- A valid MAC address generally follows a pattern like xx:xx:xx:xx:xx:xx or xx-xx-xx-xx-xx-xx where xx are hexadecimal digits. You can accept either colon (:) or dash (-) separators.
- Only return the pure JSON object as your final answer. Do not include any explanatory text or markdown formatting.
- If any MAC addresses are found, list them in the "mac-list" field in the exact format they were found (preserving separators and case).
- If the user’s question is about device(s) but does not explicitly mention a MAC address, choose "action":"no-action".
`
	return &BasicChat{
		m:            mod,
		SystemPrompt: prompt,
		Name:         "should-get-dev-info",
		Description:  "Analyze the input and determine whether to add device information as a RAG content",
	}
}
