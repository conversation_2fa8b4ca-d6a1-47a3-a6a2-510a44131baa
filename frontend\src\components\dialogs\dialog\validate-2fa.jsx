import { Form, Input, Modal } from "antd";
import React from "react";
import { useValidateSecret } from "../../../services/mutations";
import { useAuthStore } from "../../../store/auth-store";
import { useNavigate } from "@tanstack/react-router";

const DEFAULT_REDIRECT = "/dashboard/device";

const Validate2FA = ({ data, onClose }) => {
  const [form] = Form.useForm();
  const validateSecret = useValidateSecret();
  const navigate = useNavigate();
  const { setAuthData } = useAuthStore();

  const handleValidateCode = async (values) => {
    try {
      const resdata = await validateSecret.mutateAsync({
        sessionID: data.sessionID,
        code: values.c2fa,
      });
      await setAuthData(resdata);
      navigate({ to: DEFAULT_REDIRECT });
    } catch (error) {
      console.log(error);
    } finally {
      onClose();
    }
  };

  return (
    <Modal
      title="Validate Code"
      maskClosable={false}
      open
      destroyOnClose
      onCancel={onClose}
      okButtonProps={{ autoFocus: true, htmlType: "submit" }}
      style={{ top: 20 }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            handleValidateCode(values);
            form.resetFields();
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form
        form={form}
        layout="vertical"
        name="validate_2fa_form"
        clearOnDestroy
      >
        <div style={{ marginBottom: "10px" }}>
          Open Google Authenticator to get 2fa code
        </div>
        <Form.Item
          name="c2fa"
          label="Enter 2fa code"
          rules={[
            {
              required: true,
              message: "Please input your 2FA Code !",
            },
          ]}
        >
          <Input />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default React.memo(Validate2FA);
