import { Col, Modal, Row, Table } from "antd";
import React, { useCallback, useEffect } from "react";
import { useGetPortInfo } from "../../../services/queries";
import { usePortInfoColumns } from "../../table-column/portinfo-table";
import { useContextMenu } from "react-contexify";
import { MENU_IDS } from "../../context-menu/menu-items";
import { PortInfoContextMenu } from "../../../features/portinfo/components/PortInfoContextMenu";
import RealtimeTraffic from "../../../features/portinfo/components/RealtimeTraffic";
import { useTheme } from "antd-style";
import { DeviceInformation } from "../../../features/portinfo/components/DeviceInformation";
import { useTrafficData } from "../../../features/portinfo/hooks/useTrafficData";
import { theme } from "antd";

const PortInfoDialog = ({ data, onClose }) => {
  const { token } = theme.useToken();
  const { appearance } = useTheme();
  const { data: portInfoData } = useGetPortInfo(data.mac);
  const { show } = useContextMenu();
  const columns = usePortInfoColumns(token);
  const {
    chartState,
    updateTrafficData,
    resetChartState,
    setSelectedPort,
    setChartState,
  } = useTrafficData();

  useEffect(() => {
    if (portInfoData && data?.mac) {
      const portData = portInfoData[data.mac]?.portStatus || [];
      updateTrafficData(portData, chartState.selectedPort);
    }
  }, [portInfoData, chartState.selectedPort, data?.mac, updateTrafficData]);

  useEffect(() => {
    setChartState((prev) => ({
      ...prev,
      lineChartState: {
        ...prev.lineChartState,
        options: {
          ...prev.lineChartState.options,
          theme: { mode: appearance },
          chart: {
            ...prev.lineChartState.options.chart,
            background: token.colorBgContainer,
          },
        },
      },
    }));
  }, [token, appearance]);

  const handleClose = useCallback(() => {
    resetChartState();
    onClose();
  }, [onClose, resetChartState]);

  return (
    <Modal
      title={`Port and Power Information (${data.mac})`}
      maskClosable={false}
      open
      destroyOnClose
      onCancel={handleClose}
      footer={null}
      width="100%"
      style={{ top: 20 }}
      height="calc(100vh - 40px)"
    >
      {portInfoData && (
        <Row gutter={[10, 10]}>
          <Col xs={24} md={6}>
            <DeviceInformation
              data={data}
              portInfoData={portInfoData}
              token={token}
            />
          </Col>
          <Col xs={24} md={18}>
            <RealtimeTraffic
              portData={portInfoData[data?.mac]?.portStatus || []}
              selectedPort={chartState.selectedPort}
              setSelectedPort={setSelectedPort}
              lineChartState={chartState.lineChartState}
            />
          </Col>
          <Col xs={24} md={24}>
            <Table
              columns={columns}
              dataSource={portInfoData[data.mac]?.portStatus || []}
              size="small"
              rowKey="portName"
              scroll={{ x: 2021, y: 300 }}
              pagination={false}
              onRow={(record) => ({
                onContextMenu: async (event) => {
                  if (record) {
                    show({
                      id: MENU_IDS.PORT_INFO,
                      event,
                      props: { record: { ...record, mac: data.mac } },
                    });
                  }
                },
              })}
            />
            <PortInfoContextMenu />
          </Col>
        </Row>
      )}
    </Modal>
  );
};

export default React.memo(PortInfoDialog);
