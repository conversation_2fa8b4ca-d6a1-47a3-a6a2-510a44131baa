import { createFileRoute } from "@tanstack/react-router";
import { Row, Col } from "antd";
import { MibBrowserForm, CommandResultsList } from "../../features/mib-browser";

export const Route = createFileRoute("/_auth/mib-browser")({
  component: MibBrowserComponent,
});

/**
 * MIB Browser page component
 * @returns {JSX.Element} MIB Browser page
 */
function MibBrowserComponent() {
  return (
    <Row gutter={[16, 16]}>
      <Col xs={24} md={24} lg={12}>
        <MibBrowserForm />
      </Col>
      <Col xs={24} md={24} lg={12}>
        <CommandResultsList />
      </Col>
    </Row>
  );
}
