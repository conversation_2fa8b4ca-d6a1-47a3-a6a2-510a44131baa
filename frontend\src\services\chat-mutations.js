import { useMutation } from "@tanstack/react-query";
import { api } from "./api";

/**
 * Custom hook for sending chat messages
 * @returns {Object} Mutation object for sending chat messages
 */
export const useSendChatMessage = () => {
  return useMutation({
    mutationFn: (chatData) => api.sendChatMessage(chatData),
    onError: (error) => {
      console.error("Chat message failed:", error);
    },
  });
};
