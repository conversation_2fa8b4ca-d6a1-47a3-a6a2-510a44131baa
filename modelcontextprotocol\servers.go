package modelcontextprotocol

import (
	"fmt"
	"mnms/llm"
	"sync"

	"github.com/mark3labs/mcp-go/mcp"
	"go.uber.org/zap"
)

// runningClients is a map of runningClients that connect to MCP servers
// map key is the name of the server
var runningClients = make(map[string]MCPServer)

// runningClientMutex is a runningClientMutex for the runningClients map
var runningClientMutex = &sync.Mutex{}

// StartServer connects to a MCP server
func StartServer(serverName string) error {
	runningClientMutex.Lock()
	defer runningClientMutex.Unlock()

	server, err := GetMCPServerFromConfig(serverName)
	if err != nil {
		return err
	}

	err = server.InitializeMCPClient()
	if err != nil {
		return err
	}
	runningClients[serverName] = server
	zap.L().Debug("StartServer done", zap.String("server", serverName))
	return nil
}

// CloseServer disconnects from a MCP server
func CloseServer(serverName string) error {
	runningClientMutex.Lock()
	defer runningClientMutex.Unlock()

	c, ok := runningClients[serverName]
	if !ok {
		return fmt.Errorf("server %s not found", serverName)
	}
	err := c.Client.Close()
	if err != nil {
		return err
	}
	delete(runningClients, serverName)
	return nil
}

// CloseAllRunningServers closes all running MCP servers
func CloseAllRunningServers() {
	runningClientMutex.Lock()
	defer runningClientMutex.Unlock()

	for _, server := range runningClients {
		err := server.Client.Close()
		if err != nil {
			fmt.Printf("error closing server %s: %s", server.Name, err)
		}
		zap.L().Info("closed server", zap.String("server", server.Name))
	}
	runningClients = make(map[string]MCPServer)
}

// IsServerRunning checks if a MCP server is running
func IsServerRunning(serverName string) bool {
	runningClientMutex.Lock()
	defer runningClientMutex.Unlock()

	_, ok := runningClients[serverName]
	return ok
}

// ListTools lists all tools available on a MCP server
func ListTools(serverName string) ([]mcp.Tool, error) {
	server, ok := runningClients[serverName]
	if !ok {
		return nil, fmt.Errorf("server %s not found", serverName)
	}
	tools, err := server.ListTools()
	if err != nil {
		return nil, err
	}
	return tools, nil
}

// MCPToolsToAnthropicTools converts MCP tools to Anthropic tools
func MCPToolsToAnthropicTools(serverName string, mcpTools []mcp.Tool) []llm.Tool {
	anthropicTools := make([]llm.Tool, len(mcpTools))

	for i, tool := range mcpTools {
		namespacedName := fmt.Sprintf("%s__%s", serverName, tool.Name)

		anthropicTools[i] = llm.Tool{
			Name:        namespacedName,
			Description: tool.Description,
			InputSchema: llm.Schema{
				Type:       tool.InputSchema.Type,
				Properties: tool.InputSchema.Properties,
				Required:   tool.InputSchema.Required,
			},
		}
	}

	return anthropicTools
}
