package node

import (
	"context"
	"encoding/json"
	"fmt"

	"mnms/llm"
	"mnms/llm/history"
	"mnms/modelcontextprotocol"
	"os"
	"path"
	"strings"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/qeof/q"
)

// NodesFolder
const NodesFolder = "nodes"

// Node is an abstract object used to describe a node in an AI agent project. A node can:
// - have one or more string inputs, all inputs will be concatenated into a single string separated by \n
// - have one or more document inputs, all inputs will be concatenated into a single string separated by \n, with ## Relevant Documents: added at the beginning
// - output a string or formatted string
type Node struct {
	Name                  string                   `json:"name"`
	Description           string                   `json:"description"`
	OutputFormat          string                   `json:"output_format"`
	SystemPrompt          string                   `json:"system_prompt"`
	JSONSchema            llm.JSONSchemaDefinition `json:"json_schema"`
	RelevantDocCategories []string                 `json:"relevant_doc_categories"`
	NumberRelevantDocs    int                      `json:"number_relevant_docs,omitempty"`
	RelevantSimilarity    float32                  `json:"relevant_similarity,omitempty"`
	Functions             []string                 `json:"functions"`
	MCPServers            []string                 `json:"mcp_servers"`
	// Position of the node in the flow diagram, used for visualization
	Position Position `json:"position"`
	ID       string   `json:"id"`
	Type     string   `json:"type"`
	Data     struct {
		Label string `json:"label"`
	} `json:"data"`
	MixOutput        string         `json:"mix_output"`
	WaitingToolCalls []llm.ToolCall `json:"-"`
}

// NewNode creates a new node
func NewNode(name string) *Node {
	return &Node{
		Name:               name,
		Description:        "",
		NumberRelevantDocs: 5,
		RelevantSimilarity: 0.75,
		Position: Position{
			X: 0.0,
			Y: 0.0,
		},
		SystemPrompt: "",
		OutputFormat: "text",
		Type:         "agentNode",
		MixOutput:    "output",
		Functions:    []string{},
	}
}

// Output generate real output,
// append: convert output to text and append to input
// output: output the output
// pass: output the input
// default: output
func (f *Node) Output(input string, output RunNodeOutput) RunNodeOutput {
	outText := output.String()
	switch f.MixOutput {
	case "append":
		return RunNodeOutput{
			Format: "text",
			Data:   input + "\n" + outText,
		}
	case "output":
		return RunNodeOutput{
			Format: output.Format,
			Data:   output.Data,
		}
	case "pass":
		return RunNodeOutput{
			Format: "text",
			Data:   input,
		}
	case "reference":
		data := fmt.Sprintf("Input:\n%s\n Reference:\n%s", input, outText)
		return RunNodeOutput{
			Format: "text",
			Data:   data,
		}

	default:
		return RunNodeOutput{
			Format: output.Format,
			Data:   output.Data,
		}
	}
}

// Json	returns the node json
func (n *Node) Json() []byte {
	jsonBytes, _ := json.Marshal(n)
	return jsonBytes
}

// Info returns the node info
func (n *Node) Info() NodeInfo {
	return NodeInfo{
		ID:          n.ID,
		Name:        n.Name,
		Description: n.Description,
	}
}

// GetRelevantDocumentInfo returns the relevant document information
func (n *Node) GetRelevantDocumentInfo() NodeRelevantDocInfo {
	return NodeRelevantDocInfo{
		RelevantDocCategories: n.RelevantDocCategories,
		NumberRelevantDocs:    n.NumberRelevantDocs,
		RelevantSimilarity:    n.RelevantSimilarity,
	}
}

// SetMeta sets the node metadata
func (n *Node) SetMeta(settings map[string]interface{}) error {
	return fmt.Errorf("Node does not support metadata")
}

// GetMeta returns the node metadata
func (n *Node) GetMeta() map[string]interface{} {
	// convert node to map
	nodeMap := make(map[string]interface{})
	jsonBytes, _ := json.Marshal(n)
	json.Unmarshal(jsonBytes, &nodeMap)
	return nodeMap
}

// NodeSummary is a summary of a node
type NodeSummary struct {
	Name        string `json:"name"`
	Description string `json:"description"`
}

// GetSavedNodesSummary get all saved nodes summary
func GetSavedNodesSummary() ([]*NodeSummary, error) {

	// read all nodes from nodes folder
	files, err := os.ReadDir(NodesFolder)
	if err != nil {
		return nil, fmt.Errorf("read nodes folder fail: %s", err.Error())
	}
	var nodesSummary []*NodeSummary

	for _, file := range files {
		n, err := ImportNode(file.Name())
		if err != nil {
			return nil, fmt.Errorf("import node %s fail: %s", file.Name(), err.Error())
		}
		info := n.Info()
		nodesSummary = append(nodesSummary, &NodeSummary{
			Name:        info.Name,
			Description: info.Description,
		})
	}

	return nodesSummary, nil
}

// GetSavedNodes get all saved nodes
func GetSavedNodes() ([]NodeProvider, error) {
	// read all nodes from nodes folder
	files, err := os.ReadDir(NodesFolder)
	if err != nil {
		return nil, fmt.Errorf("read nodes folder fail: %s", err.Error())
	}
	// unmarshal nodes
	nodes := make([]NodeProvider, 0)
	for _, file := range files {
		node, err := ImportNode(file.Name())
		if err != nil {
			return nil, fmt.Errorf("import node %s fail: %s", file.Name(), err.Error())
		}
		nodes = append(nodes, &node)
	}

	return nodes, nil
}

// writenode write node to file
func writenode(data []byte, filename string) error {
	// make sure nodes folder exists
	err := os.MkdirAll(NodesFolder, 0755)
	if err != nil {
		return fmt.Errorf("create nodes folder fail: %s", err.Error())
	}
	// write to file
	filepath := path.Join(NodesFolder, filename)
	err = os.WriteFile(filepath, data, 0644)
	if err != nil {
		return fmt.Errorf("write node %s fail: %s", filename, err.Error())
	}
	return nil
}

// Export export node to nodes folder
func (n *Node) Export() (string, error) {
	// export node to nodes folder file name is node name
	jsonBytes, err := json.Marshal(n)
	if err != nil {
		return "", fmt.Errorf("export node %s fail: %s", n.Name, err.Error())
	}
	// write to file
	filename := n.Name + ".json"
	err = writenode(jsonBytes, filename)
	if err != nil {
		return "", fmt.Errorf("export node %s fail: %s", n.Name, err.Error())
	}
	return filename, nil
}

func ImportNode(filename string) (Node, error) {
	// if filename is not json file, append .json
	if path.Ext(filename) != ".json" {
		filename += ".json"
	}
	// read node from file
	filepath := path.Join(NodesFolder, filename)
	data, err := os.ReadFile(filepath)
	if err != nil {
		return Node{}, fmt.Errorf("read node %s fail: %s", filename, err.Error())
	}
	// unmarshal node
	node := Node{}
	err = json.Unmarshal(data, &node)
	if err != nil {
		return Node{}, fmt.Errorf("unmarshal node %s fail: %s", filename, err.Error())
	}
	return node, nil
}

// DeleteNode delete node from nodes folder
func DeleteNode(filename string) error {
	// if filename is not json file, append .json
	if path.Ext(filename) != ".json" {
		filename += ".json"
	}
	// delete node file
	filepath := path.Join(NodesFolder, filename)
	err := os.Remove(filepath)
	if err != nil {
		return fmt.Errorf("delete node %s fail: %s", filename, err.Error())
	}
	return nil
}

// Run
func (n *Node) Run(ctx context.Context, llmClient llm.Completer, relevantDoc string, query string) (*RunNodeOutput, error) {

	userPrompt := query + "\n" + relevantDoc
	switch n.OutputFormat {
	case "text":
		resulr, err := llmClient.Complete(ctx, []llm.Message{
			{
				Role:    "system",
				Content: n.SystemPrompt,
			},
			{
				Role:    "user",
				Content: userPrompt,
			},
		})
		if err != nil {
			return nil, err
		}
		return &RunNodeOutput{
			Format: "text",
			Data:   resulr,
		}, nil

	case "json":
		result, err := llmClient.FormattedOutputWithSchema(ctx, []llm.Message{
			{
				Role:    "system",
				Content: n.SystemPrompt,
			},
			{
				Role:    "user",
				Content: userPrompt,
			},
		}, n.Description, n.Name, n.JSONSchema)
		if err != nil {
			return nil, err
		}

		ret := &RunNodeOutput{
			Format: "json",
			Data:   result,
		}

		return ret, nil

	default:
		return nil, fmt.Errorf("unsupported output format %s", n.OutputFormat)

	}
}

// addRelevantDoc adds the relevant document to the prompt
func addRelevantDoc(prompt string, relevantDoc string) string {
	if relevantDoc == "" {
		return prompt
	}

	if prompt == "" {
		return relevantDoc
	}

	return prompt + "\n\n" + relevantDoc
}

// RunPrompt runs the node with the given prompt this function will check MCP servers and tools automatically
func (n *Node) RunPrompt(
	ctx context.Context,
	provider llm.Provider,
	relevantDoc string,
	query string,
	messages *[]history.HistoryMessage,
	msgCallback func(kind, msg string),
	autoMode bool,
) (string, error) {
	q.Q("RunPrompt", query, relevantDoc)

	// Deal with nil callback
	var callback = msgCallback
	if msgCallback == nil {
		callback = func(kind, msg string) {
			q.Q("callback message", kind, msg)
		}
	}

	// Set system prompt if available
	if n.SystemPrompt != "" {
		provider.SetSystemPrompt(n.SystemPrompt)
	}
	// Add user query to messages if not empty
	if query != "" {
		*messages = append(*messages, history.HistoryMessage{
			Role: "user",
			Content: []history.ContentBlock{{
				Type: "text",
				Text: query,
			}},
		})
	}

	// Initialize MCP servers and get tools
	allTools, err := n.initializeMCPServers(callback)
	if err != nil {
		return "", fmt.Errorf("failed to initialize MCP servers: %w", err)
	}

	q.Q("History messages", len(*messages))

	// Convert history messages to LLM messages
	llmMessages := make([]llm.IMessage, len(*messages))
	for i := range *messages {
		llmMessages[i] = &(*messages)[i]
	}

	// Prepare current prompt with relevant document
	var currentPrompt string
	if relevantDoc != "" {
		currentPrompt = addRelevantDoc("", relevantDoc)
	}

	// Get LLM response
	llmReturnMessage, err := provider.CreateMessage(ctx, currentPrompt, llmMessages, allTools)
	if err != nil {
		return err.Error(), err
	}

	// Process content and tool calls separately
	var messageContent []history.ContentBlock

	// Add text content if available
	if llmReturnMessage.GetContent() != "" {
		messageContent = append(messageContent, history.ContentBlock{
			Type: "text",
			Text: llmReturnMessage.GetContent(),
		})
	}

	toolCalls := llmReturnMessage.GetToolCalls()

	// **********************************
	// ** Man in the loop process
	// **********************************
	if !autoMode && len(toolCalls) > 0 {
		n.WaitingToolCalls = toolCalls
		toolCallsJSON, _ := llm.GetToolCallsJSON(toolCalls)
		callback("tool_call_permit", string(toolCallsJSON))
		return "", nil
	}

	// **********************************
	// ** Process tool calls
	// **********************************
	toolUseContent, toolResults := n.ProcessToolCalls(ctx, toolCalls, []llm.ToolCall{}, callback)
	if len(toolUseContent) != len(toolResults) {
		callback("error", fmt.Sprintf("tool use content and tool results length mismatch: %d != %d", len(toolUseContent), len(toolResults)))
		return "", fmt.Errorf("tool use content and tool results length mismatch: %d != %d", len(toolUseContent), len(toolResults))
	}

	// Handle tool results
	if len(toolResults) > 0 {
		messageContent = append(messageContent, toolUseContent...)

		// Add assistant's message to history
		*messages = append(*messages, history.HistoryMessage{
			Role:    llmReturnMessage.GetRole(),
			Content: messageContent,
		})
		*messages = append(*messages, history.HistoryMessage{
			Role:    "user",
			Content: toolResults,
		})
		callback("info", fmt.Sprintf("%s is thinking...\n", provider.Name()))
		// got tool results, send to LLM again
		return n.RunPrompt(ctx, provider, "", "", messages, callback, autoMode)
	}

	return llmReturnMessage.GetContent(), nil
}

// RunFunctions runs the function with the given name
func (n *Node) RunFunctions() []string {
	return n.Functions
}

type SpecialNodeType string

const (
	NodeStart SpecialNodeType = "start"
	NodeEnd   SpecialNodeType = "end"
)

type SpecialNode struct {
	Node
	Type SpecialNodeType `json:"type"`
}

// Run
func (n *SpecialNode) Run(ctx context.Context, llmClient llm.Completer, relevantDoc string, query string) (string, error) {
	switch n.Type {
	case NodeStart:
		return query, nil
	case NodeEnd:
		return query, nil
	default:
		return "", fmt.Errorf("unsupported special node type %s", n.Type)
	}
}

// initializeMCPServers initializes MCP servers and returns available tools
func (n *Node) initializeMCPServers(callback func(kind, msg string)) ([]llm.Tool, error) {
	var allTools []llm.Tool

	servers, err := modelcontextprotocol.GetRunningMCPServers(n.MCPServers)
	if err != nil {
		callback("error", fmt.Sprintf("get mcp servers fail: %s\n", err.Error()))
		return nil, fmt.Errorf("get running mcp servers fail: %w", err)
	}

	for _, server := range servers {
		callback("info", fmt.Sprintf("Getting %s tools\n", server.Name))
		tools, err := modelcontextprotocol.ListTools(server.Name)
		if err != nil {
			callback("error", fmt.Sprintf("list %s tools fail: %s\n", server.Name, err.Error()))
			continue
		}
		t := modelcontextprotocol.MCPToolsToAnthropicTools(server.Name, tools)
		allTools = append(allTools, t...)
	}

	return allTools, nil
}

// ProcessToolCalls handles all tool calls in the LLM response
func (n *Node) ProcessToolCalls(
	ctx context.Context,
	toolCalls []llm.ToolCall,
	rejectToolCalls []llm.ToolCall,
	callback func(kind, msg string),
) ([]history.ContentBlock, []history.ContentBlock) {
	var messageContent []history.ContentBlock
	var toolResults []history.ContentBlock

	// handle reject tool calls
	for _, toolCall := range rejectToolCalls {
		input, _ := json.Marshal(toolCall.GetArguments())
		messageContent = append(messageContent, history.ContentBlock{
			Type:  "tool_use",
			ID:    toolCall.GetID(),
			Name:  toolCall.GetName(),
			Input: input,
		})
		q.Q("tool call rejected", toolCall.GetName())
		toolResults = append(toolResults, history.ContentBlock{
			Type:      "tool_result",
			ID:        toolCall.GetID(),
			ToolUseID: toolCall.GetID(),
			Content: []history.ContentBlock{{
				Type: "text",
				Text: fmt.Sprintf("User rejected tool: %s \n", toolCall.GetName()),
			}},
		})
	}

	// Handle tool calls
	for _, toolCall := range toolCalls {
		// Add tool call to message content
		input, _ := json.Marshal(toolCall.GetArguments())
		messageContent = append(messageContent, history.ContentBlock{
			Type:  "tool_use",
			ID:    toolCall.GetID(),
			Name:  toolCall.GetName(),
			Input: input,
		})

		callback("tool", fmt.Sprintf("Using tool: %s \n", toolCall.GetName()))

		resultBlock, err := n.handleToolCall(ctx, toolCall, callback)
		// Add tool call result to the message content (toolResults)
		if err != nil {
			q.Q("handle tool call failed", err)
			// Add error message to message content
			toolResults = append(toolResults, history.ContentBlock{
				Type:      "tool_result",
				ID:        toolCall.GetID(),
				ToolUseID: toolCall.GetID(),
				Content: []history.ContentBlock{{
					Type: "text",
					Text: fmt.Sprintf("Error calling tool %s: %s", toolCall.GetName(), err.Error()),
				}},
			})
			continue
		}
		if resultBlock.Type != "" {
			toolResults = append(toolResults, resultBlock)
		}
	}

	return messageContent, toolResults
}

// handleToolCall processes a single tool call from the LLM and returns the result
func (n *Node) handleToolCall(
	ctx context.Context,
	toolCall llm.ToolCall,
	callback func(kind, msg string),
) (history.ContentBlock, error) {
	// Marshal tool arguments to JSON for processing
	toolCallArgsJSON, _ := json.Marshal(toolCall.GetArguments())

	// Parse tool name to extract server and tool information
	// Format expected: "serverName__toolName"
	parts := strings.Split(toolCall.GetName(), "__")
	if len(parts) != 2 {
		return history.ContentBlock{}, fmt.Errorf("invalid tool name format: %s", toolCall.GetName())
	}

	// Extract server name and tool name from parsed parts
	serverName, toolName := parts[0], parts[1]

	// Get running MCP server instance for the specified server name
	servers, err := modelcontextprotocol.GetRunningMCPServers([]string{serverName})
	if err != nil {
		callback("error", fmt.Sprintf("get %s server fail: %s\n", serverName, err.Error()))
		return history.ContentBlock{}, fmt.Errorf("get running mcp server fail: %w", err)
	}
	if len(servers) == 0 {
		callback("error", fmt.Sprintf("server %s not running\n", serverName))
		return history.ContentBlock{}, fmt.Errorf("server %s not running", serverName)
	}
	server := servers[0]

	// Unmarshal tool arguments from JSON to map for tool call
	var toolArgs map[string]any
	if err := json.Unmarshal(toolCallArgsJSON, &toolArgs); err != nil {
		callback("error", fmt.Sprintf("unmarshal tool call input fail: %s\n", err.Error()))
		return history.ContentBlock{}, fmt.Errorf("unmarshal tool call input fail: %w", err)
	}

	// Initialize SSE connection with MCP server
	if err := server.SseClientStart(); err != nil {
		callback("error", fmt.Sprintf("start SSE MCP client %s fail: %s\n", serverName, err.Error()))
		return history.ContentBlock{
			Type:      "tool_result",
			ID:        toolCall.GetID(),
			ToolUseID: toolCall.GetID(),
			Content: []history.ContentBlock{{
				Type: "text",
				Text: fmt.Sprintf("Error calling tool %s: %s", toolName, err.Error())}},
		}, nil
	}

	// Prepare tool call request with parsed arguments
	req := mcp.CallToolRequest{
		Params: struct {
			Name      string         `json:"name"`
			Arguments map[string]any `json:"arguments,omitempty"`
			Meta      *struct {
				ProgressToken mcp.ProgressToken `json:"progressToken,omitempty"`
			} `json:"_meta,omitempty"`
		}{
			Name:      toolName,
			Arguments: toolArgs,
			Meta:      nil,
		},
	}

	// CallTool returns a pointer to a ToolResult struct containing:
	// - Content: []any - Array of content items, each can be cast to mcp.TextContent
	//   to access the Text field containing the tool's output text
	// - Error: *mcp.Error - Error details if the tool call failed
	// - Meta: *struct{} - Optional metadata about the tool execution
	toolResultPtr, err := server.Client.CallTool(ctx, req)
	if err != nil {
		callback("error", fmt.Sprintf("call tool %s fail: %s\n", toolName, err.Error()))
		return history.ContentBlock{
			Type:      "tool_result",
			ID:        toolCall.GetID(),
			ToolUseID: toolCall.GetID(),
			Content: []history.ContentBlock{{
				Type: "text",
				Text: fmt.Sprintf("Error calling tool %s: %s", toolName, err.Error())}},
		}, nil
	}

	toolResult := *toolResultPtr
	if toolResult.Content == nil {
		return history.ContentBlock{}, nil
	}

	resultBlock := history.ContentBlock{
		Type:      "tool_result",
		ToolUseID: toolCall.GetID(),
		Content:   toolResult.Content,
	}

	var resultText string
	for _, item := range toolResult.Content {
		if content, ok := item.(mcp.TextContent); ok && content.Text != "" {
			resultText += content.Text
		}
	}

	resultBlock.Text = strings.TrimSpace(resultText)
	return resultBlock, nil
}
