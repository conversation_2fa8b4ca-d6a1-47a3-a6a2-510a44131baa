package node

import (
	"fmt"
	"os"
	"os/exec"
)

func checkDependency(module string) error {
	cmd := exec.Command("python", "-c", fmt.Sprintf("import %s", module))
	if err := cmd.Run(); err != nil {
		return fmt.<PERSON>rrorf("import %s", module)
	}
	return nil
}

var envName = "mnmsenv"

// ensureVirtualEnv checks if a virtual environment with the given name exists.
// If it does not exist, it creates one using "virtualenv".
func ensureVirtualEnv() error {
	// Check if the virtual environment directory exists.
	if _, err := os.Stat(envName); os.IsNotExist(err) {
		// Create the virtual environment.
		// Ensure that "virtualenv" is installed and available in PATH.
		cmd := exec.Command("virtualenv", envName)
		output, err := cmd.CombinedOutput()
		if err != nil {
			return fmt.Errorf("failed to create virtualenv %s: %v\nOutput: %s", envName, err, output)
		}
		fmt.Printf("Virtualenv %s created successfully.\n", envName)
	} else if err != nil {
		return fmt.Errorf("error checking virtualenv %s: %v", envName, err)
	}
	return nil
}
