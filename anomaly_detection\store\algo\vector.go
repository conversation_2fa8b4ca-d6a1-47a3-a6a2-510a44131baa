package algo

import (
	"math"
)

// This file contains vector algorithms.

// DistanceCalculator defines the interface for any type that can calculate
// the distance between two vectors.
type DistanceCalculator interface {
	Calculate(a, b []float32) float32
}

// distanceAlgorithmFun
type distanceAlgorithmFun func(a, b []float32) float32

type CosineSimilarityCalculator struct{}

// cosinSimilarity calculate cosin similarity 1 is same 0 is totally different
func (CosineSimilarityCalculator) Calculate(a, b []float32) float32 {
	if len(a) != len(b) {
		return 1.0
	}
	// calculate dot product
	var dot float32
	for i := range a {
		dot += a[i] * b[i]
	}
	// calculate magnitude
	var magA, magB float32
	for i := range a {
		magA += a[i] * a[i]
		magB += b[i] * b[i]
	}
	magA = float32(math.Sqrt(float64(magA)))
	magB = float32(math.Sqrt(float64(magB)))

	// calculate cosin similarity
	return 1 - dot/(magA*magB)
}

// L2DistanceCalculator calculates the L2 distance between two vectors.
type L2DistanceCalculator struct{}

// l2distance calculate l2 distance
func (L2DistanceCalculator) Calculate(a, b []float32) float32 {
	if len(a) != len(b) {
		return 1.0
	}

	var sum float32
	for i := range a {
		diff := a[i] - b[i]
		sum += diff * diff
	}

	return float32(math.Sqrt(float64(sum)))
}
