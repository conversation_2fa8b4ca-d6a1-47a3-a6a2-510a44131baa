package llm

import (
	"context"
	"fmt"

	"github.com/qeof/q"
)

// ValidateLLMConnection
func ValidateLLMConnection(llmSever CompleteEmbedder) error {
	q.Q("ValidateLLMConnection")
	ret, err := llmSever.Complete(context.Background(), []Message{
		{
			Role:    "user",
			Content: "test",
		},
	})
	if err != nil {
		return err
	}
	q.Q("ValidateLLMConnection", ret)
	return nil
}

// ConnectToOllama is a function that constructs and returns a instance that implement CompleteEmbedder.
func ConnectToOllama(host string, model string, port int) (LLMClient, error) {
	return NewOllama(host, model, port), nil
}

// NewLLMClient is a function that constructs and returns a instance that implement CompleteEmbedder.
func NewLLMClient(llmSettings *LargeLanguageModel) (LLMClient, error) {
	switch llmSettings.Type {
	case "open-ai":
		return NewOpenAI(llmSettings.GetAPIKey(), llmSettings.Model), nil
	case "ollama":
		return NewOllama(llmSettings.Host, llmSettings.Model, llmSettings.Port), nil
	default:
		return nil, fmt.Errorf("unknown LLM server name (support open-ai | ollama )%s", llmSettings.Type)
	}
}
