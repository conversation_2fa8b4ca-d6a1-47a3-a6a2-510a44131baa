package actions

// aiagent/actions package provides Nimbl action definitions and processing capabilities.
// action is a struct that represents an command or API call.

import (
	"context"
	_ "embed"
	"encoding/json"
	"fmt"
	"mnms/aiagent/chat"
	"mnms/aiagent/documents/def"
	"mnms/aiagent/jsonutils"
	"mnms/llm"
	"os/exec"
	"strings"
	"sync"

	"github.com/qeof/q"
)

//go:embed data/actions.json
var predefinedActionsData []byte

// ActionDefinition represents a single action definition
type ActionDefinition struct {
	Action      string      `json:"action"`      // A unique identifier for the action
	Description string      `json:"description"` // A description of the action
	Examples    []string    `json:"examples"`    // Examples of the action
	Execute     ExecuteInfo `json:"execute"`     // The information needed to execute the action
}

// String returns the JSON string representation of the action
func (action ActionDefinition) String() string {
	data, err := json.Marshal(action)
	if err != nil {
		return err.Error()
	}
	return string(data)
}

// Parameter represents a single parameter for a command
type Parameter struct {
	Name        string `json:"name"`        // The name of the parameter
	Description string `json:"description"` // A description of the parameter
}

// ExecuteInfo represents the information needed to execute an action
type ExecuteInfo struct {
	Type       string                 `json:"type"`       // The type of the action
	Cmd        string                 `json:"cmd"`        // The CLI command to execute
	Parameters []Parameter            `json:"parameters"` // The parameters for the action
	URI        string                 `json:"uri"`        // The URI for the action
	Method     string                 `json:"method"`     // The HTTP method for the action
	Body       map[string]interface{} `json:"body"`       // The body for the action
}

// ActionDefinitions represents a list of action definitions
type ActionDefinitions struct {
	mu      sync.Mutex
	Actions []ActionDefinition
}

// NewActionDefinitions creates a new ActionDefinitions object
func NewActionDefinitions() *ActionDefinitions {
	return &ActionDefinitions{
		Actions: []ActionDefinition{},
		mu:      sync.Mutex{},
	}
}

// Updater implements the Updater interface
func (actDefs *ActionDefinitions) Update(_, data string) error {
	var action ActionDefinition
	err := json.Unmarshal([]byte(data), &action)
	if err != nil {
		return fmt.Errorf("input data %s is not a valid ActionDefinition: %v", data, err)
	}
	actDefs.Upsert(action)
	return nil
}

// Upsert add a new action to the list
func (actDefs *ActionDefinitions) Upsert(action ActionDefinition) {
	actDefs.mu.Lock()
	defer actDefs.mu.Unlock()
	// check if the action already exists then update it
	for i, act := range actDefs.Actions {
		if act.Action == action.Action {
			actDefs.Actions[i] = action

		}
	}
	actDefs.Actions = append(*&actDefs.Actions, action)

}

// GetActions returns the list of actions with the given pagination
func (actDefs *ActionDefinitions) GetActions(page int, pageSize int) ([]ActionDefinition, error) {

	// check page size
	if pageSize <= 0 {
		return nil, fmt.Errorf("page size must be greater than 0")
	}

	start := (page - 1) * pageSize
	end := start + pageSize
	if start >= len(actDefs.Actions) {
		return nil, fmt.Errorf("page %d is out of range", page)
	}
	if end > len(actDefs.Actions) {
		end = len(actDefs.Actions)
	}
	return actDefs.Actions[start:end], nil
}

// Serialize 		serialize the predefined actions
func (actDefs *ActionDefinitions) Serialize() ([]byte, error) {
	return json.Marshal(actDefs.Actions)
}

// Deserialize reads the predefined actions from the given file
func (actDefs *ActionDefinitions) Deserialize(data []byte) error {
	actDefs.mu.Lock()
	defer actDefs.mu.Unlock()
	var actions []ActionDefinition
	err := json.Unmarshal(data, &actions)
	if err != nil {
		return err
	}
	actDefs.Actions = actions
	return nil
}

// DeserializeActionDefineds reads the predefined actions from the given file
func DeserializeActionDefineds(data []byte) (*ActionDefinitions, error) {

	var actions []ActionDefinition
	err := json.Unmarshal(data, &actions)
	if err != nil {
		return nil, err
	}
	return &ActionDefinitions{
		Actions: actions,
		mu:      sync.Mutex{},
	}, nil
}

// GetPredefinedActions initializes the predefined actions
func GetPredefinedActions() (*ActionDefinitions, error) {
	return DeserializeActionDefineds(predefinedActionsData)
}

// AddDocumentAsAction add a command or API document text and put into the supported actions
func (actDefs *ActionDefinitions) AddDocumentAsAction(ctx context.Context, doc string, client llm.LLMClient) (*ActionDefinition, error) {
	// create chat
	llmCaht := chat.Doc2ActionDefine(client)
	// get the response
	res := llmCaht.Talk(ctx, doc)
	if res.Error != nil {
		return nil, res.Error
	}
	jsonStr, err := jsonutils.TrimToEnclosure(res.Content)
	if err != nil {

		return nil, fmt.Errorf("%s is not a valid JSON", res.Content)
	}
	q.Q(jsonStr)
	// marshal the response
	var action ActionDefinition
	err = json.Unmarshal([]byte(jsonStr), &action)
	if err != nil {
		//can't unmarshal the response try to get JSON from response
		extraJson := chat.NormalizeJSON(client)
		res = extraJson.Talk(ctx, res.Content)
		if res.Error != nil {
			return nil, res.Error
		}
		err = json.Unmarshal([]byte(res.Content), &action)
		if err != nil {
			return nil, err
		}
	}
	actDefs.Upsert(action)
	return &action, nil

}

// DeleteAction delete an action from the list
func (actDefs *ActionDefinitions) DeleteAction(actionID string) error {
	actDefs.mu.Lock()
	defer actDefs.mu.Unlock()
	for i, action := range actDefs.Actions {
		if action.Action == actionID {
			actDefs.Actions = append(actDefs.Actions[:i], actDefs.Actions[i+1:]...)
			return nil
		}
	}
	return fmt.Errorf("action %s not found", actionID)
}

// FindAction finds the action with the given name
func (actDefs *ActionDefinitions) FindAction(act string) ([]ActionDefinition, error) {
	// fuzzy search
	var foundActions []ActionDefinition
	for _, action := range actDefs.Actions {
		if strings.Contains(strings.ToLower(action.Action), strings.ToLower(act)) {
			foundActions = append(foundActions, action)
		}
	}
	if len(foundActions) == 0 {
		return nil, fmt.Errorf("no actions found matching: %s", act)
	}
	return foundActions, nil

}

// Implement the document provider interface
// Delete the document by its ID
func (actDefs *ActionDefinitions) Delete(id string) error {
	return actDefs.DeleteAction(id)
}

// Implements the document provider interface
// Find the document by its ID
func (actDefs *ActionDefinitions) Find(id string) (string, error) {
	for _, action := range actDefs.Actions {
		if action.Action == id {
			return action.String(), nil
		}
	}
	return "", fmt.Errorf("action %s not found", id)
}

// GetName returns the name of the document provider
func (actDefs *ActionDefinitions) GetName() string {
	return "actions"
}

// GetDescription returns the description of the document provider
func (actDefs *ActionDefinitions) GetDescription() string {
	return "The Action Definitions document is a concise guide to all CLI commands and APIs, providing details for seamless system integration and automation."
}

// Query
func (actDefs *ActionDefinitions) Query(input string) ([]string, error) {
	var actions []ActionDefinition
	for _, action := range actDefs.Actions {
		// fuzzy compare .Action .Description .Examples
		if strings.Contains(strings.ToLower(action.Action), strings.ToLower(input)) ||
			strings.Contains(strings.ToLower(action.Description), strings.ToLower(input)) {
			actions = append(actions, action)
		}
	}
	if len(actions) == 0 {
		return nil, fmt.Errorf("no actions found matching: %s", input)
	}
	var result []string
	for _, action := range actions {
		result = append(result, action.String())
	}
	return result, nil
}

// GetPage returns the page of the document provider
func (actDefs *ActionDefinitions) GetPage(page, pagesize int) ([]def.DocumentItem, error) {
	actions, err := actDefs.GetActions(page, pagesize)
	if err != nil {
		return nil, err
	}
	result := make([]def.DocumentItem, 0)
	for _, action := range actions {
		result = append(result, def.DocumentItem{
			ID:      action.Action,
			Content: action.String()})
	}
	return result, nil
}

// TotalCount
func (actDefs *ActionDefinitions) TotalCount() int {
	return len(actDefs.Actions)
}

// Action represents a single action from the JSON input
type Action struct {
	Type string `json:"type"` // CLI or REST
	Cmd  string `json:"cmd"`  // Command to execute
}

// ActionRequest represents the JSON structure
type ActionRequest struct {
	Actions []Action `json:"actions"`
	Message string   `json:"message"`
	Error   string   `json:"error"`
}

// ProcessActions processes the actions described in the JSON input
func ProcessActions(inputJSON string) {
	// Unmarshal the JSON string into the ActionRequest struct
	var actionRequest ActionRequest
	err := json.Unmarshal([]byte(inputJSON), &actionRequest)
	if err != nil {
		fmt.Printf("Error unmarshalling JSON: %v\n", err)
		return
	}

	// Check for errors in the JSON input
	if actionRequest.Error != "" {
		fmt.Printf("Error in action request: %s\n", actionRequest.Error)
		return
	}

	// Process each action
	for _, action := range actionRequest.Actions {
		fmt.Printf("Processing action: %s\n", action.Cmd)

		if action.Type == "CLI" {
			// Execute the CLI command
			output, err := exec.Command("/bin/sh", "-c", action.Cmd).CombinedOutput()
			if err != nil {
				fmt.Printf("Error executing command: %v\n", err)
				continue
			}
			fmt.Printf("Command output: %s\n", output)
		} else if action.Type == "REST" {
			// Handle RESTful requests (to be implemented based on specific requirements)
			fmt.Printf("REST action detected. Command: %s\n", action.Cmd)
		} else {
			fmt.Printf("Unknown action type: %s\n", action.Type)
		}
	}
}
