import React, { memo, useMemo } from "react";
import { theme } from "antd";
import ProTable from "@ant-design/pro-table";
import { useSSHTunnelColumn } from "../../../components/table-column/sshtunnels-table";
import { useGetSSHTunnels } from "../../../services/queries";

const SSHTunnelTable = memo(() => {
  const { token } = theme.useToken();
  const columns = useSSHTunnelColumn();

  const {
    data: tunnelsData = {},
    isFetching: tunnelsFetching,
    refetch: handleRefreshClick,
  } = useGetSSHTunnels();

  // Convert the object data to array format for the table
  const tableData = useMemo(() => {
    return Object.entries(tunnelsData).map(([port, tunnelInfo]) => ({
      ...tunnelInfo,
      key: port,
    }));
  }, [tunnelsData]);

  return (
    <ProTable
      cardProps={{
        style: { boxShadow: token?.Card?.boxShadow },
      }}
      columns={columns}
      dataSource={tableData}
      rowKey="listen_port"
      headerTitle="SSH Tunnels"
      loading={tunnelsFetching}
      size="small"
      scroll={{ x: 800 }}
      options={{
        reload: handleRefreshClick,
        fullScreen: false,
      }}
      search={false}
      dateFormatter="string"
      columnsState={{
        persistenceKey: "ssh-tunnels-table",
        persistenceType: "localStorage",
      }}
    />
  );
});

export default SSHTunnelTable;
