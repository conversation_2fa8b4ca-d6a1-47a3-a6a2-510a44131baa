package modelcontextprotocol

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
)

func setupTestViper(t *testing.T) string {
	// Create a temporary directory for test config
	tempDir, err := os.MkdirTemp("", "mcp-test")
	assert.NoError(t, err)

	// Create a test config file
	configPath := filepath.Join(tempDir, "config.yaml")

	// Reset viper settings
	viper.Reset()

	// Configure viper to use our test config
	viper.SetConfigFile(configPath)
	viper.SetConfigType("yaml")

	// Initialize with empty mcpservers array and save
	viper.Set("mcpservers", []MCPServer{
		{
			Type:    MCPServerTypeStdio,
			Name:    "filesystem",
			Command: "npx",
			Env:     []string{},
			Args:    []string{"-y", "@modelcontextprotocol/server-filesystem", "c:/Users/<USER>/Downloads"},
		},
	})
	err = viper.WriteConfig()
	assert.NoError(t, err)

	return tempDir
}
