package chain

import (
	"context"
	"mnms/llm"
	"testing"
)

type Parameter struct {
	Name        string `json:"name"`
	Description string `json:"description"`
}

type Command struct {
	Action      string      `json:"action"`
	Description string      `json:"description"`
	Cmd         string      `json:"cmd"`
	Parameters  []Parameter `json:"parameters"`
	Verify      string      `json:"verify"`
	Examples    []string    `json:"examples"`
	Type        string      `json:"type"`
}

// Test<PERSON>hain<PERSON>unner is a test runner for the chain.
func TestChainRunner(t *testing.T) {
	gptSettings := llm.GPTSettings()
	mod, err := llm.NewLLMClient(gptSettings)
	if err != nil {
		t.Fatal(err)
	}

	systemPrompt := "You are a web service, response pure JSON object. put your output to field message."
	chain := NewBasicChain(mod, systemPrompt, 0.1, 5)
	ans, err := chain.Run(context.Background(), "Please introduce yourself")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(ans)

}
