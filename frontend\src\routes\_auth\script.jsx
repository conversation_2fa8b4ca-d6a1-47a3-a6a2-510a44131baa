import { createFileRoute } from "@tanstack/react-router";
import { Row, Col } from "antd";
import {
  ScriptForm,
  CommandFlags,
  CommandResultsList,
} from "../../features/script";

export const Route = createFileRoute("/_auth/script")({
  component: ScriptComponent,
});

/**
 * Script page component
 * @returns {JSX.Element} Script page
 */
function ScriptComponent() {
  return (
    <Row gutter={[16, 16]}>
      <Col xs={24} md={24} lg={12}>
        <ScriptForm />
        <div style={{ marginTop: 16 }}>
          <CommandFlags />
        </div>
      </Col>
      <Col xs={24} md={24} lg={12}>
        <CommandResultsList />
      </Col>
    </Row>
  );
}
