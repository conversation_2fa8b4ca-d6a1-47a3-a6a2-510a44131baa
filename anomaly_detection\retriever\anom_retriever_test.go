package retriever

import (
	"context"
	"fmt"
	"mnms/anomaly_detection/store/anomstore"
	"mnms/llm"
	"testing"
)

// TestLocalRetrieverOpenAi is a test for LocalRetriever.
func TestLocalRetrieverOpenAi(t *testing.T) {
	llmsettings := llm.GPTSettings()
	gpt3, err := llm.NewLLMClient(llmsettings)
	if err != nil {
		t.Fatal(err)
	}
	r := NewRetriever(&anomstore.LocalVectors, gpt3)
	promtps := []string{
		"Please introduce yourself",
		`<5>May 15 15:22:55 root alert: root offline`,
		`<5>May 15 15:22:55 root alert: root shutdown`,
		`<5>May 15 15:22:55 bbanomsvc: command insert not found!`,
		`<11> Jul 17 06:14:36 unknown: the unrecognized message`,
		`<11> Jul 17 06:14:36 unknown: a wierd message, it was sent <NAME_EMAIL>`,
		`<11> Jul 17 06:14:36 unknown: a strange message, every night!!!`,
		`<11> Jul 17 06:14:36 unknown: an alien message from nova5 system`,
	}
	ctx := context.Background()
	// add to store
	for _, p := range promtps {
		r.Add(ctx, p, map[string]any{})
	}

	// Test GetReleventDocuments
	candidatew, err := r.GetReleventDocuments(ctx, "root alert: root offline", 5)
	if err != nil {
		t.Fatal(err)
	}
	for _, c := range candidatew {
		t.Log(c)
	}

	// Test GetReleventDocumentsWithThreshold
	candidates, err := r.GetReleventDocumentsWithThreshold(ctx, "root alert: root offline", 5, 0.1)
	if err != nil {
		t.Fatal(err)
	}
	t.Log("how many candidates under 0.1 score: ", len(candidates))

	// Test GetReleventDocumentsWithThreshold
	candidates, err = r.GetReleventDocumentsWithThreshold(ctx, "root alert: root offline", 5, 0.65)
	if err != nil {
		t.Fatal(err)
	}
	t.Log("how many candidates under 0.65 score: ", len(candidates))

}

func TestLocalRetrieverOllama(t *testing.T) {
	llama := llm.NewOllama("http://localhost", "llama3", 11434)
	r := NewRetriever(&anomstore.LocalVectors, llama)
	promtps := []string{
		"Please introduce yourself",
		`<5>May 15 15:22:55 root alert: root offline`,
		`<5>May 15 15:22:55 root alert: root shutdown`,
		`<5>May 15 15:22:55 bbanomsvc: command insert not found!`,
		`<11> Jul 17 06:14:36 unknown: the unrecognized message`,
		`<11> Jul 17 06:14:36 unknown: a wierd message, it was sent <NAME_EMAIL>`,
		`<11> Jul 17 06:14:36 unknown: a strange message, every night!!!`,
		`<11> Jul 17 06:14:36 unknown: an alien message from nova5 system`,
	}

	ctx := context.Background()
	// add to store
	for _, p := range promtps {
		_, err := r.Add(ctx, p, map[string]any{})
		if err != nil {
			t.Log(err)
		}

	}
	// Test GetReleventDocuments
	t.Log("INPUT >>> <5>Sep 20 15:22:55 root alert: root offline")
	candidatew, err := r.GetReleventDocuments(ctx, "<5>Sep 20 15:22:55 root alert: root offline", 5)
	if err != nil {
		t.Fatal(err)
	}
	for _, c := range candidatew {
		t.Log(c)
	}

	t.Log("=====================================")
	t.Log("INPUT >>> <50>Jun 10 01:20:05 root alert: root offline")
	// Test GetReleventDocuments
	candidatew, err = r.GetReleventDocuments(ctx, "<50>Jun 10 01:20:05 root alert: root offline", 5)
	if err != nil {
		t.Fatal(err)
	}
	for _, c := range candidatew {
		t.Log(c)
	}
	t.Log("=====================================")
	t.Log("INPUT >>> <5>Sep 20 15:22:55 root alert: root is offline currentlly")
	// Test GetReleventDocuments
	candidatew, err = r.GetReleventDocuments(ctx, "<5>Sep 20 15:22:55 root alert: root is offline currentlly", 5)
	if err != nil {
		t.Fatal(err)
	}
	for _, c := range candidatew {
		t.Log(c)
	}

	t.Log("=====================================")
	t.Log("INPUT >>> <5>Sep 20 15:22:55 root alert: root online")
	// Test GetReleventDocuments
	candidatew, err = r.GetReleventDocuments(ctx, "<5>Sep 20 15:22:55 root alert: root online", 5)
	if err != nil {
		t.Fatal(err)
	}
	for _, c := range candidatew {
		t.Log(c)
	}
}

// TestRevelantDocuments is a test for GetReleventDocuments.
func TestRelevantDocuments(t *testing.T) {
	words := []string{
		"On Github: https://github.com/bbtechhive/mnms/releases/tag/v1.0.5",
		"On Google Drive: https://drive.google.com/file/d/1XqWbfCCd4WPMwxmaOvXnka3JvZ3_leuq/view?usp=sharing",
		"Just the user manual PDF: https://drive.google.com/file/d/11IsdzBrJvPYvkPiveGE3aLArmR06139o/view?usp=sharing",
		"Significant API changes, including []CmdInfo and refactoring of APIs.",
		"Addition of IDPS and Anomaly optional add-on modules support. ",
		"Nimbl now supports additional, add-on features such as IDPS and Anomaly detection.",
		`IDPS feature addition allows distributed intrusion detection and prevention by allowing us
ers to filter specific patterns in network traffic to generate alerts and create preventat
ive actions, such as dropping the offending packets.`,
		`Anomaly detection feature addition can help users by analyzing alerts and logs using LLM, large generative AI models like OpenAI, to automatically determine possible anomalies, while allowing for customizations to the training data via RAG, retrieval augmented generation technique to enhance the accuracy and reliability of the AI models using user supplied data and embedding vector samples.`,
		`Both IDPS and Anomaly features can work together. For example, IDPS can generate logs that alert detection of network packets in monitored network subnets. The logs can be forwarded to a series of log services, which can be analyzed by Anomaly detection service to alert the users and create recommendations for proper handling of potentially harmful traffic.   This can be done over many network segments and complex topologies in a scalable manner.  Multiple instances of IDPS and Anomaly services, as well as syslog services can coordinate with Root and Nimbl network services.`,
		`These additional features will be available if your license has these features enabled.  Please contact your Nimbl sales and support staff to activate the licenses.`,
		`Users will need additional licenses enabled for the additional features.`,
		`The Nimbl system will be updated to include these features in the next release.`,
		`The license server is online in the Google Cloud at
			https://nimbl.blackbeartechhive.com/login`,
		`Please read the user manual carefully to see all the updated features.`,
		`Intially bbnim-v1.0.5-alpha.zip is available. Depending on the feedback, we may release bbnim-v1.0.5-beta.zip, followed by bbnim-v1.0.5-final.zip.`,
	}

	llama := llm.NewOllama("http://localhost", "llama3", 11434)
	r := NewRetriever(&anomstore.LocalVectors, llama)
	// add words to store
	for _, w := range words {
		_, err := r.AddWithThreshold(context.Background(), w, map[string]any{}, float32(0.0001))
		if err != nil {
			t.Log("add fail", err)
		}
	}

	getCandidates := func(input string) {
		fmt.Printf(">>> %s\n", input)
		candidates, err := r.GetReleventDocuments(context.Background(), input, 5)
		if err != nil {
			t.Fatal(err)
		}

		for i, c := range candidates {
			//t.Logf(">>> %s, %f", c.Content, c.Score)
			fmt.Printf("%d. %s, %f\n", i, c.Content, c.Score)
		}
		fmt.Printf("\n\n")
	}

	// Test

	getCandidates("Download the latest release from Github")
	getCandidates("How to start?")
	getCandidates("那邊可以下載？")
	getCandidates("Any AI feature?")
	getCandidates("Is it MIT")
}
