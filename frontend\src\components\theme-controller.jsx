import { But<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>lex, Pop<PERSON>, Segmented, Typography } from "antd";
import { memo } from "react";
import PropTypes from "prop-types";
import { useSettingStore } from "../store/setting-store";
import { ThemeIcon } from "./custom-icons";
import ColorSwatch from "./color-swatch";

const THEME_OPTIONS = [
  { label: "Auto", value: "auto" },
  { label: "Light", value: "light" },
  { label: "Dark", value: "dark" },
];

const PopoverContent = memo(({ mode, onModeChange }) => (
  <Flex vertical align="center" gap={4}>
    <Typography.Text>Change Color Mode</Typography.Text>
    <Segmented value={mode} onChange={onModeChange} options={THEME_OPTIONS} />
    <Divider style={{ margin: "8px 0" }} />
    <Typography.Text>Change Primary Color</Typography.Text>
    <ColorSwatch />
  </Flex>
));

const ThemeController = () => {
  const { changeMode, mode } = useSettingStore();

  const handleModeChange = (value) => {
    changeMode(value);
  };

  return (
    <Popover
      placement="bottom"
      title="NIMBL Theme Settings"
      content={<PopoverContent mode={mode} onModeChange={handleModeChange} />}
      trigger="click"
      showArrow={false}
    >
      <Button type="default" aria-label="Theme settings" icon={<ThemeIcon />} />
    </Popover>
  );
};

PopoverContent.propTypes = {
  mode: PropTypes.oneOf(THEME_OPTIONS.map((opt) => opt.value)).isRequired,
  onModeChange: PropTypes.func.isRequired,
};

export default memo(ThemeController);
