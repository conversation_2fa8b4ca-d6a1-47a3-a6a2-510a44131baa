import { describe, it, expect, vi, beforeEach } from 'vitest';
import { handleCommandExecution } from './command-execution';

describe('command-execution', () => {
  const mockNotification = {
    success: vi.fn(),
    error: vi.fn(),
  };
  
  const mockSendCommand = {
    mutateAsync: vi.fn(),
  };
  
  beforeEach(() => {
    vi.clearAllMocks();
  });
  
  describe('handleCommandExecution', () => {
    it('should successfully execute a valid command', async () => {
      // Arrange
      const command = ['show version'];
      const mockResponse = { result: 'Command output' };
      mockSendCommand.mutateAsync.mockResolvedValue(mockResponse);
      
      // Act
      const result = await handleCommandExecution(command, mockSendCommand, mockNotification);
      
      // Assert
      expect(mockSendCommand.mutateAsync).toHaveBeenCalledWith(command);
      expect(mockNotification.success).toHaveBeenCalledWith({
        message: 'Command sent successfully!',
      });
      expect(result).toEqual({
        success: true,
        message: 'Command sent successfully!',
        data: mockResponse,
      });
    });
    
    it('should handle error when command is not an array', async () => {
      // Arrange
      const command = 'show version';
      
      // Act
      const result = await handleCommandExecution(command, mockSendCommand, mockNotification);
      
      // Assert
      expect(mockSendCommand.mutateAsync).not.toHaveBeenCalled();
      expect(mockNotification.error).toHaveBeenCalledWith({
        message: 'Invalid command format',
      });
      expect(result).toEqual({
        success: false,
        message: 'Invalid command format',
      });
    });
    
    it('should handle error when command array is empty', async () => {
      // Arrange
      const command = [];
      
      // Act
      const result = await handleCommandExecution(command, mockSendCommand, mockNotification);
      
      // Assert
      expect(mockSendCommand.mutateAsync).not.toHaveBeenCalled();
      expect(mockNotification.error).toHaveBeenCalledWith({
        message: 'Invalid command format',
      });
      expect(result).toEqual({
        success: false,
        message: 'Invalid command format',
      });
    });
    
    it('should handle API error with message', async () => {
      // Arrange
      const command = ['show version'];
      const error = new Error('API error');
      mockSendCommand.mutateAsync.mockRejectedValue(error);
      
      // Act
      const result = await handleCommandExecution(command, mockSendCommand, mockNotification);
      
      // Assert
      expect(mockSendCommand.mutateAsync).toHaveBeenCalledWith(command);
      expect(mockNotification.error).toHaveBeenCalledWith({
        message: 'API error',
      });
      expect(result).toEqual({
        success: false,
        message: 'API error',
      });
    });
    
    it('should handle API error with data.error', async () => {
      // Arrange
      const command = ['show version'];
      const error = { data: { error: 'Data error' } };
      mockSendCommand.mutateAsync.mockRejectedValue(error);
      
      // Act
      const result = await handleCommandExecution(command, mockSendCommand, mockNotification);
      
      // Assert
      expect(mockSendCommand.mutateAsync).toHaveBeenCalledWith(command);
      expect(mockNotification.error).toHaveBeenCalledWith({
        message: 'Data error',
      });
      expect(result).toEqual({
        success: false,
        message: 'Data error',
      });
    });
    
    it('should handle unknown error type', async () => {
      // Arrange
      const command = ['show version'];
      mockSendCommand.mutateAsync.mockRejectedValue(null);
      
      // Act
      const result = await handleCommandExecution(command, mockSendCommand, mockNotification);
      
      // Assert
      expect(mockSendCommand.mutateAsync).toHaveBeenCalledWith(command);
      expect(mockNotification.error).toHaveBeenCalledWith({
        message: 'An error occurred while executing the command',
      });
      expect(result).toEqual({
        success: false,
        message: 'An error occurred while executing the command',
      });
    });
  });
});
