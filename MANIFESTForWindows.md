# Table of contents in this ZIP installation file

## MANIFEST.md

This file.

## README.md

Basic information for the users. Please read this first.

## CHANGELOG.md

List of changes in each released version of Nimbl software

## authentication.md

Information about login, password, encryption and RSA keys.

## Blackbear_NIMBL_User_Manual.pdf

User manual for NIMBL software

## API.txt

Information about REST API and Command API 

## COPYRIGHT.txt

Copyright information

## caddy.exe

Caddy executables for Windows.
Caddy is useful for running a TLS terminating reverse-proxy front for Root Nimbl server.

## bbnimbl.exe

These are the executable files for the frontend of our project, designed for both Windows (bbnimbl.exe) systems. The frontend is built using React, a popular JavaScript library for building user interfaces. This application provides an interactive user interface for interacting with our software.

## bbrootsvc.exe

NIMBL root service. This executable now combines the functionalities of both the frontend (previously `bbnimbl.exe`) and the root service, providing a unified application for better efficiency and deployment.

## bbnmssvc.exe

NIMBL network service.

## bbctl.exe

NIMBL command line interface.

## bblogsvc.exe

NIMBL log service.

## bbanomsvc.exe

NIMBL anomaly detection service.

## bbidpsvc.exe

NIMBL idp service.

## npcap-1.72.exe

PCAP software to be installed on Windows before running Nimbl software

## windivert_hyperscan.exe

An advanced network packet inspection and manipulation, leveraging Hyperscan's regex capabilities for pattern matching within network traffic used in NIMBL IDPS service for Windows.

## get-machine-id.exe

Return current machine id that can be used for license registration
