import React from "react";
import { App, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { TABLE_WIDTHS } from "./constants";
import { deviceInfoModal } from "./DeviceInfoModal";
import { EditOutlined } from "@ant-design/icons";
import { useAppStore } from "../../store/store";

export const useDeviceColumns = (token) => {
  const { modal } = App.useApp();
  const { openDialogs } = useAppStore();

  const showDeviceInfo = React.useCallback(
    (record) => {
      modal.info(deviceInfoModal(record));
    },
    [modal]
  );

  return React.useMemo(
    () => [
      {
        title: "IP Address",
        dataIndex: "ipaddress",
        key: "ipaddress",
        width: TABLE_WIDTHS.IP_ADDRESS,
        fixed: "left",
        align: "center",
        exportable: true,
        render: (data, record) => (
          <Button
            type="link"
            style={{
              color:
                record.type === "IP discovered"
                  ? token.colorWarning
                  : token.colorPrimary,
            }}
            onClick={() => showDeviceInfo(record)}
          >
            {data}
          </Button>
        ),
      },
      {
        title: "Model",
        width: TABLE_WIDTHS.MODEL,
        dataIndex: "modelname",
        key: "modelname",
        exportable: true,
        sorter: (a, b) => a.model.localeCompare(b.model),
      },
      {
        title: "Service Name",
        width: TABLE_WIDTHS.SERVICE_NAME,
        dataIndex: "scannedby",
        key: "scannedby",
        exportable: true,
        ellipsis: true,
        sorter: (a, b) => (a.scannedby > b.scannedby ? 1 : -1),
      },
      {
        title: "MAC Address",
        dataIndex: "mac",
        key: "mac",
        exportable: true,
        width: TABLE_WIDTHS.MAC_ADDRESS,
      },
      {
        title: "Host Name",
        dataIndex: "hostname",
        key: "hostname",
        exportable: true,
        width: TABLE_WIDTHS.HOST_NAME,
        ellipsis: true,
      },
      {
        title: "Netmask",
        dataIndex: "netmask",
        key: "netmask",
        exportable: true,
        width: TABLE_WIDTHS.NETMASK,
      },
      {
        title: "kernel",
        dataIndex: "kernel",
        key: "kernel",
        exportable: true,
        width: TABLE_WIDTHS.KERNEL,
      },
      {
        title: "Firmware Ver.",
        dataIndex: "ap",
        key: "ap",
        exportable: true,
        width: TABLE_WIDTHS.FIRMWARE,
        ellipsis: true,
      },
      {
        title: "Agent Ver.",
        dataIndex: "agentVersion",
        key: "agentVersion",
        exportable: true,
        width: TABLE_WIDTHS.AGENT,
      },
      {
        title: "Timestamp",
        dataIndex: "timestamp",
        key: "timestamp",
        width: TABLE_WIDTHS.TIMESTAMP,
        exportable: true,
        render: (data) => new Date(data * 1000).toLocaleString(),
      },
      {
        title: "Supported",
        dataIndex: "supported",
        key: "supported",
        width: 200,
        hidden: true,
        exportable: false,
        render: (data) => data.toString(),
      },
      {
        title: "Action",
        key: "operation",
        width: 100,
        fixed: "right",
        align: "center",
        render: (data, record) =>
          record.type === "IP discovered" ? (
            <Tooltip title="edit manual device">
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => openDialogs({ id: "deviceEdit", data: record })}
              />
            </Tooltip>
          ) : null,
      },
    ],
    [token, showDeviceInfo, openDialogs]
  );
};

// Filter columns for search functionality
export const deviceFilterColumns = [
  { dataIndex: "ipaddress" },
  { dataIndex: "modelname" },
  { dataIndex: "scannedby" },
  { dataIndex: "mac" },
  { dataIndex: "hostname" },
  { dataIndex: "netmask" },
  { dataIndex: "kernel" },
  { dataIndex: "ap" },
  { dataIndex: "agentVersion" },
  { dataIndex: "timestamp" },
  { dataIndex: "supported" },
];
