import React from "react";
import { Global } from "@emotion/react";
import { useTheme } from "antd-style";

const GlobalStyle = React.memo(() => {
  const token = useTheme();

  return (
    <Global
      styles={{
        "html, body": {
          margin: 0,
          padding: 0,
          height: "100%",
          fontFamily: token.fontFamily,
          fontSize: token.fontSize,
          backgroundColor: token.colorBgLayout,
          color: token.colorText,
          overflowX: "hidden",
        },
        "#root": {
          height: "100%",
        },
        ".table-row-light": {
          backgroundColor: token.colorBgContainer,
        },
        ".table-row-dark": {
          backgroundColor: token.colorFillAlter,
        },
        ".scrollbar-hidden::-webkit-scrollbar": {
          display: "none",
        },
        "*": {
          boxSizing: "border-box",
          scrollbarWidth: "thin",
          scrollbarColor: `${token.colorTextSecondary} ${token.colorBgContainer}`,
        },
        "*::-webkit-scrollbar": {
          width: "8px",
          height: "8px",
        },
        "*::-webkit-scrollbar-track": {
          background: token.colorBgContainer,
          borderRadius: "4px",
        },
        "*::-webkit-scrollbar-thumb": {
          background: token.colorTextSecondary,
          borderRadius: "4px",
          border: `2px solid ${token.colorBgContainer}`,
        },
        "*::-webkit-scrollbar-thumb:hover": {
          background: token.colorPrimary,
        },
        ".custom-scrollbar": {
          scrollbarWidth: "thin",
          scrollbarColor: `${token.colorPrimary} ${token.colorBgContainer}`,
        },
        ".custom-scrollbar::-webkit-scrollbar": {
          width: "8px",
          height: "8px",
        },
        ".custom-scrollbar::-webkit-scrollbar-track": {
          background: token.colorBgContainer,
          borderRadius: "4px",
        },
        ".custom-scrollbar::-webkit-scrollbar-thumb": {
          background: token.colorTextSecondary,
          borderRadius: "4px",
          border: `2px solid ${token.colorBgContainer}`,
        },
        ".custom-scrollbar::-webkit-scrollbar-thumb:hover": {
          background: token.colorPrimary,
        },
      }}
    />
  );
});

GlobalStyle.displayName = "GlobalStyle";

export default GlobalStyle;
