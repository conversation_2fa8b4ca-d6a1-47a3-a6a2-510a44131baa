package modelcontextprotocol

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"time"

	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/qeof/q"
)

// StdioMCPServerSettings settings for a stdio MCP server
// MCPServerType represents the type of MCP server
type MCPServerType string

const (
	MCPServerTypeStdio MCPServerType = "command"
	MCPServerTypeSSE   MCPServerType = "sse"
)

// mcpserver json file
var MCPServersFile = "mcpservers.json"

// MCPServer represents settings for an MCP server
// Go doesn't have union types, so we use a struct with optional fields
// and a Type field to indicate which settings are valid
type MCPServer struct {
	Type    MCPServerType    `json:"type"`
	Name    string           `json:"name"`
	Command string           `json:"command,omitempty"`  // Used for stdio
	Env     []string         `json:"env,omitempty"`      // Used for stdio
	Args    []string         `json:"args,omitempty"`     // Used for stdio
	URL     string           `json:"url,omitempty"`      // Used for sse
	Client  client.MCPClient `json:"-" mapstructure:"-"` // Used for sse, excluded from Viper
}

// InitAllMCPServers initializes all MCP servers
func InitAllMCPServers() error {
	servers := []MCPServer{
		{
			Type: MCPServerTypeSSE,
			Name: "nimbl",
			URL:  "http://localhost:27182/api/v1/mcp/sse",
		},
	}
	for _, server := range servers {
		q.Q("initializing MCP server", server.Name)
		err := server.InitializeMCPClient()
		if err != nil {
			return fmt.Errorf("failed to initialize MCP client: %w", err)
		}
		runningClients[server.Name] = server
	}
	return nil
}

// AddMCPServerToConfig to config file mcpservers field
func AddMCPServerToConfig(serverSettings MCPServer) error {
	var mcpServers []MCPServer

	// Read existing servers or create empty slice if file doesn't exist
	jsonFile, err := os.ReadFile(MCPServersFile)
	if err != nil {
		mcpServers = []MCPServer{}
	} else {
		if err := json.Unmarshal(jsonFile, &mcpServers); err != nil {
			return fmt.Errorf("failed to unmarshal mcpservers: %w", err)
		}
	}

	// Add new server
	mcpServers = append(mcpServers, serverSettings)

	// Write back to file
	data, err := json.Marshal(mcpServers)
	if err != nil {
		return fmt.Errorf("failed to marshal mcpservers: %w", err)
	}

	return os.WriteFile(MCPServersFile, data, 0644)
}

// UpdateMCPServerInConfig updates a MCP server in the config file
func UpdateMCPServerInConfig(serverSettings MCPServer) error {
	var mcpServers []MCPServer

	// Read existing servers
	jsonFile, err := os.ReadFile(MCPServersFile)
	if err != nil {
		return fmt.Errorf("failed to read mcpservers: %w", err)
	}

	if err := json.Unmarshal(jsonFile, &mcpServers); err != nil {
		return fmt.Errorf("failed to unmarshal mcpservers: %w", err)
	}

	// Update server
	found := false
	for i, server := range mcpServers {
		if server.Name == serverSettings.Name {
			mcpServers[i] = serverSettings
			found = true
			break
		}
	}

	if !found {
		return fmt.Errorf("server %s not found", serverSettings.Name)
	}

	// Write back to file
	data, err := json.Marshal(mcpServers)
	if err != nil {
		return fmt.Errorf("failed to marshal mcpservers: %w", err)
	}

	return os.WriteFile(MCPServersFile, data, 0644)
}

// DeleteMCPServerFromConfig deletes a MCP server from the config file
func DeleteMCPServerFromConfig(serverName string) error {
	var mcpServers []MCPServer

	// Read existing servers
	jsonFile, err := os.ReadFile(MCPServersFile)
	if err != nil {
		return fmt.Errorf("failed to read mcpservers: %w", err)
	}

	if err := json.Unmarshal(jsonFile, &mcpServers); err != nil {
		return fmt.Errorf("failed to unmarshal mcpservers: %w", err)
	}

	// Remove server
	found := false
	for i, server := range mcpServers {
		if server.Name == serverName {
			mcpServers = append(mcpServers[:i], mcpServers[i+1:]...)
			found = true
			break
		}
	}

	if !found {
		return fmt.Errorf("server %s not found", serverName)
	}

	// Write back to file
	data, err := json.Marshal(mcpServers)
	if err != nil {
		return fmt.Errorf("failed to marshal mcpservers: %w", err)
	}

	return os.WriteFile(MCPServersFile, data, 0644)
}

// GetMCPServersFromConfig returns all MCP servers from the config file
// if serverNames is nil, all servers are returned
func GetMCPServersFromConfig(serverNames []string) ([]MCPServer, error) {
	var servers []MCPServer

	// Read existing servers
	jsonFile, err := os.ReadFile(MCPServersFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read mcpservers: %w", err)
	}

	if err := json.Unmarshal(jsonFile, &servers); err != nil {
		return nil, fmt.Errorf("failed to unmarshal mcpservers: %w", err)
	}

	var matchedServers []MCPServer
	if serverNames == nil {
		return servers, nil
	}

	for _, server := range servers {
		for _, name := range serverNames {
			if server.Name == name {
				matchedServers = append(matchedServers, server)
			}
		}
	}

	q.Q("matched servers", matchedServers)

	if len(matchedServers) == 0 {
		return nil, fmt.Errorf("no matching MCP servers found")
	}

	return matchedServers, nil
}

// GetMCPServerFromConfig returns a MCP server from the config file
func GetMCPServerFromConfig(serverName string) (MCPServer, error) {
	servers, err := GetMCPServersFromConfig([]string{serverName})
	if err != nil {
		return MCPServer{}, err
	}

	return servers[0], nil
}

// SseClientStart starts a SSE client
func (s *MCPServer) SseClientStart() error {
	if s.Client != nil {
		s.Client.Close()
	}
	if s.Type != MCPServerTypeSSE {
		return nil
	}
	var err error
	s.Client, err = client.NewSSEMCPClient(s.URL)
	if err != nil {
		return fmt.Errorf("failed to create SSE MCP client: %w", err)
	}

	if sseClient, ok := s.Client.(*client.SSEMCPClient); ok {
		err = sseClient.Start(context.Background())
		if err != nil {
			q.Q("failed to start SSE MCP client", s.Name, err)
			return fmt.Errorf("failed to start SSE MCP client: %w", err)
		}
	} else {
		q.Q("failed to cast MCP client to SSE client", s.Name)
		return fmt.Errorf("failed to cast MCP client to SSE client")
	}
	// Use a longer timeout for initialization
	ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second)
	defer cancel()

	request := mcp.InitializeRequest{}
	request.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
	request.Params.ClientInfo = mcp.Implementation{
		Name:    "mnms",
		Version: "1.0.0",
	}

	result, err := s.Client.Initialize(ctx, request)
	if err != nil {
		q.Q("failed to initialize MCP client", s.Name, err)
		return fmt.Errorf("Initialize failed: %w", err)
	}
	q.Q("MCP client initialized", "server:", result.ServerInfo.Name)
	return nil
}

// SseClientStop stops a SSE client
func (s *MCPServer) SseClientStop() error {
	if s.Type != MCPServerTypeSSE {
		return nil
	}
	if s.Client == nil {
		return nil
	}

	err := s.Client.Close()
	if err != nil {
		return err
	}
	s.Client = nil
	return nil
}

// InitializeMCPClient initializes MCP server
func (s *MCPServer) InitializeMCPClient() error {
	var err error
	switch s.Type {
	case MCPServerTypeStdio:
		s.Client, err = client.NewStdioMCPClient(s.Command, s.Env, s.Args...)
	case MCPServerTypeSSE:
		return s.SseClientStart()
	}
	if err != nil {
		q.Q("failed to create MCP client", s.Name, err)
		return fmt.Errorf("failed to create MCP client: %w", err)
	}
	q.Q("MCP client created", s.Name)

	// Use a longer timeout for initialization
	ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second)
	defer cancel()

	request := mcp.InitializeRequest{}
	request.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
	request.Params.ClientInfo = mcp.Implementation{
		Name:    "mnms",
		Version: "1.0.0",
	}

	result, err := s.Client.Initialize(ctx, request)
	if err != nil {
		q.Q("failed to initialize MCP client", s.Name, err)
		return fmt.Errorf("Initialize failed: %w", err)
	}
	q.Q("MCP client initialized", "server:", result.ServerInfo.Name)
	return nil
}

type MCPServerStatus struct {
	MCPServer
	Status string `json:"status"` // running, stopped, error
}

// GetMCPServersStatus returns the status of a MCP server
// if serverNames is nil, all servers are returned
func GetMCPServersStatus(serverNames []string) ([]MCPServerStatus, error) {
	q.Q("GetMCPServersStatus", serverNames)
	servers, err := GetMCPServersFromConfig(serverNames)
	if err != nil {
		q.Q("failed to get MCP servers from config", err)
		return nil, err
	}

	q.Q("found servers", servers)

	statuses := make([]MCPServerStatus, len(servers))
	for i, server := range servers {
		running := IsServerRunning(server.Name)
		status := "stopped"
		if running {
			status = "running"
		}
		q.Q("GetMCPServersStatus", server.Name, status)
		statuses[i] = MCPServerStatus{
			MCPServer: server,
			Status:    status,
		}
	}
	return statuses, nil
}

// ListTools lists all tools available on a MCP server
func (s *MCPServer) ListTools() ([]mcp.Tool, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second)
	defer cancel()
	q.Q("ListTools", s.Name)

	err := s.SseClientStart()
	if err != nil {
		return nil, fmt.Errorf("failed to start SSE MCP client: %w", err)
	}

	q.Q("Call ListTools", s.Name)

	request := mcp.ListToolsRequest{}
	result, err := s.Client.ListTools(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("failed to list tools: %w", err)
	}

	return result.Tools, nil
}

// GetRunningMCPServers returns a MCP server, if the server is not running, it will be started
func GetRunningMCPServers(serverNames []string) ([]MCPServer, error) {
	runningClientMutex.Lock()
	defer runningClientMutex.Unlock()
	servers := make([]MCPServer, 0)
	for _, serverName := range serverNames {
		server, ok := runningClients[serverName]
		if !ok {
			q.Q("server not found, trying to start it", serverName)
			server, err := GetMCPServerFromConfig(serverName)
			if err != nil {
				q.Q("failed to get MCP server", serverName, err)
				continue
			}
			q.Q("starting server", serverName)
			err = server.InitializeMCPClient()
			if err != nil {
				q.Q("failed to initialize MCP client", serverName, err)
				continue
			}
			runningClients[serverName] = server
			servers = append(servers, server)
			continue
		}
		servers = append(servers, server)
	}
	return servers, nil
}

// CallTool calls a tool on a MCP server
// func (s *MCPServer) CallTool(toolName string, arguments map[string]interface{}) (string, error) {
// 	// check if tool is in the list of tools
// 	tools, err := s.ListTools()
// 	if err != nil {
// 		return "", fmt.Errorf("failed to list tools: %w", err)
// 	}
// 	for _, tool := range tools {
// 		if tool.Name == toolName {
// 			// call the tool
// 			request := mcp.CallToolRequest{}
// 			request.Params.Name = toolName
// 			request.Params.Arguments = arguments
// 			ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
// 			defer cancel()
// 			result, err := s.Client.CallTool(ctx, request)
// 			if err != nil {
// 				return "", fmt.Errorf("failed to call tool: %w", err)
// 			}
// 			return result.Content[0].Text, nil
// 		}
// 	}

// }

// }
