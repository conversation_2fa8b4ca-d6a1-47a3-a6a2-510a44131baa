const PASSWORD_PATTERN =
  /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*#?&])[A-Za-z\d@$!%*#?&]{8,20}$/;

export const FORM_FIELDS = {
  NAME: "name",
  EMAIL: "email",
  PASSWORD: "password",
  ROLE: "role",
};

export const VALIDATION_RULES = {
  [FORM_FIELDS.NAME]: [
    { required: true, message: "Please input the username!" },
  ],
  [FORM_FIELDS.EMAIL]: [
    { required: true, message: "Please input the email!" },
    {
      type: "email",
      message: "The input is not valid E-mail!",
    },
  ],
  [FORM_FIELDS.PASSWORD]: [
    {
      required: true,
      message: "Please input the password!",
    },
    {
      pattern: PASSWORD_PATTERN,
      message:
        "password must have 8-20 characters, at least one uppercase one lowercase one digit one special character",
    },
  ],
  [FORM_FIELDS.ROLE]: [{ required: true, message: "Please select the role!" }],
};
