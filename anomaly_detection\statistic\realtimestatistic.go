package statistic

import (
	"mnms/anomaly_detection/def"
	"mnms/anomaly_detection/report"

	"github.com/qeof/q"
)

// Realtime operations, we want to record messages which were not sent to server. We also have to
// maintain a local realtime report.
var nonSyncedMessages = []*report.ReportMessage{}

// addNonSyncedMessage add message to nonSyncedMessages
func addNonSyncedMessage(message string, state string, reason string) {
	m := report.ReportMessage{
		Message: message,
		State:   state,
		Reason:  reason,
	}
	nonSyncedMessages = append(nonSyncedMessages, &m)
}

// GetNonSyncedMessages get nonSyncedMessages
func GetNonSyncedMessages() []*report.ReportMessage {
	return nonSyncedMessages
}

// ClearNonSyncedMessages clear nonSyncedMessages
func ClearNonSyncedMessages() {
	nonSyncedMessages = []*report.ReportMessage{}
}

// UpdateUpstreamLogSvc update realtime upstream log service
func UpdateUpstreamLogSvc(logsvc string) {
	LocalStatistic.Realtime.UpdateSyslog(logsvc)
}

// AddRealtimeAnalyseResult add realtime analyse result
// err argument is a error returned by LogAnomalyDetect
// We have two different realtime data RealtimeStatistic and
func AddRealtimeAnalyseResult(result *def.LogAnomalyResult, err error) {
	q.Q("add realtime analyse result")
	realtimeReport := report.GetRealtimeReport()
	// Add message count
	LocalStatistic.Realtime.IncMessage()

	if err != nil {
		realtimeReport.AddError(result.Text, err)
		addNonSyncedMessage(result.Text, "error", err.Error())
		return
	}
	if !result.Normal {
		// update last
		LocalStatistic.Realtime.IncAlert()
		addNonSyncedMessage(result.Text, "anomaly", result.Reason)
	} else {
		addNonSyncedMessage(result.Text, "normal", result.Reason)
	}
	realtimeReport.AddResult(result)

}
