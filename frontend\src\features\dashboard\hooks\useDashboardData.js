import { useState, useEffect, useMemo } from "react";
import { useGetDevices, useGetSyslogs } from "../../../services/queries";
import { useSocketStore } from "../../../store/socket-store";
import { useQuery } from "@tanstack/react-query";
import { api } from "../../../services/api";
import dayjs from "dayjs";

/**
 * Custom hook to fetch and process dashboard data
 * @returns {Object} Dashboard data and loading state
 */
export const useDashboardData = () => {
  // Fetch device data
  const { data: deviceData = [], isFetching: isDeviceLoading } =
    useGetDevices("device");
  const [dailyCounts, setDailyCounts] = useState([]);

  // Get messages from socket store
  const { messages } = useSocketStore();

  const endDate = dayjs();
  const startDate = endDate.subtract(7, "day");

  const formattedStartDate = startDate.format("YYYY/MM/DD HH:mm:ss");
  const formattedEndDate = endDate.format("YYYY/MM/DD HH:mm:ss");
  const params = {
    start: formattedStartDate,
    end: formattedEndDate,
  };

  const { data: syslogData, isFetching: isSyslogLoading } =
    useGetSyslogs(params);

  useEffect(() => {
    if (syslogData) {
      const dailyCounts = [];
      const groupedByDay = syslogData.reduce((acc, log) => {
        const date = dayjs(log.Timestamp);
        const day = date.format("YYYY-MM-DD");

        if (!acc[day]) {
          acc[day] = [];
        }

        acc[day].push(log);
        return acc;
      }, {});

      for (let i = 6; i >= 0; i--) {
        const currentDate = endDate.clone().subtract(i, "day");
        const date = currentDate.format("YYYY-MM-DD");
        const displayDate = currentDate.format("MMM DD");

        dailyCounts.push({
          date: displayDate,
          count: groupedByDay[date]?.length || 0,
        });
      }

      setDailyCounts(dailyCounts);
    }
  }, [syslogData]);

  // Fetch recent commands
  const { data: commandsData = [], isFetching: isCommandsLoading } = useQuery({
    queryKey: ["commands", "recent"],
    queryFn: async () => {
      const response = await api.getCommands("last=10");

      // Convert object to array and sort by timestamp
      return Object.values(response).sort((a, b) => {
        const timestampA = a.timestamp ? new Date(a.timestamp).getTime() : 0;
        const timestampB = b.timestamp ? new Date(b.timestamp).getTime() : 0;
        return timestampB - timestampA;
      });
    },
    staleTime: 30 * 1000, // 30 seconds
  });

  // Overall loading state
  const isLoading = isDeviceLoading || isSyslogLoading || isCommandsLoading;
  return {
    deviceData,
    syslogData: dailyCounts || [],
    commandsData,
    messages,
    isLoading,
  };
};
