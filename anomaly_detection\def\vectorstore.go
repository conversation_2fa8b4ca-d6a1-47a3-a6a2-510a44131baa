package def

import (
	"context"
	"sync"
)

type Adder interface {
	// Add new doc with vector vec, retrun id of the doc
	Add(content string, meta map[string]any, vec []float32) (int64, error)
	// Upsert add doc if not exists ( by comparing vector with threshold ), update doc if exists
	Upsert(ctx context.Context, content string, meta map[string]any, vec []float32, threshold float32) (int64, error)
}

type Finder interface {
	// SearchSimilar return n similar docs to vec
	Search(ctx context.Context, vec []float32, n int) ([]Document, error)
}

type Cleaner interface {
	// Clean remove all docs
	Clear() error
}

// Persister is an interface for storing and retrieving knowledge (embedding vectors) and metadata.
type Persister interface {
	Serialize() ([]byte, error)
}

type Store interface {
	Persister
	Adder
	Finder
	Mutex() *sync.Mutex
	// GetDocument return doc with id
	GetContent(id int64) (string, error)
	// GetMeta return meta with id
	GetMeta(id int64) (map[string]any, error)
}
