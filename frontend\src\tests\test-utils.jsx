import React from "react";
import { render } from "@testing-library/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { RouterProvider, createMemoryRouter } from "@tanstack/react-router";
import { ConfigProvider, App, theme } from "antd";
import { ThemeProvider } from "antd-style";

// Create a custom render function that includes providers
export function renderWithProviders(
  ui,
  {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    }),
    ...renderOptions
  } = {}
) {
  function Wrapper({ children }) {
    return (
      <QueryClientProvider client={queryClient}>
        <ThemeProvider
          defaultAppearance="dark"
          themeMode="dark"
          theme={{
            token: {
              colorPrimary: "#13c2c2",
              borderRadius: 4,
            },
          }}
        >
          <ConfigProvider>
            <App>{children}</App>
          </ConfigProvider>
        </ThemeProvider>
      </QueryClientProvider>
    );
  }
  return render(ui, { wrapper: Wrapper, ...renderOptions });
}

// Create a custom render function that includes router
export function renderWithRouter(
  ui,
  {
    routes = [],
    initialEntries = ["/"],
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    }),
    ...renderOptions
  } = {}
) {
  const router = createMemoryRouter({
    routeTree: {
      children: routes,
    },
    initialEntries,
  });

  function Wrapper({ children }) {
    return (
      <QueryClientProvider client={queryClient}>
        <ThemeProvider
          defaultAppearance="dark"
          themeMode="dark"
          theme={{
            token: {
              colorPrimary: "#13c2c2",
              borderRadius: 4,
            },
          }}
        >
          <ConfigProvider>
            <App>
              <RouterProvider router={router} />
            </App>
          </ConfigProvider>
        </ThemeProvider>
      </QueryClientProvider>
    );
  }

  return {
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
    router,
  };
}

// Mock data for tests
export const mockData = {
  devices: [
    {
      id: "1",
      name: "Device 1",
      ip: "***********",
      modelname: "Model A",
      status: "online",
    },
    {
      id: "2",
      name: "Device 2",
      ip: "***********",
      modelname: "Model B",
      status: "offline",
    },
  ],
  users: [
    {
      id: "1",
      username: "admin",
      role: "admin",
    },
    {
      id: "2",
      username: "user",
      role: "user",
    },
  ],
  // Add more mock data as needed
};

// Export everything from RTL
export * from "@testing-library/react";
export { default as userEvent } from "@testing-library/user-event";
