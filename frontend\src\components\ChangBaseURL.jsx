import { Button, Input, Space, Typography } from "antd";
import React, { useState, useCallback } from "react";
import { useSettingStore } from "../store/setting-store";

const { Text } = Typography;

/**
 * Component for changing the base URL of the Network Management System
 * @returns {React.ReactElement} Change base URL form component
 */
const ChangeBaseURL = () => {
  const { baseURL, changeBaseURL } = useSettingStore();
  const [inputBaseUrl, setInputBaseUrl] = useState(baseURL);
  const [error, setError] = useState("");

  const validateURL = useCallback((url) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }, []);

  const handleChange = (e) => {
    const newUrl = e.target.value;
    setInputBaseUrl(newUrl);
    setError("");
  };

  const handleSave = () => {
    if (!inputBaseUrl.trim()) {
      setError("Base URL cannot be empty");
      return;
    }

    if (!validateURL(inputBaseUrl)) {
      setError("Please enter a valid URL");
      return;
    }

    changeBaseURL(inputBaseUrl);
  };

  return (
    <Space direction="vertical" size="small" style={{ width: "100%" }}>
      <Space.Compact style={{ width: "100%" }}>
        <Input
          value={inputBaseUrl}
          onChange={handleChange}
          placeholder="Enter base URL"
          status={error ? "error" : ""}
          data-testid="base-url-input"
        />
        <Button
          type="primary"
          onClick={handleSave}
          data-testid="save-base-url-button"
        >
          Save
        </Button>
      </Space.Compact>
      {error && (
        <Text type="danger" data-testid="base-url-error">
          {error}
        </Text>
      )}
    </Space>
  );
};

export default ChangeBaseURL;
