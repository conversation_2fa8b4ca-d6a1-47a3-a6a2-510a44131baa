package service

import (
	"mnms/anomaly_detection/statistic"
	"testing"
)

// TestIsOpenAIKeyWork tests the IsOpenAIKeyWork function
func TestIsOpenAIKeyWork(t *testing.T) {
	// Test the IsOpenAIKeyWork function
	if !IsOpenAIKeyWork() {
		t.<PERSON><PERSON>r("IsOpenAIKeyWork failed")
	}
	statistic.LocalStatistic.Settings.OpenAISettings.APIKey = "xw23"
	if IsOpenAIKeyWork() {
		t.Error("IsOpenAIKeyWork failed")
	}
}
