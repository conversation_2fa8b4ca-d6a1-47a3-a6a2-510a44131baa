package project

import (
	"context"
	"mnms/aiagent/flow"
	"mnms/aiagent/node"
	"mnms/llm"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestCreateProject
func TestCreateProject(t *testing.T) {
	// LLM invalid test
	llmsettings := llm.OllamaSettings("hool.2:3b")
	failProject, _ := NewProject(llmsettings, "test-fail-project", "test project is used for testing")
	_, err := failProject.CheckLLM()

	assert.Error(t, err)

	// Create a new project
	llmsettings = llm.GPTSettings()
	llmsettings.Model = "gpt-4o"

	project, _ := NewProject(llmsettings, "test-project", "test project is used for testing")
	_, err = project.CheckLLM()
	assert.NoError(t, err)

	assert.Equal(t, "test-project", project.Name)
	assert.Equal(t, "test project is used for testing", project.Description)

	check_external_data, err := node.ImportNode("check_api_external_info")
	assert.NoError(t, err)

	f := flow.NewFlow("testing", "test flow")
	f.AddNode(check_external_data)
	project.UpsertFlow(f)

	fake_relevant_doc := `
	## Get student's score
	GET /api/score?id=123&subject=math
	
	## Get student's information
	GET /api/student?id=123

	## Get student's grade
	GET /api/grade?id=123

	## Get school's cost
	GET /api/cost?category=public

	## Get school's information
	GET /api/school?name=abc
	`
	info := check_external_data.Info()

	ans, err := project.RunNodeWithRelevantDocs(context.Background(), "testing", info.Name,
		"I want to know Alan (id=s20240019) score in math.", fake_relevant_doc)

	assert.NoError(t, err)

	t.Log(ans)
}

// TestAddNodeEdgesFlow
func TestAddNodeEdgesFlow(t *testing.T) {
	// Create a new project
	llmsettings := llm.GPTSettings()
	llmsettings.Model = "gpt-4o"

	// project, err := NewProject(llmsettings, "test-project", "test project is used for
	// assert.NoError(t, err)

}
