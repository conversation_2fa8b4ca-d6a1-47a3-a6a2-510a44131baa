import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";
import { persist } from "zustand/middleware";

/**
 * @typedef {Object} SocketState
 * @property {boolean} isConnected - Whether the WebSocket is connected
 * @property {boolean} isConnecting - Whether the WebSocket is connecting
 * @property {Array} messages - Array of received WebSocket messages
 * @property {string} lastError - Last error message
 * @property {boolean} autoReconnect - Whether to automatically reconnect
 * @property {number} reconnectAttempts - Number of reconnect attempts
 * @property {number} reconnectInterval - Interval between reconnect attempts in ms
 * @property {number} maxReconnectAttempts - Maximum number of reconnect attempts
 */

/**
 * @typedef {Object} SocketActions
 * @property {(message: Object) => void} addMessage - Add a message to the messages array
 * @property {() => void} clearMessages - Clear all messages
 * @property {(error: string) => void} setError - Set the error message
 * @property {(isConnected: boolean) => void} setConnected - Set the connection status
 * @property {(isConnecting: boolean) => void} setConnecting - Set the connecting status
 * @property {(autoReconnect: boolean) => void} setAutoReconnect - Set auto reconnect
 * @property {(attempts: number) => void} setReconnectAttempts - Set reconnect attempts
 * @property {(interval: number) => void} setReconnectInterval - Set reconnect interval
 * @property {(max: number) => void} setMaxReconnectAttempts - Set max reconnect attempts
 */

/**
 * Initial state for the Socket store
 * @type {SocketState}
 */
const initialState = {
  messages: [],
  enabledFeatures: [],
  licenseError: false,
  watermark: false,
  licenseErrMessage: "",
};

/**
 * Socket store for managing WebSocket connections and messages
 * @type {import("zustand").UseBoundStore<SocketState & SocketActions>}
 */
export const useSocketStore = create(
  devtools(
    immer((set) => ({
      // Initial state
      ...initialState,

      /**
       * Add a message to the messages array
       * @param {Object} message - Message to add
       */
      addMessage: (message) =>
        set((state) => {
          state.messages.unshift(message);
          // Limit the number of messages to 30
          if (state.messages.length > 30) {
            state.messages.pop();
          }
        }),

      /**
       * Set the messages array
       * @param {Array} messages - Messages to set
       */
      setMessages: (messages) =>
        set((state) => {
          state.messages = messages;
        }),

      /**
       * Clear all messages
       */
      clearMessages: () =>
        set((state) => {
          state.messages = [];
        }),

      setEnabledFeatures: (features) =>
        set((state) => {
          state.enabledFeatures = features;
        }),

      setLicenseError: (error) =>
        set((state) => {
          if (
            error !== "" &&
            (state.licenseErrMessage === "" ||
              error !== state.licenseErrMessage)
          ) {
            state.licenseErrMessage = error;
            state.watermark = true;
            state.licenseError = true;
          }
        }),
      setClearLicenseError: () =>
        set((state) => {
          state.licenseErrMessage = "";
          state.watermark = false;
          state.licenseError = false;
        }),
      disableLicenseError: () =>
        set((state) => {
          state.licenseError = false;
        }),
    })),
    {
      name: "SocketStore",
      enabled: process.env.NODE_ENV === "development",
    }
  )
);
