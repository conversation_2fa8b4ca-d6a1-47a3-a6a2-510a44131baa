package def

import "fmt"

// Document is uesed to represent store's search result. each document has content, metadata and score.
type Document struct {
	Content  string         `json:"content"`
	Metadata map[string]any `json:"metadata"`
	Score    float32        `json:"score"`
}

// SummarizeDocuments returns human-readable summary of the input documents.
func SummarizeDocuments(docs []Document) string {
	var summary string

	for idx, doc := range docs {
		// Add the content of the document to the summary
		docDesc := fmt.Sprintf("[%d] %s score:%f \n", idx, doc.Content, doc.Score)
		summary += docDesc
	}
	return summary
}
