import React from "react";
import { Item, Menu } from "react-contexify";
import { createStyles } from "antd-style";
import Icon, {
  ApartmentOutlined,
  CloseCircleFilled,
  CloseOutlined,
  ExclamationCircleFilled,
  GlobalOutlined,
  LineChartOutlined,
  PoweroffOutlined,
  SaveOutlined,
  SoundOutlined,
} from "@ant-design/icons";
import {
  MdAcUnit,
  MdAddTask,
  MdEvent,
  MdCloud,
  MdDelete,
} from "react-icons/md";
import { Flex } from "antd";

const useStyles = createStyles(({ token, css }) => ({
  contexify: css`
    --contexify-menu-bgColor: ${token.colorBgContainer};
    --contexify-separator-color: #4c4c4c;
    --contexify-item-color: ${token.colorText};
    --contexify-activeItem-bgColor: ${token.colorPrimaryActive};
  `,
}));

const DeviceContextMenu = ({ onMenuItemClicked }) => {
  const { styles, cx, theme } = useStyles();
  const nmsuserrole = sessionStorage.getItem("nmsuserrole");
  const items = [
    nmsuserrole !== "user" && {
      label: "Open in web",
      key: "openweb",
      icon: <GlobalOutlined />,
    },
    nmsuserrole !== "user" && {
      label: "Open in web via tunnel",
      key: "openwebtunnel",
      icon: <GlobalOutlined />,
    },
    {
      label: "Beep",
      key: "beep",
      icon: <SoundOutlined />,
    },
    {
      label: "Port Information",
      key: "portInfo",
      icon: <LineChartOutlined />,
    },
    {
      label: "Reboot",
      key: "reboot",
      icon: <PoweroffOutlined />,
    },
    {
      label: "Network Setting",
      key: "networkSetting",
      icon: <ApartmentOutlined />,
    },
    {
      label: "Syslog Setting",
      key: "syslogSetting",
      icon: <Icon component={MdEvent} />,
    },
    {
      label: "Trap Setting",
      key: "trapSetting",
      icon: <Icon component={MdAcUnit} />,
    },
    {
      label: "Enable SNMP",
      key: "enablesnmp",
      icon: <Icon component={MdAddTask} />,
    },
    {
      label: "Upload Firmware",
      key: "uploadFirmware",
      icon: <Icon component={MdCloud} />,
    },
    {
      label: "Save Running Config",
      key: "saveConfig",
      icon: <SaveOutlined />,
    },
  ];

  const massRowsItems = [
    {
      label: "Reboot",
      key: "massReboot",
      icon: <PoweroffOutlined />,
    },
    {
      label: "Syslog Setting",
      key: "massSyslogSetting",
      icon: <Icon component={MdEvent} />,
    },
    {
      label: "Trap Setting",
      key: "massTrapSetting",
      icon: <Icon component={MdAcUnit} />,
    },
    {
      label: "Enable SNMP",
      key: "massEnablesnmp",
      icon: <Icon component={MdAddTask} />,
    },
    {
      label: "Upload Firmware",
      key: "massUploadFirmware",
      icon: <Icon component={MdCloud} />,
    },
    {
      label: "Delete Device",
      key: "deleteDevice",
      icon: <Icon component={MdDelete} />,
    },
  ];

  const mdrMenuItems = [
    {
      label: "Network Setting",
      key: "setNetwork",
    },
    {
      label: "Set Led",
      key: "setLed",
    },
    {
      label: "Set MDR",
      key: "setMdr",
    },
    {
      label: "Set Profinet",
      key: "setProfinet",
    },
  ];
  function handleItemClick({ id, event, props, triggerEvent, data }) {
    console.log(id, event, props, triggerEvent, data);
    const { record, selectedRowKeys } = props;
    onMenuItemClicked(id, record, selectedRowKeys);
  }
  return (
    <div>
      <Menu id="device-menu" className={styles.contexify}>
        {items.map((item) => (
          <Item id={item.key} onClick={handleItemClick}>
            <Flex gap={10}>
              {item.icon}
              {item.label}
            </Flex>
          </Item>
        ))}
      </Menu>
      <Menu id="mass-menu" className={styles.contexify}>
        {massRowsItems.map((item) => (
          <Item id={item.key} onClick={handleItemClick}>
            <Flex gap={10}>
              {item.icon}
              {item.label}
            </Flex>
          </Item>
        ))}
      </Menu>
      <Menu id="mdr-menu" className={styles.contexify}>
        {mdrMenuItems.map((item) => (
          <Item id={item.key} onClick={handleItemClick}>
            <Flex gap={10}>{item.label}</Flex>
          </Item>
        ))}
      </Menu>
    </div>
  );
};

export default DeviceContextMenu;
