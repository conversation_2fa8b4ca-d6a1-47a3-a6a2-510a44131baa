package aiagent

import (
	"context"
	"encoding/json"
	"fmt"
	"mnms/aiagent/documents"
	"mnms/aiagent/documents/actions"
	"mnms/aiagent/documents/vectorstore"
	"mnms/llm"
	"os"
	"path/filepath"
	"runtime"

	"github.com/qeof/q"
	"github.com/spf13/viper"
)

var localLLMSettings *llm.LargeLanguageModel

// GetLLMSettings returns the local LLM settings
func GetLLMSettings() *llm.LargeLanguageModel {
	if localLLMSettings == nil {
		q.Q("init LLM settings")
		err := InitLLMSettings()
		if err != nil {
			q.Q("Failed to initialize LLM settings, use GPT default settings instead.", err)
			localLLMSettings = llm.GPTSettings()
		}
	}
	q.Q(localLLMSettings)
	return localLLMSettings
}

// Unmarshal into LargeLanguageModel
type LLMConfig struct {
	// open-ai or ollama
	Type string `json:"type" mapstructure:"type" yaml:"type"`
	// both open-ai and ollama should specify model like gpt-3 or llama3
	Model string `json:"model" mapstructure:"model" yaml:"model"`
	// open-ai api key, ollama will ignore this field
	APIKey string `json:"api_key" mapstructure:"api_key" yaml:"api_key"`
	// ollama host, open-ai will ignore this field
	Host string `json:"host" mapstructure:"host" yaml:"host"`
	// ollama port, open-ai will ignore this field
	Port int `json:"port" mapstructure:"port" yaml:"port"`
}

// SetLocalLLMSettings sets the local LLM settings
func SetLocalLLMSettings(config LLMConfig) error {

	openaiDemoSettings := llm.GPTSettings()
	switch config.Type {
	case "open-ai":
		localLLMSettings = llm.GPTSettings()
		localLLMSettings.APIKey = config.APIKey
		localLLMSettings.Model = config.Model
	case "ollama":
		localLLMSettings = llm.OllamaSettings(config.Model)
		localLLMSettings.Host = config.Host

	default:
		localLLMSettings = openaiDemoSettings
		return fmt.Errorf("unknown LLM server name (support open-ai | ollama )%s, system use default LLM settings", config.Type)
	}
	return nil
}

// InitLLMSettings initializes the AI agent, if system cannot find the configuration file, it will setup default settings
// which is the OpenAI GPT-3 model with demo API key.
func InitLLMSettings() error {
	v := viper.New()

	// Determine platform-specific paths
	var paths []string
	workingDir, _ := os.Getwd()
	if runtime.GOOS == "windows" {
		appData := os.Getenv("APPDATA")
		paths = []string{
			workingDir,
			filepath.Join(appData, "Roaming", "nimbl"),
			filepath.Join(appData, "Local", "nimbl"),
		}
	} else {
		homeDir, _ := os.UserHomeDir()
		paths = []string{
			workingDir,
			"/etc/nimbl",
			filepath.Join(homeDir, ".nimbl"),
		}
	}

	// Set up viper to read configuration
	v.SetConfigName("llmconfig") // File name without extension
	v.SetConfigType("yaml")      // File format
	v.AutomaticEnv()             // Allow overriding with environment variables

	// Search for the configuration file in the specified paths
	for _, path := range paths {
		v.AddConfigPath(path)
	}

	// Attempt to read the configuration file
	if err := v.ReadInConfig(); err != nil {
		fmt.Printf("No configuration file found: %v\n", err)
	}

	// Sample YAML configuration for LLM settings
	/*
		type: "open-ai"
		model: "gpt-3"
		api_key: "your-openai-api-key"
		host: "localhost"
		port: 11434
	*/

	var config LLMConfig
	if err := v.Unmarshal(&config); err != nil {
		localLLMSettings = llm.GPTSettings()
		// We don't want to provide the demo key in the AI agent
		localLLMSettings.APIKey = ""
		return fmt.Errorf("failed to unmarshal configuration: %w", err)
	}
	return SetLocalLLMSettings(config)

}

// AddActionReferenceString adds the action reference string to the document store
func AddActionReferenceString(ctx context.Context, actionRefStr string) error {
	actionRef := actions.ActionReference{}
	err := json.Unmarshal([]byte(actionRefStr), &actionRef)
	if err != nil {
		return fmt.Errorf("failed to unmarshal action reference: %w", err)
	}

	docRefs := ActionReferenceToDocumentRef(&actionRef)
	chromemStore := vectorstore.GetLocalChromemeStore(GetLLMSettings())
	q.Q(chromemStore.LLMSettings)
	err = chromemStore.AddDocumentRefs(ctx, docRefs)
	if err != nil {
		return err
	}
	return nil
}

// ActionReferenceToDocumentRef converts the action reference to document reference
func ActionReferenceToDocumentRef(actionRef *actions.ActionReference) []*vectorstore.DocumentRef {
	var docRefs []*vectorstore.DocumentRef
	for _, q := range actionRef.Queries {
		docRefs = append(docRefs, &vectorstore.DocumentRef{
			ItemID: actionRef.Action,
			DocKey: "actions",
			Query:  q,
		})
	}
	return docRefs
}

// InitLocalActionReferences initializes the AI agent
func InitLocalActionReferences(ctx context.Context, llmSettings *llm.LargeLanguageModel) (*vectorstore.ChromemeStore, error) {
	actionReferences, err := actions.GetPredefinedActionsReferences()
	if err != nil {
		return nil, err
	}
	// create DocumentRef
	var docRefs []*vectorstore.DocumentRef
	for _, act_ref := range actionReferences {
		refs := ActionReferenceToDocumentRef(&act_ref)
		docRefs = append(docRefs, refs...)
	}

	chromemStore := vectorstore.GetLocalChromemeStore(llmSettings)
	err = chromemStore.AddDocumentRefs(ctx, docRefs)
	if err != nil {
		return nil, err
	}
	return chromemStore, nil
}

// InitLocalActionsDocument initializes Actions docuemnt, the actions document is a document that contains all the actions
// information.
func InitLocalActionsDocument(ctx context.Context) error {
	documents.ExtraDocLock.Lock()
	defer documents.ExtraDocLock.Unlock()

	actions, err := actions.GetPredefinedActions()
	if err != nil {
		return err
	}
	q.Q("init actions", len(actions.Actions))
	q.Q(actions.GetName())
	documents.AddDocumentProviderToLocalProviders(actions)
	return nil
}
