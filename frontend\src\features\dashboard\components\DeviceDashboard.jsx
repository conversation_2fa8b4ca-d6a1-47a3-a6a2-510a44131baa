import { memo } from "react";
import { Col, Row } from "antd";
import MessageAlerts from "./MessageAlerts";
import Syslog<PERSON>hart from "./SyslogChart";
import DeviceStatistics from "./DeviceStatistics";
import CommandsList from "./CommandsList";
import { useDashboardData } from "../hooks/useDashboardData";

/**
 * Device Dashboard component that displays device statistics, syslog data,
 * command details, and alert messages
 * @returns {JSX.Element} Device Dashboard component
 */
const DeviceDashboard = () => {
  const { deviceData, syslogData, commandsData, messages, isLoading } =
    useDashboardData();

  return (
    <div>
      <Row gutter={[16, 16]}>
        {/* Left side - Charts and Statistics */}
        <Col xs={24} lg={16}>
          <Row gutter={[16, 16]}>
            <Col xs={24}>
              <DeviceStatistics data={deviceData} loading={isLoading} />
            </Col>
            <Col xs={24}>
              <SyslogChart data={syslogData} loading={isLoading} />
            </Col>
            <Col xs={24}>
              <CommandsList data={commandsData} loading={isLoading} />
            </Col>
          </Row>
        </Col>

        {/* Right side - Alert Messages */}
        <Col xs={24} lg={8}>
          <MessageAlerts messages={messages} />
        </Col>
      </Row>
    </div>
  );
};

export default memo(DeviceDashboard);
