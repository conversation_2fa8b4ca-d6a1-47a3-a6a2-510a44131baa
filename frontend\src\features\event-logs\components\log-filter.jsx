import dayjs from "dayjs";
import React, { useCallback, useMemo, useState } from "react";
import { Button, DatePicker, Flex, Form, InputNumber, Popover } from "antd";
import { FilterOutlined } from "@ant-design/icons";
import { useQueryClient } from "@tanstack/react-query";

const { RangePicker } = DatePicker;

const initialState = {
  count: 100,
  valueDate: null,
  open: false,
};

export const LogPageFilter = ({ refetch, reset }) => {
  const queryClient = useQueryClient();
  const [filterState, setFilterState] = useState(initialState);

  const resetState = useCallback(() => {
    setFilterState(initialState);
  }, []);

  const handleOpenChange = useCallback(
    (newOpen) => {
      if (!newOpen) {
        resetState();
      }
      setFilterState((prev) => ({ ...prev, open: newOpen }));
    },
    [resetState]
  );

  const handleDateChange = useCallback((dates) => {
    setFilterState((prev) => ({ ...prev, valueDate: dates }));
  }, []);

  const handleCountChange = useCallback((value) => {
    setFilterState((prev) => ({ ...prev, count: value }));
  }, []);

  const formatDateRange = useCallback((date) => {
    return date ? dayjs(date).format("YYYY/MM/DD HH:mm:ss") : "";
  }, []);

  const handleSubmit = useCallback(async () => {
    try {
      const { valueDate, count } = filterState;
      const params = {
        number: count,
        start: valueDate ? formatDateRange(valueDate[0]) : "",
        end: valueDate ? formatDateRange(valueDate[1]) : "",
      };
      refetch(params);
      await queryClient.invalidateQueries(["syslogs", params]);
    } catch (error) {
      console.log(error);
    } finally {
      handleOpenChange(false);
    }
  }, [filterState, handleOpenChange, formatDateRange, queryClient, refetch]);

  const FilterForm = useMemo(
    () => (
      <Form layout="vertical">
        <Form.Item label="Count" tooltip="Enter -1 for unlimited records">
          <InputNumber
            value={filterState.count}
            onChange={handleCountChange}
            min={-1}
            style={{ width: "100%" }}
            placeholder="Enter number of records"
          />
        </Form.Item>

        <Form.Item label="Date Range">
          <RangePicker
            value={filterState.valueDate}
            onChange={handleDateChange}
            format="YYYY/MM/DD HH:mm:ss"
            showTime
            style={{ width: "100%" }}
            disabledDate={(currentDate) =>
              currentDate?.isAfter(dayjs().endOf("day"))
            }
            placeholder={["Start Date", "End Date"]}
          />
        </Form.Item>

        <Flex gap={10} justify="flex-end">
          <Button onClick={() => handleOpenChange(false)}>Cancel</Button>
          <Button type="primary" onClick={handleSubmit}>
            Apply Filter
          </Button>
        </Flex>
      </Form>
    ),
    [
      filterState,
      handleCountChange,
      handleDateChange,
      handleOpenChange,
      handleSubmit,
    ]
  );

  const PopoverTitle = useMemo(
    () => (
      <Flex justify="space-between" align="center">
        <span>Filter Logs</span>
        <Button
          type="link"
          size="small"
          onClick={() => {
            resetState();
            reset();
          }}
        >
          Reset
        </Button>
      </Flex>
    ),
    [resetState]
  );

  return (
    <Popover
      placement="bottom"
      title={PopoverTitle}
      content={FilterForm}
      trigger="click"
      open={filterState.open}
      onOpenChange={handleOpenChange}
    >
      <Button icon={<FilterOutlined />} title="Filter logs" />
    </Popover>
  );
};

// Add display name for debugging
LogPageFilter.displayName = "LogPageFilter";
