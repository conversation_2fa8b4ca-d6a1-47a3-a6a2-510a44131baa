package actionchain

import (
	"fmt"
	"strings"
)

// Task is a struct that defines a task
type Task struct {
	Task    string   `yaml:"task"`    // A task description
	Actions []Action `yaml:"actions"` // A list of actions in the task
}

// Action is a struct that defines an action in a task
type Action struct {
	Type        string            `yaml:"type"`
	Action      string            `yaml:"action"`
	Description string            `yaml:"description"`
	Endpoint    string            `yaml:"endpoint"`
	Method      string            `yaml:"method"`
	QueryParams map[string]string `yaml:"query-params"`
}

// Validation is a struct that defines the validation of an action
type Validation struct {
	Description string            `yaml:"description"`
	Action      Action            `yaml:"action"`
	Expects     map[string]string `yaml:"expects"`
}

// DescribeTaskExecution describes the execution of a task
func DescribeTaskExecution(task Task) string {
	details := []string{}
	details = append(details, fmt.Sprintf("Task: %s", task.Task))
	details = append(details, "Actions Execution Details:")
	for i, action := range task.Actions {
		actionDetail := fmt.Sprintf("Action #%d: %s\n", i+1, action.Description)
		if action.Type == "API" {
			apiDetail := fmt.Sprintf("  - Type: %s\n    Action: %s\n    Endpoint: %s\n    Method: %s",
				action.Type, action.Action, action.Endpoint, action.Method)
			if len(action.QueryParams) > 0 {
				params := make([]string, 0, len(action.QueryParams))
				for k, v := range action.QueryParams {
					params = append(params, fmt.Sprintf("%s=%s", k, v))
				}
				apiDetail += fmt.Sprintf("\n    Query Params: %s", strings.Join(params, ", "))
			}
			actionDetail += apiDetail
		} else {
			commandDetail := fmt.Sprintf("  - Type: %s\n    Action: %s", action.Type, action.Action)
			actionDetail += commandDetail
		}
		details = append(details, actionDetail)
	}

	return strings.Join(details, "\n")
}
