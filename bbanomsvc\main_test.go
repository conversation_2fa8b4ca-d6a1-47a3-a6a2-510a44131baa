package main

import (
	"mnms"
	"mnms/anomaly_detection/statistic"
	"testing"
	"time"
)

// TestGetSyslogSvc tests the GetSyslogSvc function
func TestGetSyslogSvc(t *testing.T) {
	if mnms.QC.Name == "" {
		t.Log("pass test")
		return
	}
	statistic.LocalStatistic.Settings.Root = "http://localhost:27182"
	tok, err := mnms.GetToken("admin")

	if err != nil {
		t.<PERSON>("GetToken failed: %s", err)
	}
	statistic.LocalStatistic.Settings.Token = tok

	n, err := GetSyslogSvcName()
	if err != nil {
		t.Errorf("GetSyslogSvcName failed: %s", err)
	}
	t.Logf("GetSyslogSvcName: %s", n)
}

// TestTimeFormat tests the time format
func TestTimeFormat(t *testing.T) {
	interval := time.Duration(3) * time.Hour * 24
	interval += time.Duration(7) * time.Hour
	interval += time.Duration(30) * time.Minute
	startTime := time.Now().Add(-interval)
	endTime := time.Now()
	t.Log(startTime)
	t.Log(endTime)
	startTimeStr := startTime.Format("2006/01/02 15:04:05")
	endTimeStr := endTime.Format("2006/01/02 15:04:05")
	t.Log(startTimeStr)
	t.Log(endTimeStr)
}

// TestGetHost tests the GetHost function
func TestGetHost(t *testing.T) {
	h, err := gethost("http://localhost:27182")
	if err != nil {
		t.Fatal(err)
	}
	if h != "localhost" {
		t.Errorf("gethost failed: %s", h)
	}
	h, err = gethost("http://*******:1234/api/v1/xxx")
	if err != nil {
		t.Fatal(err)
	}
	if h != "*******" {
		t.Errorf("gethost failed: %s", h)
	}
	h, err = gethost("localhost:27182")
	if err != nil {
		t.Fatal(err)
	}
	if h != "localhost" {
		t.Errorf("gethost failed: %s", h)
	}

}
