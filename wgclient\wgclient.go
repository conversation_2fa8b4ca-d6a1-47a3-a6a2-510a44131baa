package main

// This package is used to join WireGuard peers to a WireGuard network. It is intended to be used as a library, but can also be used as a standalone executable.
//
//

import (
	"fmt"
	"os"
	"os/exec"
	"runtime"
	"strings"

	"github.com/qeof/q"
	"golang.zx2c4.com/wireguard/wgctrl/wgtypes"
)

type WgStatusPeerInfo struct {
	AllowedIPs          string `json:"allowed_ips"`
	LatestHandshake     string `json:"latest_handshake"`
	Transfer            string `json:"transfer"`
	PersistentKeepalive string `json:"persistent_keepalive"`
}

// WgStatusInfo is the wireguard status information from wg command
type WgStatusInfo struct {
	Interface string                      `json:"interface"`
	PublicKey string                      `json:"public_key"`
	Peers     map[string]WgStatusPeerInfo `json:"peers"`
}

type WgConfig struct {
	Interface WgInterface `json:"interface"`
	Peers     []WgPeer    `json:"peers"`
}

// WgInterface is the lite wireguard interface configuration
type WgInterface struct {
	PrivateKey string   `json:"-"`
	PublicKey  string   `json:"public_key"`
	Addresses  []string `json:"addresses"`
	ListenPort int      `json:"listen_port"`
	MTU        uint16   `json:"mtu"`
	DNS        []string `json:"dns"`
	PreUp      []string `json:"pre_up"`
	PostUp     []string `json:"post_up"`
	PreDown    []string `json:"pre_down"`
	PostDown   []string `json:"post_down"`
}

// WgPeer is the lite wireguard peer configuration.
type WgPeer struct {
	PublicKey           string   `json:"public_key"`
	PresharedKey        string   `json:"preshared_key"`
	AllowedIPs          []string `json:"allowed_ips"`
	Endpoint            string   `json:"endpoint"`
	PersistentKeepalive uint16   `json:"persistent_keepalive"`
}

type WgInfo struct {
	Enabled bool   `json:"enabled"`
	Name    string `json:"name"`

	Config WgConfig     `json:"config"`
	Status WgStatusInfo `json:"status"`
}

func generateWgKey() (wgtypes.Key, error) {
	key, err := wgtypes.GeneratePrivateKey()
	if err != nil {
		return wgtypes.Key{}, err
	}
	return key, nil
}

func NewWgConfig() *WgConfig {
	// Create a new WgConfig object
	key, err := generateWgKey()
	if err != nil {
		q.Q(err)
	}
	return &WgConfig{
		Interface: WgInterface{
			PrivateKey: key.String(),
			PublicKey:  key.PublicKey().String(),
		},
	}
}

func (wg *WgInfo) SetInterface(iface WgInterface) {
	wg.Config.Interface = iface
}

func (wg *WgInfo) GetInterface() WgInterface {
	return wg.Config.Interface
}

func (wg *WgInfo) AddPeer(peer WgPeer) {
	wg.Config.Peers = append(wg.Config.Peers, peer)
}

func (wg *WgInfo) RemovePeer(index int) {
	wg.Config.Peers = append(wg.Config.Peers[:index], wg.Config.Peers[index+1:]...)
}

func (wg *WgInfo) UpdatePeer(index int, peer WgPeer) error {
	if index >= len(wg.Config.Peers) {
		return fmt.Errorf("index out of range")
	}
	wg.Config.Peers[index] = peer
	return nil
}

func (wg *WgInfo) GetPeer(index int) WgPeer {
	if index < len(wg.Config.Peers) {
		return wg.Config.Peers[index]
	}
	return WgPeer{}
}

func (wg *WgInfo) GetPeers() []WgPeer {
	return wg.Config.Peers
}

// Save saves the WgConfig to current directory as a WireGuard configuration file.
func (wg *WgInfo) Save(filename string) error {

	s := fmt.Sprintf(`[Interface]
PrivateKey = %s
Address = %s`, wg.Config.Interface.PrivateKey, strings.Join(wg.Config.Interface.Addresses, ", "))
	// listen port
	if wg.Config.Interface.ListenPort > 0 {
		s += fmt.Sprintf(`
ListenPort = %d`, wg.Config.Interface.ListenPort)
	}
	// mtu
	if wg.Config.Interface.MTU > 0 {
		s += fmt.Sprintf(`
MTU = %d`, wg.Config.Interface.MTU)
	}
	// dns
	if len(wg.Config.Interface.DNS) > 0 {
		s += `
DNS = `
		for i, dns := range wg.Config.Interface.DNS {
			s += dns
			if i != len(wg.Config.Interface.DNS)-1 {
				s += ", "
			}
		}
	}
	// PreUp, PostUp, PreDown, PostDown
	for _, preup := range wg.Config.Interface.PreUp {
		s += fmt.Sprintf(`
PreUp = %s`, preup)
	}
	for _, postup := range wg.Config.Interface.PostUp {
		s += fmt.Sprintf(`
PostUp = %s`, postup)
	}
	for _, predown := range wg.Config.Interface.PreDown {
		s += fmt.Sprintf(`
PreDown = %s`, predown)
	}
	for _, postdown := range wg.Config.Interface.PostDown {
		s += fmt.Sprintf(`
PostDown = %s`, postdown)
	}
	// peer adds
	for _, peer := range wg.Config.Peers {
		// add a new line
		s += `
`
		s += fmt.Sprintf(`
[Peer]
PublicKey = %s
AllowedIPs = `, peer.PublicKey)
		for i, addr := range peer.AllowedIPs {
			s += addr
			if i != len(peer.AllowedIPs)-1 {
				s += ", "
			}
		}
		// preshared key
		if len(peer.PresharedKey) > 0 {
			s += fmt.Sprintf(`
PresharedKey = %s`, peer.PresharedKey)
		}
		// endpoint
		if len(peer.Endpoint) > 0 {
			s += fmt.Sprintf(`
Endpoint = %s`, peer.Endpoint)
		}
		// persistent keepalive
		if peer.PersistentKeepalive > 0 {
			s += fmt.Sprintf(`
PersistentKeepalive = %d`, peer.PersistentKeepalive)
		}
	}

	// write to file
	return os.WriteFile(fmt.Sprintf("%s.conf", wg.Name), []byte(s), 0644)
}

// WgStart starts the WireGuard service with the given configuration file.
// Note that this file must be in the current directory.
func WgStart(name string) error {
	// get current path
	dir, err := os.Getwd()
	if err != nil {
		q.Q("failed to get current path", err)
		return err
	}
	// if windows
	if runtime.GOOS == "windows" {
		// tunnel service: wireguard /installtunnelservice C:\path\to\some\myconfname.conf
		cmd := exec.Command("wireguard.exe", "/installtunnelservice", fmt.Sprintf("%s\\%s.conf", dir, name))
		err = cmd.Run()
		if err != nil {
			q.Q("failed to install wireguard service", err)
			return err
		}
		return nil
	}

	if runtime.GOOS == "linux" {
		// Usage: wg-quick [ up | down | save | strip ] [ CONFIG_FILE | INTERFACE ]
		cmd := exec.Command("wg-quick", "up", fmt.Sprintf("%s/%s.conf", dir, name))
		err = cmd.Run()
		if err != nil {
			q.Q("failed to start wireguard service", err)
			return err
		}
		return nil
	}

	return fmt.Errorf("unsupported os %s", runtime.GOOS)
}

func WgStop(name string) error {
	// get current path
	dir, err := os.Getwd()
	if err != nil {
		q.Q("failed to get current path", err)
		return err
	}

	if runtime.GOOS == "windows" {
		//wireguard /uninstalltunnelservice myconfname
		cmd := exec.Command("wireguard.exe", "/uninstalltunnelservice", name)
		err := cmd.Run()
		if err != nil {
			q.Q("failed to uninstall wireguard service", err)
			return err
		}
		return nil
	}

	if runtime.GOOS == "linux" {
		// Usage: wg-quick [ up | down | save | strip ] [ CONFIG_FILE | INTERFACE ]
		cmd := exec.Command("wg-quick", "down", fmt.Sprintf("%s/%s.conf", dir, name))
		err := cmd.Run()
		if err != nil {
			q.Q("failed to stop wireguard service", err)
			return err
		}
		return nil
	}

	return fmt.Errorf("unsupported os %s", runtime.GOOS)
}

func WgStatus(iface string) (WgStatusInfo, error) {
	cmd := exec.Command("wg", "show", iface)
	out, err := cmd.Output()
	if err != nil {
		if len(string(out)) == 0 {
			return WgStatusInfo{}, err
		}
	}

	// parse output
	wgInfo := WgStatusInfo{}
	wgInfo.Peers = make(map[string]WgStatusPeerInfo)
	lines := strings.Split(string(out), "\n")
	wgPeerInfo := WgStatusPeerInfo{}
	var wgPeer string
	for _, line := range lines {

		if strings.Contains(line, "interface:") {
			wgInfo.Interface = strings.TrimSpace(strings.Split(line, ":")[1])
		}
		if strings.Contains(line, "public key:") {
			wgInfo.PublicKey = strings.TrimSpace(strings.Split(line, ":")[1])
		}
		if strings.Contains(line, "peer:") {
			if wgPeer != "" {
				wgInfo.Peers[wgPeer] = wgPeerInfo
			}
			wgPeer = strings.TrimSpace(strings.Split(line, ":")[1])
			wgPeerInfo = WgStatusPeerInfo{}
		}

		if strings.Contains(line, "allowed ips:") {
			wgPeerInfo.AllowedIPs = strings.TrimSpace(strings.Split(line, ":")[1])
		}

		if strings.Contains(line, "latest handshake:") {
			wgPeerInfo.LatestHandshake = strings.TrimSpace(strings.Split(line, ":")[1])
		}

		if strings.Contains(line, "transfer:") {
			wgPeerInfo.Transfer = strings.TrimSpace(strings.Split(line, ":")[1])
		}

		if strings.Contains(line, "persistent keepalive:") {
			wgPeerInfo.PersistentKeepalive = strings.TrimSpace(strings.Split(line, ":")[1])
		}
	}
	if wgPeer != "" {
		wgInfo.Peers[wgPeer] = wgPeerInfo
	}
	return wgInfo, nil
}
