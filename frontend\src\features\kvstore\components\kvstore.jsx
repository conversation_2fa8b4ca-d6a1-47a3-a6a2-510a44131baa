import { memo } from "react";
import { Row, Col } from "antd";
import KVStoreForm from "./KVStoreForm";
import KVStoreList from "./KVStoreList";

/**
 * Main KVStore component that combines the form and list components
 * @returns {JSX.Element} KVStore component
 */
const KVStore = () => {
  return (
    <Row gutter={[16, 16]}>
      <Col xs={24} md={8}>
        <KVStoreForm />
      </Col>
      <Col xs={24} md={16}>
        <KVStoreList />
      </Col>
    </Row>
  );
};

export default memo(KVStore);
