package documents

import (
	"context"
	"encoding/json"
	"fmt"
	"mnms/aiagent/documents/actions"
	"mnms/aiagent/documents/vectorstore"

	"github.com/austinjan/chromem-go"
	"github.com/qeof/q"
)

// Document used for storing extra knowledge that can be used for LLM

func Log(msg string) {
	fmt.Println(msg)
}

// CombineJSONStrings takes a slice of JSON strings, unmarshals each into an interface{},
// and returns a single JSON []byte representing an array of these elements.
func CombineJSONStrings(jsonStrings []string) ([]byte, error) {
	var combined []interface{}

	for _, s := range jsonStrings {
		var obj interface{}
		if err := json.Unmarshal([]byte(s), &obj); err != nil {
			return nil, fmt.Errorf("failed to unmarshal JSON string: %w", err)
		}
		combined = append(combined, obj)
	}

	result, err := json.Marshal(combined)
	if err != nil {
		return nil, fmt.<PERSON><PERSON><PERSON>("failed to marshal combined JSON: %w", err)
	}

	return result, nil
}

type DocumentProviders []DocumentProvider

// localDocumentProviders is a global variable that holds all the document providers
var localDocumentProviders DocumentProviders

// GetLocalDocumentProviders returns the local document providers
func GetLocalDocumentProviders() *DocumentProviders {
	if localDocumentProviders == nil {
		localDocumentProviders = DocumentProviders{}
	}
	return &localDocumentProviders
}

// AddDocumentProviderToLocalProviders adds a document provider to the local providers
func AddDocumentProviderToLocalProviders(doc DocumentProvider) {
	localDocumentProviders = append(localDocumentProviders, doc)
}

// NaturalLanguageResolver is an interface designed for parsing natural language queries.
// To better connect natural language and Documents, the AI assist introduces an intermediate layer
// to create more potential connections. In a basic RAG (Retrieval-Augmented Generation) framework,
// a document's content is typically compared to the input natural language by calculating vectors.
// However, the accuracy of such comparisons can vary depending on the user's habits and understanding
// of natural language. To address this, AI assist utilizes an LLM to introduce an intermediate layer.
// This layer maps multiple pieces of content to the document's keys, enhancing the connection possibilities.

type NaturalLanguageResolver interface {
	// ResolveKey processes a natural language query and returns a key that can be used
	// to access specific document. If the resolution fails, it returns an error.
	// To retrieve the key, refer to Result.Metadata["item_key"] and Result.Metadata["doc_key"].
	// For example:
	// DocumentProviders.GetDocument(ctx, Result.Metadata["item_key"], Result.Metadata["doc_key"]).
	ResolveKey(ctx context.Context, query string) (chromem.Result, error)

	ResolveKeys(ctx context.Context, query string, nResults int) ([]chromem.Result, error)
}

// GetDocumentItems returns a document items with pagination
func (d *DocumentProviders) GetDocumentItems(ctx context.Context, category string, query string, page int, limit int) ([]string, error) {
	// find doc
	if len(*d) == 0 {
		return nil, fmt.Errorf("no documents in the provider")
	}
	var output []string
	var err error
	for _, doc := range *d {
		// check ctx
		select {
		case <-ctx.Done():
			return output, fmt.Errorf("context is done")
		default:
			if doc.GetName() == category {
				output, err = doc.Query(query)
				if err != nil {
					return nil, fmt.Errorf("query: %s to %s got error: %s", query, doc.GetName(), err.Error())
				}
				Log(fmt.Sprintf("[%s] Query: %s \n Out: %s", doc.GetName(), query, output))
				break
			}
		}
	}
	// pagination
	start := (page - 1) * limit
	end := start + limit
	if start >= len(output) {
		return nil, fmt.Errorf("page %d is out of range", page)
	}
	if end > len(output) {
		end = len(output)
	}
	return output[start:end], nil
}

// GetDocumentProvider returns a document by its ID
func (d *DocumentProviders) GetDocumentProvider(docName string) (DocumentProvider, error) {
	if len(*d) == 0 {
		return nil, fmt.Errorf("no documents in the provider")
	}

	for _, doc := range *d {
		if doc.GetName() == docName {
			return doc, nil
		}
	}
	return nil, fmt.Errorf("document %s not found", docName)
}

// GetDocumentItem returns a document by its ID
func (d *DocumentProviders) GetDocumentItem(ctx context.Context, docName string, id string) (string, error) {
	if len(*d) == 0 {
		return "", fmt.Errorf("no documents in the provider")
	}
	var output string
	var err error
	for _, doc := range *d {
		// check ctx
		select {
		case <-ctx.Done():
			return output, fmt.Errorf("context is done")
		default:
			if doc.GetName() == docName {
				output, err = doc.Find(id)
				if err != nil {
					return "", fmt.Errorf("id: %s to %s got error: %s", id, doc.GetName(), err.Error())
				}

				break
			}
		}
	}
	return output, nil
}

type docProvidersSerialize struct {
	Data json.RawMessage `json:"data"`
	Name string          `json:"name"`
}

// Serialize returns a serialized version of the document providers
func (d *DocumentProviders) Serialize() ([]byte, error) {
	ExtraDocLock.Lock()
	defer ExtraDocLock.Unlock()
	var data []docProvidersSerialize
	for _, doc := range *d {
		serialized, err := doc.Serialize()
		if err != nil {
			return nil, err
		}
		data = append(data, docProvidersSerialize{
			Data: serialized,
			Name: doc.GetName()})
	}
	return json.Marshal(data)
}

// NewDocumentFromName returns a new document based on the name
func NewDocumentFromName(name string) (DocumentProvider, error) {
	switch name {
	case "actions":
		return actions.NewActionDefinitions(), nil
	default:
		return nil, fmt.Errorf("unknown document type: %s", name)
	}
}

// ListDocuments returns a list of documents
func (d *DocumentProviders) ListDocuments() []string {
	ExtraDocLock.Lock()
	defer ExtraDocLock.Unlock()
	var names []string
	for _, doc := range *d {
		names = append(names, doc.GetName())
	}
	return names
}

// DeserializeDocumantProviders populates the DocumentProviders from a serialized []byte
func DeserializeDocumantProviders(data []byte) (DocumentProviders, error) {

	// Temporary structure to hold serialized data
	var serializedData []docProvidersSerialize

	// Unmarshal the input JSON
	if err := json.Unmarshal(data, &serializedData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal data: %w", err)
	}

	// Clear the existing DocumentProviders slice
	d := DocumentProviders{}

	// Iterate through serialized data and reconstruct documents
	for _, serializedDoc := range serializedData {
		doc, err := DeserializeLocalMemoryDocumentProvider(serializedDoc.Data)
		if err != nil {
			return nil, fmt.Errorf("failed to deserialize document provider: %w", err)
		}
		// Append the reconstructed document to the slice
		d = append(d, doc)
	}

	return d, nil
}

// GetDocumentItem()
func GetDocumentItemFromChememResult(ctx context.Context, providers DocumentProviders, result chromem.Result) (string, error) {
	meta := result.Metadata
	docKey, ok := meta["doc_key"]
	if !ok {
		return "", fmt.Errorf("doc_key not found")
	}
	itemKey, ok := meta["item_id"]
	if !ok {
		return "", fmt.Errorf("item_key not found")
	}

	return providers.GetDocumentItem(ctx, docKey, itemKey)
}

// GetRelevantDocumentItems returns a list of relevant document items
func (d *DocumentProviders) GetRelevantDocumentItems(ctx context.Context, query string, doc string, store *vectorstore.ChromemeStore, nResults int) ([]string, error) {

	resolver, err := store.GetReference(doc)
	if err != nil {
		return nil, err
	}

	results, err := resolver.QueryWithNResult(ctx, query, nResults)
	if err != nil {
		return nil, fmt.Errorf("Resolve key fail: %v", err)
	}

	var docs []string
	for _, ret := range results {
		if ret.Similarity < 0.8 {
			continue
		}

		s, err := GetDocumentItemFromChememResult(ctx, *d, ret)
		if err != nil {
			q.Q(err)
			continue
		}
		docs = append(docs, s)
	}

	return docs, nil
}
