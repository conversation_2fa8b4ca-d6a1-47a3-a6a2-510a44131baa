package loganalyse

import (
	"encoding/json"
	"fmt"
	"os"
	"path"
	"regexp"
	"testing"
	"time"
)

var testlogs = []string{
	`<134>Jun 25 04:04:26 combo logrotate: ALERT exited abnormally with [1]`,
	`<1>May 15 15:23:05 jilllocal InsertDev: new device: 00-60-E9-2E-BE-E0`,
	`<1>May 15 15:35:34 jilllocal trapserver: {"Version":2,"TrapType":0,"OID":null,"Other":null,"Community":"public","Username":"","Address":"**************:43808","VarBinds":{".*******.*******.0":253046190000000,".*******.*******.1.1":8,".*******.*******.1.7":1,".*******.*******.1.8":1,".*******.*******.4.1.0":[1,3,6,1,6,3,1,1,5,4]},"VarBindOIDs":[".*******.*******.0",".*******.*******.4.1.0",".*******.*******.1.1",".*******.*******.1.7",".*******.*******.1.8"]}`,
	`<134>Jun 20 09:20:07 combo sshd(pam_unix)[10043]: check pass; user unknown`,
}

// TestLogParser is a helper function to parse log
func TestLogParser(t *testing.T) {
	logStruct, err := ParseLog(testlogs[0])
	if err != nil {
		t.Errorf("parse log error: %v", err)
	}
	if logStruct.Priority != 134 {
		t.Errorf("parse log facility error: %v", logStruct.Priority)
	}
	if logStruct.Timestamp != "Jun 25 04:04:26" {
		t.Errorf("parse log timestamp error: %v", logStruct.Timestamp)
	}
	if logStruct.Hostname != "combo" {
		t.Errorf("parse log hostname error: %v", logStruct.Hostname)
	}
	if logStruct.Tag != "logrotate" {
		t.Errorf("parse log tag error: %v", logStruct.Tag)
	}
	if logStruct.Message != "ALERT exited abnormally with [1]" {
		t.Errorf("parse log message error: %v", logStruct.Message)
	}
	t.Log(logStruct)
	// parse log 2
	logStruct, err = ParseLog(testlogs[1])
	if err != nil {
		t.Errorf("parse log error: %v", err)
	}
	if logStruct.Priority != 1 {
		t.Errorf("parse log facility error: %v", logStruct.Priority)
	}
	if logStruct.Timestamp != "May 15 15:23:05" {
		t.Errorf("parse log timestamp error: %v", logStruct.Timestamp)
	}
	if logStruct.Hostname != "jilllocal" {
		t.Errorf("parse log hostname error: %v", logStruct.Hostname)
	}
	if logStruct.Tag != "InsertDev" {
		t.Errorf("parse log tag error: %v", logStruct.Tag)
	}
	if logStruct.Message != "new device: 00-60-E9-2E-BE-E0" {
		t.Errorf("parse log message error: %v", logStruct.Message)
	}
	t.Log(logStruct)

}

// TestSyslogFileAnalyseOpt is a helper function to test SyslogFileAnalyseOpt
func TestSyslogFileAnalyseOpt(t *testing.T) {
	opt := SyslogFileAnalyseOpt{
		LastLines: 0,
		// start time 2023/10/5
		StartTime: time.Date(2023, 10, 5, 0, 0, 0, 0, time.UTC),
		// end time 2023/10/5 14:00:32
		EndTime: time.Date(2023, 10, 5, 14, 0, 32, 0, time.UTC),
	}

	err := opt.SetTime("Oct 5 10:00:26", "Oct 5 14:00:32")
	if err != nil {
		t.Fatal(err)
	}

	between, err := opt.IsTimeBetween("Oct 5 10:04:26")
	if !between {
		t.Errorf("time between error: %v", err)
	}

	// check time 10/4 14:00:33
	between, err = opt.IsTimeBetween("Oct 4 10:04:26")
	if between {
		t.Errorf("time between error: %v", between)
	}
}

// TestReadLastLines is a helper function to test ReadLastLines
func TestReadLastLines(t *testing.T) {
	// get os temp dir
	tempDir := os.TempDir()
	// write a temp file each line like 1 text
	tempFile := path.Join(tempDir, "temp.log")
	file, err := os.OpenFile(tempFile, os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		t.Fatal(err)
	}

	for i := 0; i < 100; i++ {
		text := fmt.Sprintf("%d text\n", i)
		file.WriteString(text)
	}
	file.Close()

	file, err = os.Open(tempFile)
	if err != nil {
		t.Fatal(err)
	}

	// read last 10 lines
	lines, err := readLastNLines(file, 10)
	if err != nil {
		file.Close()
		t.Fatal(err)
	}
	if len(lines) != 10 {
		t.Errorf("read last lines error: %v", lines)
	}
	// check first line
	if lines[0] != "90 text" {
		t.Errorf("read last lines error: %v", lines)
	}
	// check last line
	if lines[9] != "99 text" {
		t.Errorf("read last lines error: %v", lines)
	}
	file.Close()
	// remove temp file
	err = os.Remove(tempFile)
	if err != nil {
		t.Fatal(err)
	}
}

// TestConvertSyslogTime is a helper function to test ConvertSyslogTime
func TestConvertSyslogTime(t *testing.T) {
	log1 := `<5>May 15 15:31:42 jilllocal RunCmd: {"kind":"","timestamp":"2023-05-15T15:31:59+08:00","command":"beep 00-60-E9-1A-3B-89 **************","result":"","status":"ok","name":"jilllocal","nooverwrite":false,"all":false,"nosyslog":false,"client":"","devid":"00-60-E9-1A-3B-89","tag":""}`
	convertedTime, err := convertSyslogTime(log1)
	if err != nil {
		t.Fatal(err)
	}
	// check convertedTime is 1983/5/15 15:31:42
	if convertedTime != time.Date(1983, 5, 15, 15, 31, 42, 0, time.UTC) {
		t.Errorf("convert syslog time error: %v", convertedTime)
	}
	log2 := "Jul 17 14:02:49"
	convertedTime, err = convertSyslogTime(log2)
	if err != nil {
		t.Fatal(err)
	}
	// check convertedTime is 1983/7/17 14:02:49
	if convertedTime != time.Date(1983, 7, 17, 14, 2, 49, 0, time.UTC) {
		t.Errorf("convert syslog time error: %v", convertedTime)
	}
}

func TestExtractFirstJSON(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
		hasError bool
	}{
		{
			name: "Valid JSON",
			input: `{
				"message": "This is a valid JSON",
				"anomaly": false,
				"reason": null
			}`,
			expected: `{
				"message": "This is a valid JSON",
				"anomaly": false,
				"reason": null
			}`,
			hasError: false,
		},
		{
			name: "JSON with non-JSON prefix",
			input: `ret={
				"message": "<11> A strange message",
				"anomaly": false,
				"reason": null
			}`,
			expected: `{
				"message": "<11> A strange message",
				"anomaly": false,
				"reason": null
			}`,
			hasError: false,
		},
		{
			name: "JSON with nested braces in string",
			input: `ret={
				"message": "<11> {A TIME} unknown: a strange message, every night!!!",
				"anomaly": false,
				"reason": null
			}`,
			expected: `{
				"message": "<11> {A TIME} unknown: a strange message, every night!!!",
				"anomaly": false,
				"reason": null
			}`,
			hasError: false,
		},
		{
			name:     "No valid JSON",
			input:    `This is not a JSON string`,
			expected: "",
			hasError: true,
		},
		{
			name:  "markdown JSON ",
			input: "```json\n{\n  \"message\": \"This is a valid JSON\",\n  \"anomaly\": false,\n  \"reason\": null\n}\n```",
			expected: `{
				"message": "This is a valid JSON",
				"anomaly": false,
				"reason": null
			}`,
			hasError: false,
		},
		{
			name: "multiple braces",
			input: `ret={
           "message": "<134>{A TIME} combo ftpd[23939]: connection from {A IPV4 ADDRESS} () at Sun {A TIME} 2005  ",
           "anomaly": false,
           "reason": "Normal operation" }`,
			expected: `{
				"message": "<134>{A TIME} combo ftpd[23939]: connection from {A IPV4 ADDRESS} () at Sun {A TIME} 2005  ",
				"anomaly": false,
				"reason": "Normal operation"
			}`,
			hasError: false,
		},
		{
			name:  "time test",
			input: `{"message":"<11> [MASKED TIME] unknown: the unrecognized message"}`,
			expected: `{
				"message": "<11> [MASKED TIME] unknown: the unrecognized message"
			}`,
			hasError: false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result, err := extractFirstJSON(test.input)
			if (err != nil) != test.hasError {
				t.Errorf("Expected error: %v, got: %v", test.hasError, err != nil)
			}

			var resultMap, expectedMap map[string]interface{}
			if err := json.Unmarshal([]byte(result), &resultMap); err != nil && result != "" {
				t.Errorf("Failed to unmarshal result: %v", err)
			}
			if err := json.Unmarshal([]byte(test.expected), &expectedMap); err != nil && test.expected != "" {
				t.Errorf("Failed to unmarshal expected: %v", err)
			}

			if !equalJSON(resultMap, expectedMap) {
				t.Errorf("Expected result: %v, got: %v", expectedMap, resultMap)
			}
		})
	}
}

// equalJSON
func equalJSON(a, b map[string]interface{}) bool {
	return fmt.Sprintf("%v", a) == fmt.Sprintf("%v", b)
}

// TestingSyslog
func TestSyslogFromLogsvc(t *testing.T) {
	logs := []string{
		"<6>Sep  2 16:46:55 log1 bblogsvc: log service is forwarding",
		`<134>Jun 25 04:04:26 combo logrotate: ALERT exited abnormally with [1]`,
		`<1>May 15 15:23:05 jilllocal InsertDev: new device: 00-60-E9-2E-BE-E0`,
		`<1>May 15 15:35:34 jilllocal trapserver: {"Version":2,"TrapType":0,"OID":null,"Other":null,"Community":"public","Username":"","Address":"**************:43808","VarBinds":{".*******.*******.0":253046190000000,".*******.*******.1.1":8,".*******.*******.1.7":1,".*******.*******.1.8":1,".*******.*******.4.1.0":[1,3,6,1,6,3,1,1,5,4]},"VarBindOIDs":[".*******.*******.0",".*******.*******.4.1.0",".*******.*******.1.1",".*******.*******.1.7",".*******.*******.1.8"]}`,
		`<134>Jun 20 09:20:07 combo sshd(pam_unix)[10043]: check pass; user unknown`,
	}

	p := regexp.MustCompile(`^<(\d+)>(\w+\s+\d+\s+\d+:\d+:\d+)\s+(\S+)\s+bblogsvc: log service is forwarding$`)

	for _, log := range logs {
		matches := p.FindStringSubmatch(log)
		if len(matches) == 0 {
			t.Logf("parse log error: %v", log)
			continue
		}
		msg := fmt.Sprintf("<%s>  - [ %s ] >> %s ", matches[1], matches[2], matches[3])
		t.Log(msg)
	}
}
