export const IP_REGEX =
  /^((?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){1}$/;

export const FORM_FIELDS = {
  LOG_TO_FLASH: "logToFlash",
  LOG_LEVEL: "logLevel",
  LOG_TO_SERVER: "logToServer",
  SERVER_IP: "serverIP",
  SERVER_PORT: "serverPort",
};

export const VALIDATION_RULES = {
  [FORM_FIELDS.SERVER_IP]: [
    { required: true, message: "Please input the server IP!" },
    { pattern: IP_REGEX, message: "Invalid IP format" },
  ],
  [FORM_FIELDS.SERVER_PORT]: [
    { required: true, message: "Please input the server service port!" },
  ],
};
