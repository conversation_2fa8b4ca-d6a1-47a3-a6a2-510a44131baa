#!/bin/bash

function check_anomaly_http_status() {
    echo "Checking anomaly http status"
    response=$(curl -s http://localhost:27183/api)
    # check if the response matches the expected string
    if [ "$response" == "anomaly service says hello" ]; then
        echo "[OK] Anomaly service is running"
    else
        echo "[FAIL] Anomaly service is not running"
    fi
}

# Run root & bbanomaly
if [ "$1" == "root" ]; then
    echo "Running root"
    ../../mnmsctl/mnmsctl -R -n root -P ".*" 
elif [ "$1" == "bbanomaly" ]; then
    echo "Running bbanomaly"
    ./bbanomaly -n testan1 -r http://localhost:27182 -p 27183 -P ".*" 
elif [ "$1" == "test-http" ]; then
    echo "Running test"
    check_anomaly_http_status
else
    echo "Usage: test.sh [command]
    commands:
    - root: run the root anomaly detection service
    - bbanomaly: run the bbanomaly service
    - test-http: test the http status of the anomaly service
    
    test.sh root"
fi


# the detect command
# ./mnmsctl.exe anomaly detect file:///e:/code/bbtechhive/mnms/mnmsctl/rawlog.log

# configdb command
# How to run multiple instances of postgresql on the same machine for testing
## Go to postgresql bin folder (/e/Program Files/PostgreSQL/16/bin)
# - Create a volume:  initdb -D {volume_path} 
# - Start the server: pg_ctl -D {volume_path} -l logfile start
##     pg_ctl.exe -D ^"E^:^\postgres2^" -l E^:^\logfile -o "-p 5433" start  (in windows)
##     pg_ctl.exe -D ^"E^:^\postgres2^" -l E^:^\logfile stop  (in windows)
# - Connect to the server: psql -p 5433 -U postgres
#      psql.exe -p 5433 -U austi -d postgres
# - Use postgres_conf.yaml or command configdb [host] [port] [user] [password] to configure the database
# Testing command, 
