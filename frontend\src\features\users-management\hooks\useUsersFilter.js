import { useState, useMemo } from "react";
import { useGetAllUsers } from "../../../services/queries";
import { useUserColumn } from "../../../components/table-column/user-table";

export const useUsersFilters = () => {
  const [inputSearch, setInputSearch] = useState("");
  const columns = useUserColumn();
  const { data = [], isFetching, refetch } = useGetAllUsers();
  const filteredData = useMemo(() => {
    const searchTerm = inputSearch.toLowerCase();
    return data.filter((row) =>
      columns.some((column) =>
        String(row[column.dataIndex]).toLowerCase().includes(searchTerm)
      )
    );
  }, [data, inputSearch]);

  return {
    filteredData,
    isFetching,
    refetch,
    inputSearch,
    setInputSearch,
    columns,
  };
};
