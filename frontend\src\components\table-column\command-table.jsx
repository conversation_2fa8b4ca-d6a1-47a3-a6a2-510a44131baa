import React, { useCallback } from "react";
import dayjs from "dayjs";
import { App, Button, Space, Tooltip, Typography } from "antd";
import { DeleteOutlined, RetweetOutlined } from "@ant-design/icons";
import { handleCommandExecution } from "../../utils/command-execution";
import { useSendCommand } from "../../services/mutations";

export const useCommandTableColumn = () => {
  const { notification } = App.useApp();
  const sendCommand = useSendCommand();

  const handleActionClick = useCallback(
    async (record, type) => {
      try {
        const command =
          type === "delete"
            ? [
                {
                  command: record.command,
                  edit: "delete",
                  client: record.client,
                },
              ]
            : [
                {
                  command: record.command,
                  client: record.client,
                  kind: record.kind,
                },
              ];

        await handleCommandExecution(command, sendCommand, notification);
      } catch (error) {
        notification.error({
          message: "Command Execution Failed",
          description: error.message,
        });
      }
    },
    [sendCommand, notification]
  );

  return React.useMemo(
    () => [
      {
        title: "CMD",
        dataIndex: "command",
        key: "command",
        exportable: true,
        width: 580,
      },
      {
        title: "Status",
        dataIndex: "status",
        key: "status",
        width: 150,
        exportable: true,
        align: "center",
      },
      {
        title: "Timestamp",
        dataIndex: "timestamp",
        key: "timestamp",
        defaultSortOrder: "descend",
        exportable: true,
        sorter: (a, b) => dayjs(a.timestamp).unix() - dayjs(b.timestamp).unix(),
        render: (timestamp) => dayjs(timestamp).format("YYYY/MM/DD HH:mm:ss"),
        width: 200,
        align: "center",
      },
      {
        title: "Result",
        dataIndex: "result",
        key: "result",
        width: 400,
        align: "top",
        exportable: false,
        render: (data) => (
          <Typography.Paragraph
            ellipsis={{
              rows: 2,
              defaultExpanded: false,
              expandable: "collapsible",
            }}
            style={{ overflow: "auto", maxHeight: "250px" }}
          >
            {data}
          </Typography.Paragraph>
        ),
      },
      {
        title: "Service Name",
        dataIndex: "client",
        key: "client",
        render: (_, record) => record.client || record.name,
        width: 150,
        exportable: true,
        align: "center",
      },
      {
        title: "Index",
        dataIndex: "index",
        key: "index",
        width: 100,
        exportable: false,
        align: "center",
      },
      {
        title: "Action",
        dataIndex: "index",
        key: "action",
        width: 150,
        align: "center",
        fixed: "right",
        exportable: false,
        render: (_, record) => (
          <Space size={10}>
            <Tooltip title="delete cmd">
              <Button
                aria-label="delete cmd"
                danger
                icon={<DeleteOutlined />}
                size="small"
                onClick={() => handleActionClick(record, "delete")}
              />
            </Tooltip>
            <Tooltip title="rerun cmd">
              <Button
                aria-label="rerun cmd"
                icon={<RetweetOutlined />}
                size="small"
                onClick={() => handleActionClick(record, "rerun")}
              />
            </Tooltip>
          </Space>
        ),
      },
    ],
    [handleActionClick]
  );
};
