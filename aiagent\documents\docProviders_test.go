package documents

import "testing"

// TestSerialize tests the serialization of a document
func TestSerialize(t *testing.T) {
	docsProvider := DocumentProviders{}
	// Add some docProvider
	doc1 := NewLocalMemoryDocumentProvider("doc1", "doc1 description")
	doc2 := NewLocalMemoryDocumentProvider("doc2", "doc2 description")

	// add some fake data
	doc1.Update("id1", "id1_content")
	doc2.Update("id2", "id2_content")

	docsProvider = append(docsProvider, doc1)
	docsProvider = append(docsProvider, doc2)

	// Serialize the document providers
	data, err := docsProvider.Serialize()
	if err != nil {
		t.Fatalf("Serialize failed: %s", err.Error())
	}

	// Deserialize the document providers
	docProviders2, err := DeserializeDocumantProviders(data)
	if err != nil {
		t.Fatalf("Deserialize failed: %s", err.<PERSON>rror())
	}
	// Check if the deserialized document providers are the same as the original
	doc11, err := docProviders2.GetDocumentProvider("doc1")
	if err != nil {
		t.Fatalf("GetDocumentProvider failed: %s", err.Error())
	}
	ret, err := doc11.Find("id1")
	if err != nil {
		t.Fatalf("Find failed: %s", err.Error())
	}
	t.Log("ret: ", ret)
}
