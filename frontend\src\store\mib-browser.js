import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";
import { persist } from "zustand/middleware";

/**
 * Initial state for the MIB Browser
 * @type {Object}
 */
const initialState = {
  // Command response data
  cmdResponse: [],

  // SNMP parameters
  ip_address: "",
  oid: ".1.3",
  operation: "get",
  value: "",
  valueType: "OctetString",
};

/**
 * MIB Browser store for managing SNMP operations and results
 * @type {import("zustand").UseBoundStore}
 */
export const useuseMibBrowserStore = create(
  devtools(
    persist(
      immer((set) => ({
        // Initial state
        ...initialState,

        /**
         * Update SNMP parameters
         * @param {Object} params - Parameters to update
         */
        updateSnmpParams: (params) =>
          set((state) => {
            Object.assign(state, params);
          }),

        /**
         * Add a command response to the list
         * @param {Object} response - Command response to add
         */
        addCmdResponse: (response) =>
          set((state) => {
            state.cmdResponse.push(response);
          }),

        /**
         * Set the entire command response list
         * @param {Array} responses - Command responses to set
         */
        setCmdResponses: (responses) =>
          set((state) => {
            state.cmdResponse = responses;
          }),

        /**
         * Clear all command responses
         */
        clearCmdResponses: () =>
          set((state) => {
            state.cmdResponse = [];
          }),

        /**
         * Reset all SNMP parameters to initial values
         */
        resetSnmpParams: () => set(initialState),
      })),
      {
        name: "nimbl-mib-browser",
        partialize: (state) => ({
          // Only persist these fields
          ip_address: state.ip_address,
          oid: state.oid,
          operation: state.operation,
          value: state.value,
          valueType: state.valueType,
        }),
      }
    ),
    {
      name: "mib-browser-store",
      enabled: process.env.NODE_ENV === "development",
    }
  )
);
