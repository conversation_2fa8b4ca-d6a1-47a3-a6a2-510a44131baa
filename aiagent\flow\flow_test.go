package flow

import (
	"mnms/aiagent/node"
	"testing"
)

var testingFlow = Flow{
	Name:        "testing",
	Description: "test flow",
	Nodes: []node.Node{
		{
			Name: "test",
			ID:   "1-1",
		},
		{
			Name: "test",
			ID:   "1-2",
		},
		{
			Name: "test",
			ID:   "1-3",
		},
		{
			Name: "test",
			ID:   "2-1",
		},
	},
	Edges: []Edge{
		{
			ID:     "1-1-1-2",
			Source: "1-1",
			Target: "1-2",
		},
		{
			ID:     "1-2-1-3",
			Source: "1-2",
			Target: "1-3",
		},
	},
}

// TestingFlow returns a testing flow
func TestFlow(t *testing.T) {
	heads := testingFlow.HeadNodes()
	if len(heads) != 2 {
		t.<PERSON><PERSON><PERSON>("Expected 2 head node, got %d", len(heads))
	}
	for _, head := range heads {
		info := head.Info()
		if info.ID != "1-1" && info.ID != "2-1" {
			t.<PERSON>("Expected head node to be 1-1 or 2-1, got %s", info.ID)
		}
	}
	// Testing NextNodes
	nexts := testingFlow.NextNodes("1-1")
	if len(nexts) != 1 {
		t.<PERSON><PERSON><PERSON>("Expected 1 next node, got %d", len(nexts))
	}
	info := nexts[0].Info()
	if info.ID != "1-2" {
		t.Errorf("Expected next node to be 1-2, got %s", info.ID)
	}
	nexts = testingFlow.NextNodes("1-2")
	if len(nexts) != 1 {
		t.Errorf("Expected 1 next node, got %d", len(nexts))
	}
	info = nexts[0].Info()

	if info.ID != "1-3" {
		t.Errorf("Expected next node to be 1-3, got %s", info.ID)
	}
	nexts = testingFlow.NextNodes("1-3")
	if len(nexts) != 0 {
		t.Errorf("Expected 0 next node, got %d", len(nexts))
	}
	nexts = testingFlow.NextNodes("2-1")
	if len(nexts) != 0 {
		t.Errorf("Expected 0 next node, got %d", len(nexts))
	}
}
