import * as React from "react";
import { Outlet, createRootRoute } from "@tanstack/react-router";
import { TanStackRouterDevtools } from "@tanstack/react-router-devtools";
import Dialogs from "../components/dialogs/dialogs";

export const Route = createRootRoute({
  component: RootComponent,
});

export function RootComponent() {
  return (
    <React.Fragment>
      <Outlet />
      <Dialogs />
      <TanStackRouterDevtools initialIsOpen={false} />
    </React.Fragment>
  );
}
