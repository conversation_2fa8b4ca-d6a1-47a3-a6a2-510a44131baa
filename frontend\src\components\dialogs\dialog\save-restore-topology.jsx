import { App, Form, Input, Modal } from "antd";
import React, { useCallback } from "react";
import { useSaveRestoreTopology } from "../../../services/mutations";

const SaveRestoreTopology = ({ data, onClose }) => {
  const [form] = Form.useForm();
  const { notification } = App.useApp();
  const saveRestoreTopology = useSaveRestoreTopology();
  const actionType = data?.actionType;

  const handleSubmit = useCallback(
    async (values) => {
      try {
        await saveRestoreTopology.mutateAsync({
          filename: `${values.filename}.json`,
          actionType,
        });

        notification.success({
          message: "Success",
          description:
            actionType === "save"
              ? "Topology saved successfully"
              : "Topology restored successfully",
        });

        form.resetFields();
        onClose();
      } catch (error) {
        console.error(`Failed to ${actionType} topology:`, error);
        notification.error({
          message: `Error ${actionType === "save" ? "saving" : "restoring"} topology`,
          description: error.message || "Something went wrong",
        });
      }
    },
    [actionType, form, notification, onClose, saveRestoreTopology]
  );

  return (
    <Modal
      title="Save/Restore Topology"
      open
      destroyOnClose
      onCancel={onClose}
      style={{ top: 20 }}
      maskClosable={false}
      okButtonProps={{ autoFocus: true, htmlType: "submit" }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            handleSubmit(values);
            // Form will be reset in handleSubmit on success
          })
          .catch((info) => {
            console.error("Form validation failed:", info);
            notification.error({
              message: "Validation Error",
              description: "Please enter a valid filename",
            });
          });
      }}
    >
      <Form
        name="save_restore_topology_form"
        form={form}
        autoComplete="off"
        layout="vertical"
        clearOnDestroy
      >
        <Form.Item
          name="filename"
          label="Filename"
          rules={[
            {
              required: true,
              message: "Missing filename!",
            },
          ]}
        >
          <Input placeholder="Filename" suffix=".json" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default SaveRestoreTopology;
