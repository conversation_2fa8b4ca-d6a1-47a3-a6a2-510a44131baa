package chain

import "context"

type Options struct {
	CallbackFunc    func(context.Context, map[string]any)
	DebugOutputFile string
	ReportFile      string
	Score           float32
	AddRAG          bool
}

func WithAddRAG(add bool) OptionAssigner {
	return func(o *Options) {
		o.AddRAG = add
	}
}

func WithReportFile(filepath string) OptionAssigner {
	return func(o *Options) {
		o.ReportFile = filepath
	}
}

func WithDebugOutput(filepath string) OptionAssigner {
	return func(o *Options) {
		o.DebugOutputFile = filepath
	}
}

func WithCallback(f func(context.Context, map[string]any)) OptionAssigner {
	return func(o *Options) {
		o.CallbackFunc = f
	}
}

func WithScoreThreshold(threshold float32) OptionAssigner {
	return func(o *Options) {
		o.Score = threshold
	}
}

type OptionAssigner func(*Options)
