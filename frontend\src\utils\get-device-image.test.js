import { describe, it, expect, vi, beforeEach } from 'vitest';
import { getDeviceImage } from './get-device-image';

describe('get-device-image', () => {
  const defaultDeviceImage = '/src/assets/images/default-device.png';
  
  // Mock the Image constructor
  const originalImage = global.Image;
  
  beforeEach(() => {
    // Reset console.error mock
    vi.spyOn(console, 'error').mockImplementation(() => {});
    
    // Mock Image constructor
    global.Image = class {
      constructor() {
        this.complete = false;
        this.naturalWidth = 0;
        this.src = '';
      }
      
      set src(value) {
        this._src = value;
        // Simulate image loading success for specific model names
        if (value.includes('Model-X')) {
          this.complete = true;
          this.naturalWidth = 100;
        }
      }
      
      get src() {
        return this._src;
      }
    };
  });
  
  afterEach(() => {
    // Restore original Image constructor
    global.Image = originalImage;
    vi.restoreAllMocks();
  });
  
  it('should return default image when model name is not provided', () => {
    // Act
    const result = getDeviceImage();
    
    // Assert
    expect(result).toBe(defaultDeviceImage);
  });
  
  it('should return default image when model name is empty string', () => {
    // Act
    const result = getDeviceImage('');
    
    // Assert
    expect(result).toBe(defaultDeviceImage);
  });
  
  it('should return device image URL when image loads successfully', () => {
    // Arrange
    const modelName = 'Model-X';
    const expectedUrl = `https://nimbl.blackbeartechhive.com/api/v1/files/device-images/${modelName}.png`;
    
    // Act
    const result = getDeviceImage(modelName);
    
    // Assert
    expect(result).toBe(expectedUrl);
  });
  
  it('should return default image when image fails to load', () => {
    // Arrange
    const modelName = 'Unknown-Model';
    
    // Act
    const result = getDeviceImage(modelName);
    
    // Assert
    expect(result).toBe(defaultDeviceImage);
  });
  
  it('should handle URL creation errors and return default image', () => {
    // Arrange
    const modelName = 'Invalid/Model\\Name';
    
    // Mock URL constructor to throw an error
    const originalURL = global.URL;
    global.URL = function() {
      throw new Error('Invalid URL');
    };
    
    // Act
    const result = getDeviceImage(modelName);
    
    // Assert
    expect(result).toBe(defaultDeviceImage);
    expect(console.error).toHaveBeenCalledWith(
      'Error creating device image URL:',
      expect.any(Error)
    );
    
    // Restore URL constructor
    global.URL = originalURL;
  });
});
