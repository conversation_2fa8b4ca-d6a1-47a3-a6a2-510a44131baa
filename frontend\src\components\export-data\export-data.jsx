import { FileExcelOutlined, FilePdfOutlined } from "@ant-design/icons";
import { App, Button, Dropdown, notification } from "antd";
import jsPDF from "jspdf";
import { applyPlugin } from "jspdf-autotable";
import { CSVLink } from "react-csv";
import React, { useEffect, useRef, useState } from "react";
import { getFilename } from "../../utils/topologyUtils";

applyPlugin(jsPDF);

const items = [
  {
    label: "PDF",
    key: "pdf",
    icon: <FilePdfOutlined style={{ fontSize: 15 }} />,
  },
  {
    label: "CSV",
    key: "csv",
    icon: <FileExcelOutlined style={{ fontSize: 15 }} />,
  },
];

const ExportData = ({ Columns, DataSource, title }) => {
  const csvLink = useRef();
  const [csvHeader, setCsvHeader] = useState([]);
  const [csvData, setCsvData] = useState([]);
  const { notification } = App.useApp();

  const showError = (message) => {
    notification.error({
      message: "Export Error",
      description: message,
    });
  };

  const processExportData = () => {
    const headers = Columns.filter((col) => col.exportable).map(
      (col) => col.title
    );
    const data = DataSource.map((item) => {
      return Columns.filter((col) => col.exportable).map(
        (col) => item[col.key]
      );
    });
    return { headers, data };
  };

  const handlePdfDownload = () => {
    try {
      const { headers, data } = processExportData();
      exportPDF(data, [headers], title);
    } catch (error) {
      showError("Failed to generate PDF. Please try again.");
    }
  };

  const handleCsvDownload = () => {
    try {
      const { headers, data } = processExportData();
      setCsvHeader(headers);
      setCsvData(data);
    } catch (error) {
      showError("Failed to generate CSV. Please try again.");
    }
  };

  const handleMenuClick = (e) => {
    if (e.key === "pdf") {
      handlePdfDownload();
    } else {
      handleCsvDownload();
    }
  };

  useEffect(() => {
    if (csvData.length > 0) csvLink.current.link.click();
  }, [csvData]);

  return (
    <>
      <Dropdown
        menu={{ items, onClick: handleMenuClick }}
        placement="bottomLeft"
      >
        <Button type="primary">Export</Button>
      </Dropdown>
      <CSVLink
        headers={csvHeader}
        data={csvData}
        filename={getFilename(title)}
        className="hidden"
        ref={csvLink}
        target="_blank"
      />
    </>
  );
};

export default ExportData;

const defaultPdfConfig = {
  unit: "pt",
  size: "A4",
  orientation: "landscape",
  margins: {
    top: 40,
    left: 40,
    right: 40,
    bottom: 40,
  },
  fontSize: 12,
  tableStartY: 50,
};

const exportPDF = (data, headers, title, config = {}) => {
  try {
    const pdfConfig = { ...defaultPdfConfig, ...config };
    const doc = new jsPDF(
      pdfConfig.orientation,
      pdfConfig.unit,
      pdfConfig.size
    );

    doc.setFontSize(pdfConfig.fontSize);

    const content = {
      startY: pdfConfig.tableStartY,
      head: headers,
      body: data,
      margin: pdfConfig.margins,
      styles: {
        fontSize: 10,
        cellPadding: 5,
        overflow: "linebreak",
        halign: "left",
      },
      headStyles: {
        fillColor: [71, 71, 71],
        textColor: [255, 255, 255],
        fontStyle: "bold",
      },
      alternateRowStyles: {
        fillColor: [245, 245, 245],
      },
    };

    doc.text(title, pdfConfig.margins.left, pdfConfig.margins.top);
    doc.autoTable(content);
    doc.save(getFilename(title));
  } catch (error) {
    console.error("Error generating PDF:", error);
    // You might want to add proper error handling here, such as showing a notification to the user
  }
};
