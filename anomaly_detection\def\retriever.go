package def

import "context"

// Retriever is an interface that defines how to retrieve relevant data (Retrieval augmented generation).
type Retriever interface {
	Add(ctx context.Context, content string, meta map[string]any) (int64, error)
	AddWithThreshold(ctx context.Context, content string, meta map[string]any, threshold float32) (int64, error)
	GetReleventDocuments(ctx context.Context, query string, count int) ([]Document, error)
	GetReleventDocumentsWithThreshold(ctx context.Context, query string, count int, threshold float32) ([]Document, error)
}
