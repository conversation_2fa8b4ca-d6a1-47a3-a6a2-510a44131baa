package mnms

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
)

// startMCPTestServer starts the MCP server for testing, returning the base URL
func startMCPTestServer(port int) error {
	mcpServer, err := NewMCPServer()
	if err != nil {
		return err
	}

	baseURL := "http://localhost:" + fmt.Sprintf("%d", port)
	sseServer := server.NewSSEServer(mcpServer, server.WithBaseURL(baseURL))
	fmt.Printf("Starting MCP server on port %d\n", port)
	if err := sseServer.Start(":" + fmt.Sprintf("%d", port)); err != nil {
		fmt.Println("Error starting MCP server:", err)
		return err
	}

	return nil
}

func TestMCPServer(t *testing.T) {
	port, err := randomOpenPort()
	if err != nil {
		t.Fatalf("Error getting random open port: %v", err)
	}

	go startMCPTestServer(port)

	time.Sleep(1 * time.Second)

	t.Run("Can initialize and make requests", func(t *testing.T) {
		client, err := client.NewSSEMCPClient("http://localhost:" + fmt.Sprintf("%d", port) + "/sse")
		if err != nil {
			t.Fatalf("Failed to create client: %v", err)
		}
		defer client.Close()

		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		// Start the client
		if err := client.Start(ctx); err != nil {
			t.Fatalf("Failed to start client: %v", err)
		}

		// Initialize
		initRequest := mcp.InitializeRequest{}
		initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
		initRequest.Params.ClientInfo = mcp.Implementation{
			Name:    "test-client",
			Version: "1.0.0",
		}

		result, err := client.Initialize(ctx, initRequest)
		if err != nil {
			t.Fatalf("Failed to initialize: %v", err)
		}

		if result.ServerInfo.Name != "nimbl-mcp-server" {
			t.Errorf(
				"Expected server name 'nimbl-mcp-server', got '%s'",
				result.ServerInfo.Name,
			)
		}

		// Test Ping
		if err := client.Ping(ctx); err != nil {
			t.Errorf("Ping failed: %v", err)
		}

		// Test ListTools
		toolsRequest := mcp.ListToolsRequest{}
		_, err = client.ListTools(ctx, toolsRequest)
		if err != nil {
			t.Errorf("ListTools failed: %v", err)
		}
	})
}
