import { <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON> } from "@antv/g-svg";
import { Graph as G6Graph, NodeEvent } from "@antv/g6";
import { useCallback, useEffect, useRef } from "react";
import { useTheme } from "antd-style";
import "./fly-marker-line";
import { getDeviceImage } from "../../utils/get-device-image";
import { useTopologyStore } from "../../store/topology-store";

export const Graph = ({ options, onRender, onDestroy }) => {
  const token = useTheme();
  const graphRef = useRef();
  const containerRef = useRef(null);
  const resizeObserverRef = useRef(null);
  // Import the topology store
  const { saveNodePositions } = useTopologyStore();

  // Add new effect to handle container resizing
  useEffect(() => {
    const container = containerRef.current;
    const graph = graphRef.current;

    if (!container || !graph) return;

    const handleResize = () => {
      const { clientWidth, clientHeight } = container;
      graph.setSize(clientWidth, clientHeight);
      graph
        .draw()
        .then(() => {
          graph.stopLayout();
          onRender?.(graph, container);
        })
        .catch((error) => console.debug(error));
    };

    // Create ResizeObserver
    resizeObserverRef.current = new ResizeObserver(handleResize);
    resizeObserverRef.current.observe(container);

    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
    };
  }, [containerRef, graphRef, onRender]);

  // Memoized savePositions function
  const savePositions = useCallback(() => {
    try {
      if (!graphRef.current) {
        console.warn("Graph reference is not available");
        return;
      }

      const nodes = graphRef.current.getNodeData().map((node) => ({
        id: node.id,
        style: {
          x: node.style.x ?? 0,
          y: node.style.y ?? 0,
        },
      }));

      // Save positions to the store
      saveNodePositions(nodes);

      if (process.env.NODE_ENV === "development") {
        console.log("Saving topology positions:", nodes);
      }
    } catch (error) {
      console.error("Failed to save topology positions:", error);
    }
  }, [saveNodePositions]); // Dependency on saveNodePositions

  useEffect(() => {
    const graph = new G6Graph({
      container: containerRef.current || undefined,
      width: containerRef.current ? containerRef.current.clientWidth : 0,
      height: containerRef.current?.clientHeight ?? 500,
      autoFit: "center",
      layout: {
        type: "force",
        linkDistance: 200,
        animation: false,
        preventOverlap: true,
        center: [250, 250],
      },
      autoResize: true,
      zoomRange: [0.1, 5],
      padding: 10,
      behaviors: ["drag-canvas", "zoom-canvas", "drag-element"],
      renderer: () => new SVGRenderer(),
    });

    graph.on(NodeEvent.DRAG_END, savePositions); // Using memoized function

    graphRef.current = graph;
    const container = containerRef.current;
    if (!container) return;

    // Handle drag end event

    graph
      .render()
      .then(() => {
        graph.stopLayout();
        onRender?.(graph, container);
      })
      // eslint-disable-next-line no-console
      .catch((error) => console.debug(error));

    return () => {
      const graph = graphRef.current;
      if (graph) {
        graph.destroy();
        onDestroy?.();
      }
    };
  }, []);

  useEffect(() => {
    const container = containerRef.current;
    const graph = graphRef.current;

    if (!options || !container || !graph || graph.destroyed) return;

    graph.setOptions({
      ...options,
      node: {
        type: "image",
        style: {
          x: (d) => d.style?.x ?? 0,
          y: (d) => d.style?.y ?? 0,
          size: [56, 56],
          label: true,
          src: (d) => getDeviceImage(d.modelname),
          labelText: (d) => {
            return `${d.ipAddress}\n${d.macAddress}\n${d.modelname}`;
          },
          labelFill: token.colorText,
          labelFontSize: 16,
          portDisplay: false,
          ports: [{ placement: "center" }],
        },
      },
      edge: {
        type: "fly-marker-line",
        style: (d) => ({
          label: true,
          labelText: `${d.source}_${d.sourcePort}\n${d.target}_${d.targetPort}`,
          labelFill: token.colorText,
          labelFontSize: 12,
          lineWidth: 2,
          labelAutoRotate: false,
          stroke:
            d.blockedPort === "true" ? "#faad14" : token.colorTextDisabled,
          circleColor:
            d.blockedPort === "true" ? "transparent" : token.colorPrimary,
          markerConfig: {
            duration: 3000,
            radius: 5,
            iterations: Infinity,
          },
        }),
      },
    });

    graph
      .draw()
      .then(() => {
        graph.stopLayout();
        onRender?.(graph, container);
      })
      // eslint-disable-next-line no-console
      .catch((error) => console.debug(error));
  }, [options, onRender, token]);

  return (
    <div
      ref={containerRef}
      style={{
        width: "100%",
        height: "100%",
        position: "relative",
        border: "1px solid gray",
        overflow: "hidden",
        background: token.colorBgContainer,
      }}
    />
  );
};
