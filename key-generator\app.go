package main

import (
	"bytes"
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os"

	"github.com/wailsapp/wails/v2/pkg/runtime"
)

// App struct
type App struct {
	ctx context.Context
}

// NewA<PERSON> creates a new App application struct
func NewApp() *App {
	return &App{}
}

// startup is called when the app starts. The context is saved
// so we can call the runtime methods
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx
}

// Greet returns a greeting for the given name
func (a *App) Greet(name string) string {
	return fmt.Sprintf("Hello %s, It's show time!", name)
}

func (a *App) GenetateKey(services int, devices int, machineID string) string {
	key := []byte("nmskeygeneratoruniquekey")
	keyText := make(map[string]interface{})
	keyText["program"] = "mnms"
	keyText["version"] = "v1.0.2"
	keyText["generated"] = "https://license.blackbeartechhive.com"
	keyText["numClients"] = services
	keyText["numOfDevice"] = devices
	keyText["machineID"] = machineID
	keyText["contact"] = "<EMAIL>"

	jsonkeybyte, _ := json.Marshal(keyText)

	block, err := aes.NewCipher(key)
	if err != nil {
		panic(err)
	}

	ciphertext := make([]byte, aes.BlockSize+len(jsonkeybyte))
	iv := ciphertext[:aes.BlockSize]
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		panic(err)
	}

	stream := cipher.NewCTR(block, iv)
	stream.XORKeyStream(ciphertext[aes.BlockSize:], jsonkeybyte)

	homeDir, err := os.UserHomeDir()
	check(a.ctx, err)
	filepath, err := runtime.SaveFileDialog(a.ctx, runtime.SaveDialogOptions{
		DefaultDirectory:           homeDir,
		DefaultFilename:            "nmskey",
		Title:                      "Choose where to save file to",
		Filters:                    nil,
		ShowHiddenFiles:            false,
		CanCreateDirectories:       true,
		TreatPackagesAsDirectories: false,
	})
	check(a.ctx, err)
	if !FileExists(filepath) {
		CreateFile(filepath)
	}
	err = os.WriteFile(filepath, ciphertext, 0o644)
	check(a.ctx, err)

	return fmt.Sprintf("File saved location: %s", filepath)
}

func (a *App) GenetatePlainText() string {
	key := []byte("nmskeygeneratoruniquekey")
	block, err := aes.NewCipher(key)
	check(a.ctx, err)
	homeDir, err := os.UserHomeDir()
	check(a.ctx, err)
	filepath, err := runtime.OpenFileDialog(a.ctx, runtime.OpenDialogOptions{
		DefaultDirectory:           homeDir,
		DefaultFilename:            "nmskey",
		Title:                      "Choose file to open",
		Filters:                    nil,
		ShowHiddenFiles:            false,
		CanCreateDirectories:       true,
		TreatPackagesAsDirectories: false,
	})
	check(a.ctx, err)
	data, err := os.ReadFile(filepath)
	if len(data) == 0 {
		check(a.ctx, errors.New("license file is corrupt and cannot be opened"))
		return "license file is corrupt and cannot be opened"
	}
	check(a.ctx, err)
	iv := data[:aes.BlockSize]
	plaintext2 := make([]byte, len(data)-aes.BlockSize)
	stream := cipher.NewCTR(block, iv)
	stream.XORKeyStream(plaintext2, data[aes.BlockSize:])
	var out bytes.Buffer
	err = json.Indent(&out, plaintext2, "", "\t")
	check(a.ctx, err)
	return out.String()
}

func FileExists(name string) bool {
	if _, err := os.Stat(name); err != nil {
		if os.IsNotExist(err) {
			return false
		}
	}
	return true
}

func CreateFile(name string) error {
	fo, err := os.Create(name)
	if err != nil {
		return err
	}
	defer func() {
		fo.Close()
	}()
	return nil
}

func check(ctx context.Context, e error) {
	if e != nil {
		runtime.MessageDialog(ctx, runtime.MessageDialogOptions{
			Type:    runtime.ErrorDialog,
			Title:   "Error",
			Message: e.Error(),
		})
	}
}
