package result

// package result defines the common result of chain's action result

import "fmt"

// Response represents http request's response.
type Response struct {
	Status string         `json:"status"` // success, ok or err: reason
	Data   map[string]any `json:"data"`
}

// NewErrorResponse represents an error response.
func NewErrorResponse(err error) *Response {
	status := fmt.Sprintf("err: %s", err.Error())
	return &Response{
		Status: status,
		Data:   nil,
	}
}
