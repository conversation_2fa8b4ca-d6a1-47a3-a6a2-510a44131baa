import React, { memo, useCallback } from "react";
import { useTheme } from "antd-style";
import { ColorPicker } from "antd";
import { useSettingStore } from "../store/setting-store";

// Constants
const COLOR_PRESETS = {
  BRAND_COLORS: {
    label: "Brand Colors",
    colors: [
      "#3B71CA", // Primary Blue
      "#F5222D", // Red
      "#FA541C", // Sunset Orange
      "#FAAD14", // Warning Yellow
      "#13C3C3", // Cyan
      "#52C41A", // Success Green
      "#2F54EB", // Royal Blue
      "#722ED1", // Purple
    ],
  },
};

const ColorSwatchComponent = () => {
  const { changePrimaryColor } = useSettingStore();
  const token = useTheme();

  const handleColorChange = useCallback(
    (color) => {
      changePrimaryColor(color.toHexString());
    },
    [changePrimaryColor]
  );

  return (
    <ColorPicker
      presets={[COLOR_PRESETS.BRAND_COLORS]}
      defaultValue={token.colorPrimary}
      onChangeComplete={handleColorChange}
      showText
      size="middle"
      placement="bottomLeft"
      trigger="click"
      format="hex"
      arrow
    />
  );
};

ColorSwatchComponent.propTypes = {
  // Add props if needed in the future
};

// Export memoized component
export default memo(ColorSwatchComponent);

// Type definitions for TypeScript users
/**
 * @typedef {Object} ColorPreset
 * @property {string} label - The label for the preset group
 * @property {string[]} colors - Array of hex color values
 */

/**
 * @typedef {Object} ColorPresetsType
 * @property {ColorPreset} BRAND_COLORS - Brand color presets
 */
