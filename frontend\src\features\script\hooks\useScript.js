import { useState, useCallback } from 'react';
import { App } from 'antd';
import dayjs from 'dayjs';
import { useSendCommand } from '../../../services/mutations';
import { useGetCommandResult, useGetAllCommands } from '../../../services/queries';
import { useScriptStore } from '../../../store/script-store';
import { processCommands } from '../../../utils/script-utils';
import { handleCommandExecution } from '../../../utils/command-execution';

/**
 * Custom hook for Script functionality
 * @returns {Object} Script state and functions
 */
export const useScript = () => {
  const { notification, message } = App.useApp();
  const sendCommand = useSendCommand();
  const [cmdResult, setCmdResult] = useState(null);
  
  // Get store state and actions
  const {
    inputCmd,
    cmdFlags,
    cmdResponse,
    setInputCmd,
    setCmdFlags,
    updateCmdFlag,
    clearCmdFlags,
    clearInputCmd,
    setCmdResponse,
  } = useScriptStore();
  
  // Get command result data
  const { data: resultData } = useGetCommandResult(
    cmdResult?.command ? encodeURIComponent(cmdResult.command) : null
  );
  
  // Get all commands for download functionality
  const { refetch: refetchAllCommand } = useGetAllCommands();

  /**
   * Handle running the command
   * @returns {Promise<void>}
   */
  const handleRunCommand = useCallback(async () => {
    setCmdResult(null);
    
    try {
      const command = processCommands(inputCmd, cmdFlags);
      
      if (command.length === 0) {
        notification.warning({
          message: "No Command",
          description: "Please enter a command to run"
        });
        return;
      }
      
      const response = await handleCommandExecution(
        command,
        sendCommand,
        notification
      );
      
      if (response.success) {
        setCmdResponse(response.data);
      }
    } catch (error) {
      console.error("Error running command:", error);
      notification.error({
        message: "Error",
        description: "Failed to execute command"
      });
    }
  }, [inputCmd, cmdFlags, sendCommand, notification, setCmdResponse]);

  /**
   * Handle file upload
   * @returns {Object} Upload props for Ant Design Upload component
   */
  const getUploadProps = useCallback(() => ({
    name: "cmdfile",
    multiple: false,
    accept: "text/plain",
    customRequest: ({ onSuccess }) => {
      setTimeout(() => {
        onSuccess("ok");
      }, 0);
    },
    showUploadList: false,
    onChange({ file, fileList }) {
      if (file.status !== "uploading") {
        console.log(file, fileList);
      }
      if (file.status === "done") {
        message.success(`${file.name} file uploaded successfully`);
        const reader = new FileReader();
        reader.onload = async (e) => {
          const text = e.target.result;
          setInputCmd(text);
        };
        reader.readAsText(file.originFileObj);
      } else if (file.status === "error") {
        message.error(`${file.name} file upload failed.`);
      }
    },
  }), [message, setInputCmd]);

  /**
   * Download all commands as a JSON file
   */
  const downloadFile = useCallback(() => {
    refetchAllCommand().then((res) => {
      const data = res.data;
      const fileName = `all_commands_${dayjs().format("YYYYMMDD_HHmmss")}.json`;
      const jsonData = JSON.stringify(data, null, 2);
      const blob = new Blob([jsonData], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = fileName;
      a.click();
      URL.revokeObjectURL(url);
    });
  }, [refetchAllCommand]);

  return {
    // State
    inputCmd,
    cmdFlags,
    cmdResponse,
    resultData,
    
    // Actions
    setInputCmd,
    setCmdFlags,
    updateCmdFlag,
    clearCmdFlags,
    clearInputCmd,
    handleRunCommand,
    setCmdResult,
    downloadFile,
    getUploadProps,
  };
};
