import { App, Form, Input, Modal, Select } from "antd";
import React, { useCallback, useEffect } from "react";
import { FORM_FIELDS, VALIDATION_RULES } from "../../../constants/user-form";
import {
  useCreateUserMutation,
  useEditUserMutation,
} from "../../../services/mutations";

const roles = [
  { value: "user", label: "User" },
  { value: "admin", label: "Admin" },
  { value: "superuser", label: "Superuser" },
];
const adminRoles = [
  { value: "user", label: "User" },
  { value: "admin", label: "Admin" },
];

const UserForm = ({ data, onClose }) => {
  const [form] = Form.useForm();
  const createUser = useCreateUserMutation();
  const editUser = useEditUserMutation();
  const { notification } = App.useApp();

  const handleSubmit = useCallback(
    (values) => {
      try {
        if (data === null) {
          createUser.mutate({
            name: values.name,
            email: values.email,
            password: values.password,
            role: values.role,
          });
        } else {
          editUser.mutate({
            name: data.name,
            email: values.email,
            password: values.password,
            role: values.role,
          });
        }
        onClose();
      } catch (error) {
        notification.error({
          message: "Error",
          description: error,
        });
      }
    },
    [data, createUser, editUser, notification, onClose]
  );

  useEffect(() => {
    if (data) {
      form.setFieldsValue({
        [FORM_FIELDS.NAME]: data.name,
        [FORM_FIELDS.EMAIL]: data.email,
        [FORM_FIELDS.ROLE]: data.role,
      });
    } else {
      form.resetFields();
    }
  }, [data, form]);

  return (
    <Modal
      title={data !== null ? "Edit user" : "Add new user"}
      maskClosable={false}
      open
      destroyOnClose
      onCancel={onClose}
      style={{ top: 20 }}
      okButtonProps={{ autoFocus: true, htmlType: "submit" }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            console.log("Received values of form: ", values);
            handleSubmit(values);
            form.resetFields();
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form form={form} layout="vertical" name="nimbl_user_form">
        <Form.Item
          name={FORM_FIELDS.NAME}
          label="Username"
          rules={VALIDATION_RULES[FORM_FIELDS.NAME]}
        >
          <Input disabled={data !== null} />
        </Form.Item>
        <Form.Item
          name={FORM_FIELDS.EMAIL}
          label="Email"
          rules={VALIDATION_RULES[FORM_FIELDS.EMAIL]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          name={FORM_FIELDS.PASSWORD}
          label="Password"
          rules={VALIDATION_RULES[FORM_FIELDS.PASSWORD]}
        >
          <Input.Password />
        </Form.Item>
        {/* add select of role has admin, superuser, user */}
        <Form.Item
          name={FORM_FIELDS.ROLE}
          label="Role"
          rules={VALIDATION_RULES[FORM_FIELDS.ROLE]}
        >
          <Select
            options={data && data?.role == "admin" ? adminRoles : roles}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default React.memo(UserForm);
