import React, { useMemo, memo } from "react";
import { Card, Col, Row, Statistic, Spin, theme } from "antd";
import { DesktopOutlined, WarningOutlined } from "@ant-design/icons";
import ReactApexChart from "react-apexcharts";
import { useTheme } from "antd-style";

/**
 * Component to display device statistics and charts
 * @param {Object} props Component props
 * @param {Array} props.data Device data for statistics
 * @param {boolean} props.loading Loading state
 * @returns {JSX.Element} DeviceStatistics component
 */
const DeviceStatistics = ({ data = [], loading = false }) => {
  const { appearance } = useTheme();
  const { token } = theme.useToken();
  const stats = useMemo(() => {
    const total = data.length;
    const warning = data.filter(
      (device) => device.device_errors?.length > 0
    ).length;

    return { total, warning };
  }, [data]);

  const chartOptions = useMemo(() => {
    // Group devices by model name
    const deviceModels = data.reduce((acc, device) => {
      const modelName = device.modelname || "Unknown";
      acc[modelName] = (acc[modelName] || 0) + 1;
      return acc;
    }, {});

    return {
      series: Object.values(deviceModels),
      options: {
        chart: {
          type: "pie",
          height: 350,
          background: "transparent",
        },
        theme: {
          mode: appearance === "dark" ? "dark" : "light",
        },
        colors: [
          token.colorPrimary,
          token.colorSuccess,
          token.colorWarning,
          token.colorError,
          token.colorInfo,
          // Generate more colors if needed
          token.colorPrimaryActive,
          token.colorSuccessActive,
          token.colorWarningActive,
          token.colorErrorActive,
          token.colorInfoActive,
        ],
        labels: Object.keys(deviceModels),
        responsive: [
          {
            breakpoint: 480,
            options: {
              chart: {
                width: 200,
              },
              legend: {
                position: "bottom",
              },
            },
          },
        ],
        title: {
          text: "Device Models",
          align: "left",
        },
      },
    };
  }, [data, appearance, token]);

  return (
    <Spin spinning={loading}>
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12}>
          <Card variant="borderless">
            <Statistic
              title="Total Devices"
              value={stats.total}
              prefix={<DesktopOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12}>
          <Card variant="borderless">
            <Statistic
              title="Warnings"
              value={stats.warning}
              valueStyle={{ color: "#faad14" }}
              prefix={<WarningOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24}>
          <Card variant="borderless">
            {data.length > 0 ? (
              <ReactApexChart
                options={chartOptions.options}
                series={chartOptions.series}
                type="pie"
                height={350}
              />
            ) : (
              <div
                style={{
                  height: 350,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                No device data available
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </Spin>
  );
};

export default memo(DeviceStatistics);
