/**
 * @typedef {Object} PortInfoConfig
 * @property {Array<{label: string, id: string}>} INFO_ITEMS - Basic information items to display
 * @property {Object} CHART - Chart related configurations
 */

export const PORT_INFO_CONFIG = {
  INFO_ITEMS: [
    { label: "Model Name", id: "modelname" },
    { label: "IP Address", id: "ipaddress" },
    { label: "MAC Address", id: "mac" },
    { label: "Kernel", id: "kernel" },
    { label: "Power", id: "power" },
  ],
  CHART: {
    INITIAL_STATE: {
      DATA_POINTS: 9,
      createInitialData: () => ({
        labels: Array(9).fill(""),
        data: Array(9).fill(null),
      }),
    },
    CONFIG: {
      series: [
        { name: "in traffic data", data: [] },
        { name: "out traffic data", data: [] },
      ],
      options: {
        chart: {
          height: 400,
          type: "line",
          toolbar: { show: false },
          zoom: { enabled: false },
        },
        colors: ["#1677ff", "#ff4d4f"],
        dataLabels: { enabled: false },
        stroke: { curve: "smooth" },
        xaxis: {
          type: "text",
          title: { text: "Time" },
        },
        yaxis: {
          title: { text: "Byte" },
        },
        legend: {
          show: true,
          position: "top",
        },
      },
    },
  },
};
