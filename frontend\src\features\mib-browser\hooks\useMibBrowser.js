import { useState, useCallback } from "react";
import { App } from "antd";
import { useSendCommand } from "../../../services/mutations";
import { useGetDevices, useGetCommandResult } from "../../../services/queries";
import { useuseMibBrowserStore } from "../../../store/mib-browser";
import { handleCommandExecution } from "../../../utils/command-execution";

/**
 * Custom hook for MIB Browser functionality
 * @returns {Object} MIB Browser state and functions
 */
export const useMibBrowser = () => {
  const { notification } = App.useApp();
  const sendCommand = useSendCommand();
  const [cmdResult, setCmdResult] = useState(null);

  // Get store state and actions
  const {
    ip_address,
    oid,
    operation,
    value,
    valueType,
    cmdResponse,
    updateSnmpParams,
  } = useuseMibBrowserStore((state) => state);

  // Get device data for client lookup
  const { data: deviceData = [] } = useGetDevices("device");

  // Get command result data
  const { data: resultData } = useGetCommandResult(
    cmdResult?.command ? encodeURIComponent(cmdResult.command) : null
  );

  /**
   * Handle form field changes
   * @param {Object} fieldValues - Object containing field name and value
   */
  const handleFieldChange = useCallback(
    (fieldValues) => {
      updateSnmpParams(fieldValues);
    },
    [updateSnmpParams]
  );

  /**
   * Find the network service client for a given IP address
   * @param {string} ipAddress - IP address to look up
   * @returns {string|null} Client name or null if not found
   */
  const findClientByIp = useCallback(
    (ipAddress) => {
      const device = deviceData.find((item) => item.ipaddress === ipAddress);
      return device?.scannedby || null;
    },
    [deviceData]
  );

  /**
   * Execute SNMP command
   * @returns {Promise<void>}
   */
  const executeCommand = useCallback(async () => {
    setCmdResult(null);

    try {
      const client = findClientByIp(ip_address);

      if (!client) {
        notification.error({
          message: "Error",
          description: "Network service not found for this IP address",
        });
        return;
      }

      const commandStr =
        operation === "set"
          ? `snmp set ${ip_address} ${oid} ${value} ${valueType}`
          : `snmp ${operation} ${ip_address} ${oid}`;
      const command = [{ command: commandStr, client }];

      const response = await handleCommandExecution(
        command,
        sendCommand,
        notification
      );

      if (response.success) {
        updateSnmpParams({ cmdResponse: response.data });
      }
    } catch (error) {
      console.error("Error executing SNMP command:", error);
      notification.error({
        message: "Error",
        description: "Failed to execute SNMP command",
      });
    }
  }, [
    ip_address,
    oid,
    operation,
    findClientByIp,
    sendCommand,
    notification,
    updateSnmpParams,
  ]);

  /**
   * Handle Go button click
   */
  const handleGoClick = useCallback(() => {
    if (operation === "set" && (!value || !valueType)) {
      notification.warning({
        message: "Missing Information",
        description: "Value and Value Type are required for SET operation",
      });
      return;
    }

    executeCommand();
  }, [operation, value, valueType, executeCommand, notification]);

  return {
    // State
    formValues: {
      ip_address,
      oid,
      operation,
      value,
      valueType,
    },
    cmdResponse,
    resultData,

    // Actions
    handleFieldChange,
    handleGoClick,
    setCmdResult,
  };
};

/**
 * Available SNMP operations
 */
export const SNMP_OPERATIONS = [
  { value: "get", label: "Get" },
  { value: "walk", label: "Walk" },
  { value: "bulk", label: "Bulk" },
  { value: "set", label: "Set" },
];

/**
 * Available SNMP value types
 */
export const SNMP_VALUE_TYPES = [
  { value: "OctetString", label: "OctetString" },
  { value: "Integer", label: "Integer" },
  { value: "OID", label: "OID" },
  { value: "IpAddress", label: "IpAddress" },
  { value: "Counter", label: "Counter" },
  { value: "Gauge", label: "Gauge" },
  { value: "TimeTicks", label: "TimeTicks" },
  { value: "Opaque", label: "Opaque" },
];
