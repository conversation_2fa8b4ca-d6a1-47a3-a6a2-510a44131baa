// This file contains action-chain prompt related functions
package actionchain

import (
	"context"
	"encoding/json"
	"mnms/anomaly_detection/chain/prompt"
	"mnms/llm"
)

const taskDescription = `As a software engineer, you are responsible for creating a JSON object that describes a network configuration task based on the provided inputs. The output must be strictly in raw JSON format, adhering to the defined schema without any Markdown or other formatting symbols.

JSON Object Schema Instructions:
- Ensure the response is in pure JSON text.
- Include dynamic content by directly replacing placeholders with the provided inputs.
- Avoid adding Markdown formatting or non-JSON syntax.


Detailed Requirements:
Task Object: Contains a description of the task and an array of action objects.
Actions Array: Each entry represents a step in the task.
Validations Array: Each entry represents a validation for a specific device in the task.

Schema Details:
- Task Object:
  - task: Description of the task.
	- tasktype: Type of the task, e.g., "network configuration".
  - actions: An array of action objects.
  - validation: An array of validation objects detailing the validation process for each device.
- Action Object:
  - type: Identifies whether the action is a "command" or an "API" call.
  - action: Details the action to be executed
- Validation Object:
  - description:  Description of the validation step.
  - action: Contains the API endpoint and expected outcomes for validation.
    - endpoint:  The API endpoint to call.
    - emthod: The HTTP method to use, e.g., "GET".
    - query-params or body
  - expects: A JSON object containing all fields to be validated.

Error Object:
- error: a error reason
- task: which task is failed

Generate a JSON object following the structure provided without any non-JSON text or formatting. If any thing make confuse return a error object.`

const clarifyIntentInstruction = `Identify and return only the main action or intent expressed in the following text. Use a short, simple phrase in natural language without including any specific details or arguments.`

// ClarifyInput returns a prompt to clarify the intent of the input.
func ClarifyInput(m llm.Completer, input string) (string, error) {
	promp := clarifyIntentInstruction + "\n" + input
	return m.Complete(context.Background(), []llm.Message{{
		Role:    "user",
		Content: promp,
	}})
}

// GetDeviceCurrentSettings
func GetDeviceCurrentSettings(m llm.Completer, input string) ([]map[string]any, error) {
	systemPtompt := `
You are an expert engineer. Your task is to analyze input text and determine the type of task it represents. If the text involves device setup or configuration, follow these steps:

1. First, identify any devices mentioned in the input and retrieve their current information.
2. Return a JSON array specifying the required information for each device.
3. The JSON array should follow this structure:
[
    {
        "task": "get-device-info",
        "mac": "<MAC_ADDRESS_OF_DEVICE>"
    }
]

Ensure that return raw JSON data without any additional formatting or text.`
	prompt := prompt.Prompt{
		SystemPrompt: systemPtompt,
		UserPrompt:   input,
	}
	ret, err := m.Complete(context.Background(), prompt.Messages())
	if err != nil {
		return nil, err
	}
	// marshal ret to JSON
	var result []map[string]any
	err = json.Unmarshal([]byte(ret), &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// NewActionPrompt returns a new action prompt with default Task system instructions.
func NewActionPrompt(input string) prompt.Prompt {
	return prompt.Prompt{
		SystemPrompt: taskDescription,
		UserPrompt:   input,
	}
}

// AddUserInput adds a user input to the prompt.
func AddUserInput(p *prompt.Prompt, userInput string) *prompt.Prompt {
	p.UserPrompt = userInput
	return p
}

// AddTaskDescription adds a task description to the prompt.
// The task description usually get from the taskStore which should be contains
// tasks descriptions and it's vector.
// query can be a nature language that ask LLM do some task. For example:
// I would like to setup device which has MAC address 00-12-34-56-78-9a whith following settings:
// old ip: ************
// new ip: ************
// mask: *************
// gateway: *************
// hostname: test-switch
// dhcp: 1
// func AddTaskDescription(p *prompt.Prompt, query string) *prompt.Prompt {
// 	p.UserPrompt =
// 	return p
// }
