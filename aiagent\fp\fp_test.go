package fp

import (
	"context"
	"strings"
	"testing"
)

type toLower struct{}

func (t toLower) Invoke(ctx context.Context, input string) (string, error) {
	return strings.ToLower(input), nil
}

type encloseBrackets struct{}

func (e encloseBrackets) Invoke(ctx context.Context, input string) (string, error) {
	return "(" + input + ")", nil
}

type identity struct{}

func (i identity) Invoke(ctx context.Context, input string) (string, error) {
	return input, nil
}

// TestConcatenate is a test function
func TestConcatenate(t *testing.T) {
	// TestConcatenate is a test function
	threeBrackets := Concatenate(identity{}, identity{}, identity{})
	result, err := threeBrackets(context.Background(), "hello")
	if err != nil {
		t.Error(err)
	}

	if result != "hellohellohello" {
		t.<PERSON>("expected hellohellohello, got %s", result)
	}
}
