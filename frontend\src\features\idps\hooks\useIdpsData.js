import { useState, useEffect, useCallback } from "react";
import { App } from "antd";
import {
  useIdpsReport,
  useProcessedIdpsData,
  useImportRules,
  useDeleteRule,
  useSearchFile,
  useDeleteRecords,
  useFilterEvents,
} from "../../../services/idps";

/**
 * Custom hook for managing IDPS data
 * @returns {Object} IDPS data and methods
 */
export const useIdpsData = () => {
  const { notification } = App.useApp();
  const [selectedService, setSelectedService] = useState(null);

  // Fetch all IDPS services data
  const { data: idpsData, isLoading, refetch, error } = useIdpsReport();

  // Extract services from data
  const services = idpsData ? Object.keys(idpsData) : [];

  // Auto-select first service if none selected
  useEffect(() => {
    if (services.length > 0 && !selectedService) {
      setSelectedService(services[0]);
    }
  }, [services, selectedService]);

  // Process data for selected service
  const {
    selectedServiceData,
    rulesData,
    eventsData,
    packetData,
    recordListData,
    statistics,
  } = useProcessedIdpsData(idpsData, selectedService);

  // Handle service selection
  const selectService = useCallback((serviceName) => {
    setSelectedService(serviceName);
  }, []);

  // Setup mutations with auto-refresh
  const handleRefreshAfterSuccess = useCallback(() => {
    setTimeout(() => {
      refetch();
    }, 2000); // Wait 2 seconds for command to process
  }, [refetch]);

  // Import rules mutation
  const importRulesMutation = useImportRules(handleRefreshAfterSuccess);

  // Delete rule mutation
  const deleteRuleMutation = useDeleteRule(handleRefreshAfterSuccess);

  // Handle rules import
  const importRules = useCallback(
    (url, serviceName) => {
      if (!url || !serviceName) {
        notification.error({
          message: "Invalid Input",
          description: "Please provide both URL and service name.",
        });
        return;
      }
      importRulesMutation.mutate({ url, serviceName });
    },
    [importRulesMutation, notification]
  );

  // File search mutation
  const fileSearchMutation = useSearchFile();

  // Handle rule delete
  const deleteRule = useCallback(
    (ruleName, serviceName) => {
      if (!ruleName || !serviceName) {
        notification.error({
          message: "Invalid Input",
          description: "Please provide both rule name and service name.",
        });
        return;
      }
      deleteRuleMutation.mutate({ ruleName, serviceName });
    },
    [deleteRuleMutation, notification]
  );

  // Handle file search
  const searchFile = useCallback(
    (filename, selectedRecordDate, serviceName) => {
      if (!filename || !selectedRecordDate || !serviceName) {
        notification.error({
          message: "Invalid Input",
          description: "Please provide filename, date, and service name.",
        });
        return;
      }
      fileSearchMutation.mutate({ filename, selectedRecordDate, serviceName });
    },
    [fileSearchMutation, notification]
  );

  // Delete records mutation
  const deleteRecordsMutation = useDeleteRecords(handleRefreshAfterSuccess);

  // Event filter mutation
  const eventFilterMutation = useFilterEvents();

  // Handle delete records
  const deleteRecords = useCallback(
    (deleteType, deleteSelectedDate, deleteFileName, serviceName) => {
      if (!deleteType || !serviceName) {
        notification.error({
          message: "Invalid Input",
          description: "Please provide delete type and service name.",
        });
        return;
      }
      deleteRecordsMutation.mutate({
        deleteType,
        deleteSelectedDate,
        deleteFileName,
        serviceName,
      });
    },
    [deleteRecordsMutation, notification]
  );

  // Handle event filter
  const filterEvents = useCallback(
    (
      eventFilterFileName,
      eventFilterStartDate,
      eventFilterEndDate,
      serviceName
    ) => {
      if (
        !eventFilterFileName ||
        !eventFilterStartDate ||
        !eventFilterEndDate ||
        !serviceName
      ) {
        notification.error({
          message: "Invalid Input",
          description: "Please provide filename, date range, and service name.",
        });
        return;
      }
      eventFilterMutation.mutate({
        eventFilterFileName,
        eventFilterStartDate,
        eventFilterEndDate,
        serviceName,
      });
    },
    [eventFilterMutation, notification]
  );

  // Handle data refresh
  const refreshData = useCallback(() => {
    refetch();
    notification.success({
      message: "Data Refreshed",
      description: "IDPS data has been refreshed successfully.",
    });
  }, [refetch, notification]);

  // Handle errors
  useEffect(() => {
    if (error) {
      notification.error({
        message: "Error Loading IDPS Data",
        description:
          error.message || "Failed to load IDPS data. Please try again.",
      });
    }
  }, [error, notification]);

  return {
    services,
    selectedService,
    rulesData,
    eventsData,
    packetData,
    recordListData,
    statistics,
    isLoading,
    selectService,
    refreshData,
    selectedServiceData,
    importRules,
    deleteRule,
    searchFile,
    deleteRecords,
    filterEvents,
    isImporting: importRulesMutation.isPending,
    isDeleting: deleteRuleMutation.isPending,
    isSearching: fileSearchMutation.isPending,
    isDeletingRecords: deleteRecordsMutation.isPending,
    isFilteringEvents: eventFilterMutation.isPending,
  };
};
