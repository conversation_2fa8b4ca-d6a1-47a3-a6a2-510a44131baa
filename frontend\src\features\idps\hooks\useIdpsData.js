import { useState, useEffect, useCallback } from "react";
import { App } from "antd";
import { useQuery } from "@tanstack/react-query";
import { axiosInstance } from "../../../services/api";

/**
 * Custom hook for managing IDPS data
 * @returns {Object} IDPS data and methods
 */
export const useIdpsData = () => {
  const { notification } = App.useApp();
  const [selectedService, setSelectedService] = useState(null);

  // Fetch all IDPS services data
  const {
    data: idpsData,
    isLoading,
    refetch,
    error,
  } = useQuery({
    queryKey: ["idps", "report"],
    queryFn: async () => {
      const response = await axiosInstance.get("/api/v1/idps/report");
      return response.data;
    },
    refetchInterval: 30000, // Refresh every 30 seconds
    retry: 3,
  });

  // Extract services from data
  const services = idpsData ? Object.keys(idpsData) : [];

  // Auto-select first service if none selected
  useEffect(() => {
    if (services.length > 0 && !selectedService) {
      setSelectedService(services[0]);
    }
  }, [services, selectedService]);

  // Get data for selected service
  const selectedServiceData =
    selectedService && idpsData ? idpsData[selectedService] : null;

  // Process rules data
  const rulesData = selectedServiceData?.rules || [];

  // Process events data
  const eventsData = selectedServiceData?.event || [];

  // Process packet data for chart
  const packetData = selectedServiceData?.rulepackets || [];

  // Calculate statistics
  const statistics = {
    totalEvents: eventsData.length,
    alertEvents: eventsData.filter((event) => event.type === "alert").length,
    dropEvents: eventsData.filter((event) => event.type === "drop").length,
  };

  // Handle service selection
  const selectService = useCallback((serviceName) => {
    setSelectedService(serviceName);
  }, []);

  // Handle data refresh
  const refreshData = useCallback(() => {
    refetch();
    notification.success({
      message: "Data Refreshed",
      description: "IDPS data has been refreshed successfully.",
    });
  }, [refetch, notification]);

  // Handle errors
  useEffect(() => {
    if (error) {
      notification.error({
        message: "Error Loading IDPS Data",
        description:
          error.message || "Failed to load IDPS data. Please try again.",
      });
    }
  }, [error, notification]);

  return {
    services,
    selectedService,
    rulesData,
    eventsData,
    packetData,
    statistics,
    isLoading,
    selectService,
    refreshData,
    selectedServiceData,
  };
};
