import { useState, useEffect, useCallback } from "react";
import { App } from "antd";
import { useQuery, useMutation } from "@tanstack/react-query";
import { axiosInstance } from "../../../services/api";

/**
 * Custom hook for managing IDPS data
 * @returns {Object} IDPS data and methods
 */
export const useIdpsData = () => {
  const { notification } = App.useApp();
  const [selectedService, setSelectedService] = useState(null);

  // Fetch all IDPS services data
  const {
    data: idpsData,
    isLoading,
    refetch,
    error,
  } = useQuery({
    queryKey: ["idps", "report"],
    queryFn: async () => {
      const response = await axiosInstance.get("/api/v1/idps/report");
      return response.data;
    },
    refetchInterval: 30000, // Refresh every 30 seconds
    retry: 3,
  });

  // Extract services from data
  const services = idpsData ? Object.keys(idpsData) : [];

  // Auto-select first service if none selected
  useEffect(() => {
    if (services.length > 0 && !selectedService) {
      setSelectedService(services[0]);
    }
  }, [services, selectedService]);

  // Get data for selected service
  const selectedServiceData =
    selectedService && idpsData ? idpsData[selectedService] : null;

  // Process rules data
  const rulesData = selectedServiceData?.rules || [];

  // Process events data
  const eventsData = selectedServiceData?.event || [];

  // Process packet data for chart
  const packetData = selectedServiceData?.rulepackets || [];

  // Process record list data
  const recordListData = selectedServiceData?.recordlist || [];

  // Calculate statistics
  const statistics = {
    totalEvents: eventsData.length,
    alertEvents: eventsData.filter((event) => event.type === "alert").length,
    dropEvents: eventsData.filter((event) => event.type === "drop").length,
  };

  // Handle service selection
  const selectService = useCallback((serviceName) => {
    setSelectedService(serviceName);
  }, []);

  // Import rules mutation
  const importRulesMutation = useMutation({
    mutationFn: async ({ url, serviceName }) => {
      const command = `idps rules import ${url}`;
      const commandData = [
        {
          command,
          client: serviceName,
        },
      ];
      const response = await axiosInstance.post(
        "/api/v1/commands",
        commandData
      );
      return response.data;
    },
    onSuccess: () => {
      notification.success({
        message: "Import Rules",
        description: "Rules import command has been sent successfully.",
      });
      // Refresh data after import
      setTimeout(() => {
        refetch();
      }, 2000); // Wait 2 seconds for command to process
    },
    onError: (error) => {
      notification.error({
        message: "Import Rules Failed",
        description:
          error.message || "Failed to import rules. Please try again.",
      });
    },
  });

  // Delete rule mutation
  const deleteRuleMutation = useMutation({
    mutationFn: async ({ ruleName, serviceName }) => {
      const command = `idps rules delete ${ruleName}`;
      const commandData = [
        {
          command,
          client: serviceName,
        },
      ];
      const response = await axiosInstance.post(
        "/api/v1/commands",
        commandData
      );
      return response.data;
    },
    onSuccess: () => {
      notification.success({
        message: "Delete Rule",
        description: "Rule delete command has been sent successfully.",
      });
      // Refresh data after delete
      setTimeout(() => {
        refetch();
      }, 2000); // Wait 2 seconds for command to process
    },
    onError: (error) => {
      notification.error({
        message: "Delete Rule Failed",
        description:
          error.message || "Failed to delete rule. Please try again.",
      });
    },
  });

  // Handle rules import
  const importRules = useCallback(
    (url, serviceName) => {
      if (!url || !serviceName) {
        notification.error({
          message: "Invalid Input",
          description: "Please provide both URL and service name.",
        });
        return;
      }
      importRulesMutation.mutate({ url, serviceName });
    },
    [importRulesMutation, notification]
  );

  // File search mutation
  const fileSearchMutation = useMutation({
    mutationFn: async ({ filename, selectedRecordDate, serviceName }) => {
      const command = `idps records search -f ${filename} -st ${selectedRecordDate}-00:00 -et ${selectedRecordDate}-23:59`;
      const commandData = [
        {
          command,
          client: serviceName,
        },
      ];
      const response = await axiosInstance.post(
        "/api/v1/commands",
        commandData
      );
      return response.data;
    },
    onSuccess: () => {
      notification.success({
        message: "File Search",
        description: "File search command has been sent successfully.",
      });
    },
    onError: (error) => {
      notification.error({
        message: "File Search Failed",
        description:
          error.message || "Failed to search file. Please try again.",
      });
    },
  });

  // Handle rule delete
  const deleteRule = useCallback(
    (ruleName, serviceName) => {
      if (!ruleName || !serviceName) {
        notification.error({
          message: "Invalid Input",
          description: "Please provide both rule name and service name.",
        });
        return;
      }
      deleteRuleMutation.mutate({ ruleName, serviceName });
    },
    [deleteRuleMutation, notification]
  );

  // Handle file search
  const searchFile = useCallback(
    (filename, selectedRecordDate, serviceName) => {
      console.log("Search file:", filename, selectedRecordDate, serviceName);
      if (!filename || !selectedRecordDate || !serviceName) {
        notification.error({
          message: "Invalid Input",
          description: "Please provide filename, date, and service name.",
        });
        return;
      }
      fileSearchMutation.mutate({ filename, selectedRecordDate, serviceName });
    },
    [fileSearchMutation, notification]
  );

  // Handle data refresh
  const refreshData = useCallback(() => {
    refetch();
    notification.success({
      message: "Data Refreshed",
      description: "IDPS data has been refreshed successfully.",
    });
  }, [refetch, notification]);

  // Handle errors
  useEffect(() => {
    if (error) {
      notification.error({
        message: "Error Loading IDPS Data",
        description:
          error.message || "Failed to load IDPS data. Please try again.",
      });
    }
  }, [error, notification]);

  return {
    services,
    selectedService,
    rulesData,
    eventsData,
    packetData,
    recordListData,
    statistics,
    isLoading,
    selectService,
    refreshData,
    selectedServiceData,
    importRules,
    deleteRule,
    searchFile,
    isImporting: importRulesMutation.isPending,
    isDeleting: deleteRuleMutation.isPending,
    isSearching: fileSearchMutation.isPending,
  };
};
