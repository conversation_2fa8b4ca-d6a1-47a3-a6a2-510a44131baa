package service

import (
	"bytes"
	"encoding/json"
	"fmt"
	"strings"

	"mnms/clientutils"

	anmReport "mnms/anomaly_detection/report"
	statistic "mnms/anomaly_detection/statistic"
	"mnms/anomaly_detection/utils"

	"github.com/qeof/q"
)

func maskAPIKey(apiKey string) string {
	// Check if the API key length is less than or equal to 8
	if len(apiKey) <= 8 {
		return "Key too short"
	}

	// Mask the API key except the first and last 4 characters
	prefix := apiKey[:4]
	suffix := apiKey[len(apiKey)-4:]
	maskedLength := len(apiKey) - 8
	maskedPortion := strings.Repeat("*", maskedLength)

	return prefix + maskedPortion + suffix
}

// UploadAnomalyStatistic upload statistic to server
func UploadAnomalyStatistic() error {

	settings := statistic.LocalStatistic.Settings

	if settings.Root == "" {
		q.Q("root url is empty")
		return fmt.Errorf("root url is empty")
	}

	type request struct {
		ClientName string              `json:"clientname"`
		Statistic  statistic.Statistic `json:"statistic"`
	}

	statistic.LocalStatistic.Settings.OpenAISettings.IsDemoAPIKey = utils.IsDemoAPIKey()
	updatedata := statistic.LocalStatistic
	updatedata.Settings.OpenAISettings.APIKey = maskAPIKey(updatedata.Settings.OpenAISettings.APIKey)
	req := request{
		ClientName: statistic.LocalStatistic.ClientName,
		Statistic:  updatedata,
	}
	jsonBytes, err := json.Marshal(req)
	if err != nil {
		q.Q(err)
		return err
	}

	// q.Q("upload statistics jsonBytes: ", string(jsonBytes))

	resp, err := clientutils.Post(settings.Root+"/api/v1/anomaly/statistics", settings.Token, bytes.NewBuffer(jsonBytes))
	if err != nil {
		q.Q("Post anomaly statistic fail, ", err, settings.Root)
		return err
	}
	if resp != nil {
		// save close, in resp != nil block
		defer resp.Body.Close()
		if resp.StatusCode != 200 {
			q.Q("Post anomaly statistic fail, ", resp.StatusCode)
			return fmt.Errorf("Post anomaly statistic fail, %d", resp.StatusCode)
		}
	}
	// q.Q("upload statistics done")
	return nil
}

// UploadAnomalyReport upload report to server
func UploadAnomalyReport(msg string, report *anmReport.Report) error {
	settings := statistic.LocalStatistic.Settings
	report.Message = msg

	if settings.Root == "" {
		q.Q("root url is empty")
		return fmt.Errorf("root url is empty")
	}

	type request struct {
		Action string                   `json:"action"`
		Report anmReport.ExportedReport `json:"report"`
	}

	var req request
	req.Action = "add"
	exportedReport, err := report.Export()
	if err != nil {
		q.Q("Export report fail: ", err)
		return err
	}
	req.Report = *exportedReport

	jsonBytes, err := json.Marshal(req)
	if err != nil {
		q.Q(err)
		return err
	}

	resp, err := clientutils.Post(settings.Root+"/api/v1/anomaly/reports", settings.Token, bytes.NewBuffer(jsonBytes))
	if err != nil {
		q.Q("Post fail: ", err)
		return err
	}

	if resp != nil {
		// save close, in resp != nil block
		defer resp.Body.Close()
		if resp.StatusCode != 200 {
			q.Q("Post anomaly report response has error, ", resp.StatusCode)
			return fmt.Errorf("Post anomaly report fail, %d", resp.StatusCode)
		}
	}
	// q.Q("upload statistics done")
	return nil
}

// UploadRealtimeReport upload realtime report to server
func UploadRealtimeReport() error {
	realtimeReport := anmReport.GetRealtimeReport()
	settings := statistic.LocalStatistic.Settings
	if settings.Root == "" {
		q.Q("root url is empty")
		return fmt.Errorf("root url is empty")
	}

	type request struct {
		Report     anmReport.Report `json:"report"`
		ClientName string           `json:"client"`
	}
	req := request{
		Report:     *realtimeReport,
		ClientName: statistic.LocalStatistic.ClientName,
	}
	jsonBytes, err := json.Marshal(req)
	if err != nil {
		q.Q(err)
		return err
	}
	resp, err := clientutils.Post(settings.Root+"/api/v1/anomaly/realtime/report/sync", settings.Token, bytes.NewBuffer(jsonBytes))
	if err != nil {
		q.Q("Post anomaly realtime report fail: ", err)
		return err
	}

	if resp != nil {
		defer resp.Body.Close()
		if resp.StatusCode != 200 {
			q.Q("Post anomaly realtime report fail, ", resp.StatusCode)
			return fmt.Errorf("Post anomaly realtime report fail, %d", resp.StatusCode)
		}
	}
	return nil
}

// UploadRealtimeMessages upload realtime messages to server
func UploadRealtimeMessages(msgs []*anmReport.ReportMessage) error {
	q.Q("upload realtime messages to server")
	settings := statistic.LocalStatistic.Settings
	if settings.Root == "" {
		q.Q("root url is empty")
		return fmt.Errorf("root url is empty")
	}

	type request struct {
		Messages   []*anmReport.ReportMessage `json:"messages"`
		ClientName string                     `json:"client"`
	}
	req := request{
		Messages:   msgs,
		ClientName: statistic.LocalStatistic.ClientName,
	}
	jsonBytes, err := json.Marshal(req)
	if err != nil {
		q.Q(err)
		return err
	}

	resp, err := clientutils.Post(settings.Root+"/api/v1/anomaly/realtime/report/messages", settings.Token, bytes.NewBuffer(jsonBytes))
	if err != nil {
		q.Q("Post anomaly realtime report messages fail: ", err)
		return err
	}

	if resp != nil {
		defer resp.Body.Close()
		if resp.StatusCode != 200 {
			q.Q("Post anomaly realtime report messages fail, ", resp.StatusCode)
			return fmt.Errorf("Post anomaly realtime report messages fail, %d", resp.StatusCode)
		}
	}
	return nil
}
