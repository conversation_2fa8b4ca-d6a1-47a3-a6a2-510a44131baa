import { useCallback, useMemo, useState } from "react";
import { useLogTableColumn } from "../../../components/table-column/logs-table";
import { useGetSyslogs } from "../../../services/queries";
import { v4 as uuidv4 } from "uuid";

export const useLogsTable = (initialParams = {}, customColumns = null) => {
  // Default params with ability to override
  const [params, setParams] = useState({
    number: 100,
    start: "",
    end: "",
    ...initialParams,
  });
  const [inputSearch, setInputSearch] = useState("");

  // Allow custom columns or use default log columns
  const defaultColumns = useLogTableColumn();
  const columns = customColumns || defaultColumns;

  const { data = [], refetch, isFetching } = useGetSyslogs(params);

  const filteredData = useMemo(() => {
    const searchTerm = inputSearch.toLowerCase();
    return data
      .filter((rows) =>
        searchTerm === ""
          ? true
          : columns.some(
              (column) =>
                column.dataIndex &&
                String(rows[column.dataIndex] || "")
                  .toLowerCase()
                  .includes(searchTerm)
            )
      )
      .map((item) => ({ ...item, uuid: uuidv4() }));
  }, [data, inputSearch, columns]);

  const handleSetParams = useCallback((newParams) => {
    setParams((prev) => ({ ...prev, ...newParams }));
  }, []);

  const resetParams = useCallback(() => {
    setParams({
      number: 100,
      start: "",
      end: "",
      ...initialParams,
    });
  }, [initialParams]);

  return {
    columns,
    filteredData,
    params,
    setParams,
    setInputSearch,
    refetch,
    isFetching,
    handleSetParams,
    resetParams,
    rawData: data,
  };
};
