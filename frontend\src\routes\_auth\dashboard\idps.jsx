import { createFileRoute } from "@tanstack/react-router";
import { Space, Row, Col } from "antd";
import {
  IdpsServiceSelector,
  IdpsRulesTable,
  IdpsPacketChart,
  IdpsEventsTable,
  IdpsStatistics,
  IdpsRecordList,
  useIdpsData,
} from "../../../features/idps";

export const Route = createFileRoute("/_auth/dashboard/idps")({
  component: IdpsComponent,
});

/**
 * IDPS Dashboard Component
 * Displays intrusion detection and prevention system data
 * @returns {JSX.Element} IDPS Dashboard component
 */
function IdpsComponent() {
  const {
    services,
    selectedService,
    rulesData,
    eventsData,
    packetData,
    recordListData,
    statistics,
    isLoading,
    selectService,
    refreshData,
    selectedServiceData,
    importRules,
    deleteRule,
    searchFile,
    deleteRecords,
    filterEvents,
    isImporting,
    isDeleting,
  } = useIdpsData();

  return (
    <div style={{ padding: "24px" }}>
      <Space direction="vertical" size="large" style={{ width: "100%" }}>
        {/* Service Selector */}
        <IdpsServiceSelector
          services={services}
          selectedService={selectedService}
          onSelectService={selectService}
          onRefresh={refreshData}
          onImportRules={importRules}
          selectedServiceData={selectedServiceData}
          loading={isLoading}
          isImporting={isImporting}
        />

        {/* Statistics */}
        <IdpsStatistics data={statistics} loading={isLoading} />

        <Row gutter={[16, 16]}>
          {/* Rules Table */}
          <Col xs={24} lg={14}>
            <IdpsRulesTable
              data={rulesData}
              loading={isLoading}
              onRefresh={refreshData}
              onDeleteRule={deleteRule}
              selectedService={selectedService}
              isDeleting={isDeleting}
            />
          </Col>

          {/* Packet Chart */}
          <Col xs={24} lg={10}>
            <IdpsPacketChart
              data={packetData}
              loading={isLoading}
              selectedService={selectedService}
            />
          </Col>
        </Row>

        {/* Record List */}
        <IdpsRecordList
          data={recordListData}
          loading={isLoading}
          selectedService={selectedService}
          onFileSearch={searchFile}
          onDeleteRecords={deleteRecords}
        />

        {/* Events Table */}
        <IdpsEventsTable
          data={eventsData}
          loading={isLoading}
          onRefresh={refreshData}
          onFilterEvents={filterEvents}
          selectedService={selectedService}
        />
      </Space>
    </div>
  );
}
