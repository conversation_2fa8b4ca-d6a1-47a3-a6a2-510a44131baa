import { createFileRoute } from "@tanstack/react-router";
import { Space, Typography, Row, Col } from "antd";
import {
  IdpsServiceSelector,
  IdpsRulesTable,
  IdpsPacketChart,
  IdpsEventsTable,
  IdpsStatistics,
  useIdpsData,
} from "../../../features/idps";

const { Title } = Typography;

export const Route = createFileRoute("/_auth/dashboard/idps")({
  component: IdpsComponent,
});

/**
 * IDPS Dashboard Component
 * Displays intrusion detection and prevention system data
 * @returns {JSX.Element} IDPS Dashboard component
 */
function IdpsComponent() {
  const {
    services,
    selectedService,
    rulesData,
    eventsData,
    packetData,
    statistics,
    isLoading,
    selectService,
    refreshData,
  } = useIdpsData();

  return (
    <div style={{ padding: "24px" }}>
      <Space direction="vertical" size="large" style={{ width: "100%" }}>
        {/* Service Selector */}
        <IdpsServiceSelector
          services={services}
          selectedService={selectedService}
          onSelectService={selectService}
          loading={isLoading}
        />

        {/* Statistics */}
        <IdpsStatistics data={statistics} loading={isLoading} />

        <Row gutter={[16, 16]}>
          {/* Rules Table */}
          <Col xs={24} lg={14}>
            <IdpsRulesTable
              data={rulesData}
              loading={isLoading}
              onRefresh={refreshData}
              selectedService={selectedService}
            />
          </Col>

          {/* Packet Chart */}
          <Col xs={24} lg={10}>
            <IdpsPacketChart
              data={packetData}
              loading={isLoading}
              selectedService={selectedService}
            />
          </Col>
        </Row>

        {/* Events Table */}
        <IdpsEventsTable
          data={eventsData}
          loading={isLoading}
          onRefresh={refreshData}
          selectedService={selectedService}
        />
      </Space>
    </div>
  );
}
