import { createFileRoute } from "@tanstack/react-router";
import { Space, Typography, App } from "antd";
import {
  IdpsStatistics,
  IdpsControls,
  IdpsChart,
  IdpsTable,
  IdpsServiceStatus,
  ServiceDetails,
  useIdpsData,
} from "../../../features/idps";

const { Title } = Typography;

export const Route = createFileRoute("/_auth/dashboard/idps")({
  component: IdpsComponent,
});

/**
 * IDPS Dashboard Component
 * Displays intrusion detection and prevention system data
 * @returns {JSX.Element} IDPS Dashboard component
 */
function IdpsComponent() {
  const { notification } = App.useApp();

  const {
    eventsData,
    supportedEvents,
    statistics,
    isLoading,
    availableServices,
    serviceStatuses,
    selectedService,
    refreshAll,
    selectService,
    importRulesToService,
    addRuleToService,
    deleteRuleFromService,
    rulesData,
  } = useIdpsData();

  /**
   * Handle service selection
   * @param {string} serviceName - Service name to select
   */
  const handleSelectService = (serviceName) => {
    selectService(serviceName);
    notification.info({
      message: "Service Selected",
      description: `Selected service: ${serviceName}`,
    });
  };

  /**
   * Handle importing rules from URL to a specific service
   * @param {string} serviceName - Service name
   * @param {string} rulesUrl - URL containing rules to import
   */
  const handleImportRules = async (serviceName, rulesUrl) => {
    try {
      await importRulesToService(serviceName, rulesUrl);
    } catch (error) {
      // Error handling is done in the hook
      console.error("Failed to import rules:", error);
    }
  };

  /**
   * Handle adding a specific rule to a service
   * @param {string} serviceName - Service name
   * @param {string} ruleName - Rule name
   * @param {string} ruleContent - Rule content
   */
  const handleAddRule = async (serviceName, ruleName, ruleContent) => {
    try {
      await addRuleToService(serviceName, ruleName, ruleContent);
    } catch (error) {
      // Error handling is done in the hook
      console.error("Failed to add rule:", error);
    }
  };

  /**
   * Handle deleting a rule from a service
   * @param {string} serviceName - Service name
   * @param {string} ruleName - Rule name to delete
   */
  const handleDeleteRule = async (serviceName, ruleName) => {
    try {
      await deleteRuleFromService(serviceName, ruleName);
    } catch (error) {
      // Error handling is done in the hook
      console.error("Failed to delete rule:", error);
    }
  };

  /**
   * Handle closing service details
   */
  const handleCloseServiceDetails = () => {
    selectService(null);
  };

  /**
   * Handle data export
   */
  const handleExport = () => {
    // Export functionality will be handled by the ExportData component in the table
    notification.info({
      message: "Export",
      description: "Use the export button in the events table to download data",
    });
  };

  return (
    <div style={{ padding: "0 16px" }}>
      <Space direction="vertical" size="large" style={{ width: "100%" }}>
        {/* Page Header */}
        <div>
          <Title level={2} style={{ margin: 0 }}>
            Intrusion Detection & Prevention System
            {availableServices.length > 0 && (
              <Typography.Text
                type="secondary"
                style={{ fontSize: 16, fontWeight: 400, marginLeft: 16 }}
              >
                ({availableServices.length} Service
                {availableServices.length !== 1 ? "s" : ""})
              </Typography.Text>
            )}
          </Title>
          <Typography.Text type="secondary">
            Monitor and manage network security events in real-time
            {availableServices.length > 1 && " across multiple services"}
          </Typography.Text>
        </div>

        {/* Service Status Cards */}
        {availableServices.length > 1 && (
          <IdpsServiceStatus
            availableServices={availableServices}
            serviceStatuses={serviceStatuses}
            selectedService={selectedService}
            onSelectService={handleSelectService}
            onImportRules={handleImportRules}
            onAddRule={handleAddRule}
            onDeleteRule={handleDeleteRule}
            rulesData={rulesData}
          />
        )}

        {/* Statistics Cards */}
        <IdpsStatistics
          statistics={statistics}
          loading={isLoading}
          onRefresh={refreshAll}
          availableServices={availableServices}
        />

        {/* Service Details Section */}
        {selectedService && (
          <ServiceDetails
            serviceName={selectedService}
            serviceStatus={serviceStatuses[selectedService]}
            rulesData={rulesData.filter(
              (rule) => rule.serviceId === selectedService
            )}
            eventsData={eventsData.filter(
              (event) => event.serviceId === selectedService
            )}
            onImportRules={handleImportRules}
            onAddRule={handleAddRule}
            onDeleteRule={handleDeleteRule}
            onClose={handleCloseServiceDetails}
          />
        )}

        {/* Controls and Filters */}
        <IdpsControls
          supportedEvents={supportedEvents}
          availableServices={availableServices}
          onExport={handleExport}
          loading={isLoading}
        />

        {/* Charts */}
        <IdpsChart data={eventsData} loading={isLoading} />

        {/* Events Table */}
        <IdpsTable
          data={eventsData}
          loading={isLoading}
          onRefresh={refreshAll}
        />
      </Space>
    </div>
  );
}

export default IdpsComponent;
