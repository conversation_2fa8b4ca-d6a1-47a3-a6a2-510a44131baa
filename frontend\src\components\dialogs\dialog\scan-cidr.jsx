import { App, Form, Input, Modal } from "antd";
import React, { useCallback } from "react";
import { useSendCommand } from "../../../services/mutations";
import { handleCommandExecution } from "../../../utils/command-execution";

const CIDR_PATTERN = /^([0-9]{1,3}\.){3}[0-9]{1,3}\/([0-9]|[1-2][0-9]|3[0-2])$/;

const ScanCidr = ({ data, onClose }) => {
  const { notification } = App.useApp();
  const sendCommand = useSendCommand();
  const [form] = Form.useForm();

  const handleSubmit = useCallback(
    (values) => {
      let commands = [{ command: `scan ${values.cidr} -save` }];
      handleCommandExecution(commands, sendCommand, notification);
      onClose();
    },
    [sendCommand, notification, onClose]
  );

  return (
    <Modal
      title="add device by IP Range (CIDR)"
      maskClosable={false}
      open
      destroyOnClose
      onCancel={onClose}
      okButtonProps={{ autoFocus: true, htmlType: "submit" }}
      style={{ top: 20 }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            handleSubmit(values);
            form.resetFields();
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form
        name="scan_ip_cidr_form"
        form={form}
        autoComplete="off"
        layout="vertical"
        clearOnDestroy
      >
        <Form.Item
          name="cidr"
          label="IP Range (CIDR)"
          rules={[
            {
              required: true,
              message: "Missing IP Range (CIDR)!",
            },
            {
              pattern: CIDR_PATTERN,
              message: "input should be IP Range (CIDR)!",
            },
          ]}
        >
          <Input placeholder="eg. xxx.xxx.xxx.xxx/xx" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default React.memo(ScanCidr);
