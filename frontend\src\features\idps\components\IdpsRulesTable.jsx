import React, { memo, useMemo } from "react";
import { Card, Table, Tag, Space, Button, List, Typography, Tooltip } from "antd";
import { ReloadOutlined, ExpandAltOutlined } from "@ant-design/icons";
import dayjs from "dayjs";

const { Text } = Typography;

/**
 * IDPS Rules Table Component
 * Displays rules with expandable rows showing rule contents
 * @param {Object} props Component props
 * @param {Array} props.data Rules data
 * @param {boolean} props.loading Loading state
 * @param {Function} props.onRefresh Refresh callback
 * @param {string} props.selectedService Selected service name
 * @returns {JSX.Element} Rules table component
 */
const IdpsRulesTable = ({
  data = [],
  loading = false,
  onRefresh,
  selectedService,
}) => {
  const columns = useMemo(
    () => [
      {
        title: "Rule Name",
        dataIndex: "name",
        key: "name",
        width: 200,
        render: (text) => (
          <Text strong style={{ color: "#1890ff" }}>
            {text}
          </Text>
        ),
      },
      {
        title: "Created Time",
        dataIndex: "created_time",
        key: "created_time",
        width: 180,
        render: (text) => (
          <Text>{dayjs(text).format("YYYY-MM-DD HH:mm:ss")}</Text>
        ),
      },
      {
        title: "Contents Count",
        key: "contentsCount",
        width: 120,
        align: "center",
        render: (_, record) => (
          <Tag color="blue">{record.contents?.length || 0}</Tag>
        ),
      },
      {
        title: "Action",
        key: "action",
        width: 100,
        align: "center",
        render: () => (
          <Tooltip title="Expand to view contents">
            <ExpandAltOutlined style={{ color: "#1890ff" }} />
          </Tooltip>
        ),
      },
    ],
    []
  );

  const expandedRowRender = (record) => {
    const contents = record.contents || [];

    if (contents.length === 0) {
      return (
        <div style={{ padding: "16px", textAlign: "center" }}>
          <Text type="secondary">No contents available for this rule</Text>
        </div>
      );
    }

    return (
      <div style={{ padding: "16px" }}>
        <Text strong style={{ marginBottom: "12px", display: "block" }}>
          Rule Contents ({contents.length} items)
        </Text>
        <List
          size="small"
          bordered
          dataSource={contents}
          renderItem={(item, index) => (
            <List.Item>
              <Space direction="vertical" size="small" style={{ width: "100%" }}>
                <Space>
                  <Tag color="green">#{index + 1}</Tag>
                  {item.sid && <Tag color="blue">SID: {item.sid}</Tag>}
                </Space>
                {item.value && (
                  <div>
                    <Text type="secondary">Value: </Text>
                    <Text code>{item.value}</Text>
                  </div>
                )}
              </Space>
            </List.Item>
          )}
          pagination={
            contents.length > 10
              ? {
                  pageSize: 10,
                  size: "small",
                  showSizeChanger: false,
                }
              : false
          }
        />
      </div>
    );
  };

  return (
    <Card
      title={
        <Space>
          <Text strong>Rules</Text>
          {selectedService && (
            <Tag color="blue">{selectedService}</Tag>
          )}
        </Space>
      }
      extra={
        <Button
          icon={<ReloadOutlined />}
          onClick={onRefresh}
          loading={loading}
          size="small"
        >
          Refresh
        </Button>
      }
      variant="borderless"
    >
      <Table
        columns={columns}
        dataSource={data}
        rowKey="name"
        loading={loading}
        size="small"
        expandable={{
          expandedRowRender,
          rowExpandable: (record) => record.contents && record.contents.length > 0,
          expandRowByClick: true,
        }}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} of ${total} rules`,
        }}
        scroll={{ x: 600 }}
      />
    </Card>
  );
};

export default memo(IdpsRulesTable);
