# AI Assistant Development Guide


#### Environment Variables
You can set the following environment variables to configure the AI Assistant:
- NIMBL_LLM_API_KEY: The API key of the LLM.
- NIMBL_LLM_TOKEN: The token of the LLM.
- NIMBL_LLM_HOST: The host of the OLLAMA server (i.g. http://localhost:11434).
- NIMBL_LLM_MODEL: The model of the LLM. for example: llama3.2, llama3.1, etc.

## Quick Start
1. The AI assistant mode is enabled by default when running `bbrootsvc`. You can disable it using the `-noai` flag if needed.
2. Choose your preferred LLM vendor by using the `-llm` flag:
   - For OpenAI (default): `-llm openai`, NIMBL support OpenAI models: gpt-4o, o4-mini
   - For Ollama: `-llm ollama`

   Example:
```bash
# Use OpenAI (default)
bbrootsvc -n root

# Use Ollama, NIMBL checks the running model and use the running model if the requested model is not running, otherwise NIMBL will use the requested model.
env NIMBL_LLM_MODEL=llama3 NIMBL_LLM_HOST=http://localhost:11434 ./bbrootsvc.exe -n root -llm ollama
Initialilzing AI assistant with model llama3 ...
warning: using default private key
WARNING: Ollama is running model qwen3:8b, but the requested model is llama3 NIMBL will use the running model qwen3:8b
ollama URL: http://localhost:11434
AI assistant is working
```

## For developers
- To modify system prompt refer `aiagent/project/project.go` ImportDefaultProject() function.
- To add new LLM vendor, refer `llm/provider.go`, new LLM provider should implement `llm.Provider` interface.





