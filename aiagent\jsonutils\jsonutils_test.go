package jsonutils

import (
	"testing"
)

func TestTrimToEnclosure(t *testing.T) {
	tests := []struct {
		input    string
		expected string
		hasError bool
	}{
		{"some text {\"key\": \"value\"} more text", "{\"key\": \"value\"}", false},
		{"[1, 2, 3] some text", "[1, 2, 3]", false},
		{"no brackets here", "", true},
		{"mismatched {brackets]", "", true},
		{"{nested {brackets}}", "{nested {brackets}}", false},
		{"[nested [brackets]]", "[nested [brackets]]", false},
		{"```json\n{\"key\": \"value\"}\n```", "{\"key\": \"value\"}", false},
	}

	for _, test := range tests {
		result, err := TrimToEnclosure(test.input)
		if (err != nil) != test.hasError {
			t.<PERSON><PERSON><PERSON>("TrimToEnclosure(%q) error = %v, wantErr %v", test.input, err, test.hasError)
			continue
		}
		if result != test.expected {
			t.<PERSON><PERSON><PERSON>("TrimToEnclosure(%q) = %q, want %q", test.input, result, test.expected)
		}
	}
}

// TestQueryJSONString tests the QueryJSONString function
func TestQueryJSONString(t *testing.T) {
	jsonString := `
	{
        "id": "b1d3d374-a04d-41cd-af94-dbdd32e7b402",
        "metadata": {
            "doc_key": "actions",
            "item_id": "get_llm_settings",
            "query": "Get the current LLM settings"
        },
        "content": "Get the current LLM settings",
        "similarity": 0.89793885
    }
	`
	query := ".metadata.doc_key"
	expected := "actions"
	result, err := QueryJSONString(jsonString, query)
	if err != nil {
		t.Errorf("QueryJSONString(%q, %q) error = %v", jsonString, query, err)
	}
	if result != expected {
		t.Errorf("QueryJSONString(%q, %q) = %q, want %q", jsonString, query, result, expected)
	}
}
