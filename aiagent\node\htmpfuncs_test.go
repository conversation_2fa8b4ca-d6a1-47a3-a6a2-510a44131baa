package node

import (
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/spf13/viper"
)

func TestGetWithToken(t *testing.T) {
	// Set up a test server to mock the API response.
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Check for the Authorization header.
		authHeader := r.Header.Get("Authorization")
		if authHeader != "Bearer test-token" {
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}

		// Check for query parameters.
		if r.URL.Query().Get("param1") != "value1" {
			http.Error(w, "Bad Request", http.StatusBadRequest)
			return
		}

		// Return a successful response.
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"message": "success"}`))
	}))
	defer ts.Close()

	// Set the environment variable for the token.
	os.Setenv("AAAGENT_API_TOKEN", "test-token")
	defer os.Unsetenv("AAAGENT_API_TOKEN")

	// Test case: Successful request.
	t.Run("Successful request", func(t *testing.T) {
		queryParams := map[string]string{"param1": "value1"}
		response, err := getWithToken(ts.URL, queryParams)
		if err != nil {
			t.Fatalf("expected no error, got %v", err)
		}
		expectedResponse := `{"message": "success"}`
		if response != expectedResponse {
			t.Fatalf("expected %s, got %s", expectedResponse, response)
		}
	})

	// Test case: Missing token.
	t.Run("Missing token", func(t *testing.T) {
		os.Unsetenv("AAAGENT_API_TOKEN")
		viper.Set("api_token", "")
		queryParams := map[string]string{"param1": "value1"}
		_, err := getWithToken(ts.URL, queryParams)
		if err == nil {
			t.Fatal("expected error, got none")
		}

		os.Setenv("AAAGENT_API_TOKEN", "test-token")
	})

	// Test case: Invalid URL.
	t.Run("Invalid URL", func(t *testing.T) {
		queryParams := map[string]string{"param1": "value1"}
		_, err := getWithToken(":", queryParams)
		if err == nil {
			t.Fatal("expected error, got none")
		}
	})

	// Test case: Non-OK HTTP status.
	t.Run("Non-OK HTTP status", func(t *testing.T) {
		queryParams := map[string]string{"param1": "wrongvalue"}
		_, err := getWithToken(ts.URL, queryParams)
		if err == nil {
			t.Fatal("expected error, got none")
		}

	})
}
