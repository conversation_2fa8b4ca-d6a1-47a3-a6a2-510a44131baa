package agents

import (
	"encoding/json"
	"io"
	"net/http"
	"strings"
)

// RunAPIParam is a struct that describes the parameters for running an API
// schema:
//
//	{
//		 "$schema": "https://json-schema.org/draft/2020-12/schema",
//		 "properties": {
//		   "url": {
//		     "type": "string"
//		   },
//		   "method": {
//		     "type": "string"
//		   },
//		   "request_body": {
//		     "oneOf": [
//		       {
//		         "type": "array"
//		       },
//		       {
//		         "type": "object"
//		       }
//		     ]
//		   }
//		 },
//		 "additionalProperties": false,
//		 "type": "object",
//		 "required": [
//		   "url",
//		   "method"
//		 ]
//		}
type RunAPIParam struct {
	URL         string `json:"url"`
	Method      string `json:"method"`
	RequestBody any    `json:"request_body,omitempty" jsonschema:"oneof_type=array;object"`
}

type NeedFetchData struct {
	NeedFetch bool          `json:"need_fetch"`
	RunAPIs   []RunAPIParam `json:"run_apis"`
}

// SendRequest send a request to the given URL
func SendRequest(param RunAPIParam, token string) (string, error) {
	client := &http.Client{}
	var req *http.Request
	var err error
	var reqBody []byte
	// if token is not empty, add it to the header
	if param.RequestBody != nil {
		reqBody, err = json.Marshal(param.RequestBody)
		if err != nil {
			return "", err
		}
		req, err = http.NewRequest(param.Method, param.URL, strings.NewReader(string(reqBody)))
	} else {
		req, err = http.NewRequest(param.Method, param.URL, nil)
	}
	if err != nil {
		return "", err
	}
	if token != "" {
		req.Header.Add("Authorization", "Bearer "+token)
	}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	return string(body), nil

}
