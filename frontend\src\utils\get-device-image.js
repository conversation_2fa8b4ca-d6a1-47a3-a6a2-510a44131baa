const defaultDeviceImage = "/src/assets/images/default-device.png";

function getDeviceImage(modelName) {
  if (!modelName) return defaultDeviceImage;

  try {
    const url = new URL(
      `https://nimbl.blackbeartechhive.com/api/v1/files/device-images/${modelName}.png`
    ).href;

    const img = new Image();
    img.src = url;

    return img.complete && img.naturalWidth !== 0 ? url : defaultDeviceImage;
  } catch (error) {
    console.error("Error creating device image URL:", error);
    return defaultDeviceImage;
  }
}

export { getDeviceImage };
