import { useMemo, useState } from "react";
import { App, Form } from "antd";
import { useGetKVStore } from "../../../services/queries";
import { useImportKVStore } from "../../../services/mutations";

/**
 * Custom hook for KVStore operations
 * @returns {Object} KVStore operations and state
 */
export const useKVStore = () => {
  const [form] = Form.useForm();
  const { notification } = App.useApp();
  const [searchFilter, setSearchFilter] = useState("");

  // Fetch KV store data
  const { data: kvStoreData = {}, isLoading, refetch } = useGetKVStore();

  // Import KV store mutation
  const importKVStore = useImportKVStore();

  // Filter KV store data based on search filter
  const filteredData = useMemo(() => {
    if (!searchFilter) return kvStoreData;

    const lowerCaseFilter = searchFilter.toLowerCase();
    const filtered = {};

    Object.entries(kvStoreData).forEach(([key, value]) => {
      if (
        key.toLowerCase().includes(lowerCaseFilter) ||
        String(value).toLowerCase().includes(lowerCaseFilter)
      ) {
        filtered[key] = value;
      }
    });

    return filtered;
  }, [kvStoreData, searchFilter]);

  // Format data for Descriptions component
  const descriptionItems = useMemo(() => {
    return Object.entries(filteredData).map(([key, value]) => ({
      key,
      label: key,
      children: value,
    }));
  }, [filteredData]);

  /**
   * Handle form submission to add new key-value pairs
   * @param {Object} values - Form values
   */
  const handleAddKeyValues = async (values) => {
    const keyStore = values.keyStore;

    if (!keyStore || keyStore.length <= 0) {
      notification.error({
        message: "Key name and key value should not be empty!",
      });
      return;
    }

    try {
      // Convert array of key-value pairs to object
      const keyStoreObj = keyStore.reduce(
        (obj, item) => Object.assign(obj, { [item.keyName]: item.keyValue }),
        {}
      );

      // Send to API
      await importKVStore.mutateAsync(keyStoreObj);

      notification.success({
        message: "Successfully added key-value pairs!",
      });

      // Reset form
      form.resetFields();
    } catch (error) {
      notification.error({
        message:
          error.data?.error ||
          error.data ||
          error.message ||
          "Something went wrong!",
      });
    }
  };

  return {
    form,
    kvStoreData,
    filteredData,
    descriptionItems,
    isLoading,
    refetch,
    searchFilter,
    setSearchFilter,
    handleAddKeyValues,
    importKVStore,
  };
};
