import { createFileRoute } from "@tanstack/react-router";
import { Col, Row } from "antd";
import { ClusterInfoTable, RootInfoCard } from "../../features/clusterinfo";
import { memo } from "react";

export const Route = createFileRoute("/_auth/clusterinfo")({
  component: ClusterInfoComponent,
});

// Memoized layout component
const ClusterInfoLayout = memo(() => (
  <Row gutter={[10, 10]}>
    <Col span={24}>
      <RootInfoCard />
    </Col>
    <Col span={24}>
      <ClusterInfoTable />
    </Col>
  </Row>
));

export function ClusterInfoComponent() {
  return <ClusterInfoLayout />;
}
