package main

import (
	"io"
	"net/http"
	"time"

	"github.com/go-chi/jwtauth/v5"
	"github.com/qeof/q"
)

type CmdInfo struct {
	Kind        string `json:"kind"`
	Timestamp   string `json:"timestamp"`
	Command     string `json:"command"`
	Result      string `json:"result"`
	Status      string `json:"status"`
	Name        string `json:"name"`
	NoOverwrite bool   `json:"nooverwrite"`
	All         bool   `json:"all"`
	NoSyslog    bool   `json:"nosyslog"`
	Client      string `json:"client"`
	DevId       string `json:"devid"`
	Tag         string `json:"tag"`
}

func PostWithToken(url, token string, body io.Reader) (resp *http.Response, err error) {
	bearer := "Bearer " + token
	req, err := http.NewRequest("POST", url, body)
	if err != nil {
		q.Q(err)
		return nil, err
	}
	req.Header.Add("Authorization", bearer)
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err = client.Do(req)
	if err != nil {
		q.Q(err.Error())
		return nil, err
	}
	return resp, nil
}

// GetWithToken encapsulates the http GET request with the token
func GetWithToken(url, token string) (resp *http.Response, err error) {
	bearer := "Bearer " + token
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		q.Q(err)
		return nil, err
	}
	req.Header.Add("Authorization", bearer)
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err = client.Do(req)
	if err != nil {
		q.Q(err)
		return nil, err
	}
	return resp, nil
}

var jwtSecret = []byte("mjnwmtssecret")
var jwtTokenAuth = jwtauth.New("HS256", jwtSecret, nil)

func GetToken(name string) (string, error) {
	var token string
	var err error

	if len(token) == 0 {
		// generate special token for CLI and node
		_, token, err = jwtTokenAuth.Encode(map[string]any{
			"user":      name,
			"timestamp": time.Now().Format(time.RFC3339),
		})
		if err != nil {
			return "", err
		}
	}
	return token, nil
}
