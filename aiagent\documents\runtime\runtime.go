// The runtime provides the DocumentProvider interface to access runtime information
package runtime

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mnms/clientutils"
	"net"
	"net/http"
	"net/url"
	"os"

	"strings"

	"github.com/qeof/q"
)

// DevInfoProvider is a struct that provides information about the device
type DevInfoProvider struct{}

// SimpleDevInfo is a struct that provides simple information about the device
// Because the original DevInfo struct contains too much information that is not helpful for actions,
// a simplified struct is added
type SimpleDevInfo struct {
	Mac            string          `json:"mac"`
	ModelName      string          `json:"modelname"`
	Timestamp      string          `json:"timestamp"`
	Scanproto      string          `json:"scanproto"`
	IPAddress      string          `json:"ipaddress"`
	Netmask        string          `json:"netmask"`
	Gateway        string          `json:"gateway"`
	Hostname       string          `json:"hostname"`
	Kernel         string          `json:"kernel"`
	Ap             string          `json:"ap"`
	ScannedBy      string          `json:"scannedby"`
	ArpMissed      int             `json:"arpmissed"`
	Lock           bool            `json:"lock"`
	ReadCommunity  string          `json:"readcommunity"`
	WriteCommunity string          `json:"writecommunity"`
	IsDHCP         bool            `json:"isdhcp"`
	IsOnline       bool            `json:"isonline"`
	TopologyProto  string          `json:"topologyproto"`
	SvcDiscoVia    string          `json:"svcdiscovia"`
	Capabilities   map[string]bool `json:"capabilities"`
	DeviceErrors   []string        `json:"device_errors"`
	UserName       string          `json:"username"`
	PassWord       string          `json:"password"`
	TunneledUrl    string          `json:"tunneled_url"`
	SnmpSupported  string          `json:"snmpSupported"`
	SnmpEnabled    string          `json:"snmpEnabled"`
	GwdModelName   string          `json:"gwdModelName"`
	Type           string          `json:"type"`
	Supported      []string        `json:"supported"`
	AgentVersion   string          `json:"agentVersion"`
}

// String returns the JSON string representation of the SimpleDevInfo
func (s SimpleDevInfo) String() string {
	jsonBytes, _ := json.Marshal(s)
	return string(jsonBytes)

}

// GetDescription returns a description of the device
func (d DevInfoProvider) GetDescription() string {
	return "Get DevInof (device information) from the Nimbl"
}

// GetName returns the name of the device
func (d DevInfoProvider) GetName() string {
	return "dev-info"
}

// Serialize returns the serialized version of the device information
func (d DevInfoProvider) Serialize() ([]byte, error) {
	return []byte("no-need"), nil
}

// Deserialize returns the deserialized version of the device information
func (d DevInfoProvider) Deserialize(data []byte) error {
	return nil
}

// Qeury accepts a query string and returns the device information
func (d DevInfoProvider) Query(query string) (string, error) {
	return d.Find(query)
}

// normalizeMac normalizes the MAC address
func normalizeMac(mac string) (string, error) {
	// Standard MAC addresses are 17 characters long with delimiters like ':' or '-'
	if len(mac) != 17 {
		return "", errors.New("invalid MAC address length")
	}
	// Parse the MAC address using net.ParseMAC
	parsedMAC, err := net.ParseMAC(mac)
	if err != nil {
		// Return an error if the MAC address is invalid
		return "", errors.New("invalid MAC Address")
	}
	// Convert to uppercase and use "-" as the delimiter
	formattedMAC := strings.ToUpper(parsedMAC.String())
	formattedMAC = strings.ReplaceAll(formattedMAC, ":", "-")

	return formattedMAC, nil
}

var localToken string
var localBBrootHost string

// GetToken check environment variable BB_TOKEN
func GetToken() string {
	// read from environment variable BB_TOKEN
	localToken = os.Getenv("BB_TOKEN")
	return localToken
}

// GetBBRootHost check environment variable BBROOT_HOST
func GetBBRootHost() string {
	localBBrootHost = os.Getenv("BBROOT_HOST")
	return localBBrootHost
}

// combineURL combines the base URL and the path
func combineURL(baseURL, path string) (string, error) {
	base, err := url.Parse(baseURL)
	if err != nil {
		return "", err
	}
	rel, err := url.Parse(path)
	if err != nil {
		return "", err
	}
	fullURL := base.ResolveReference(rel)

	return fullURL.String(), nil

}

// decodeJSONBody takes a response body as a JSON string and populates the SimpleDevInfo struct.
func decodeJSONBody(body io.ReadCloser, v interface{}) error {
	decoder := json.NewDecoder(body)
	return decoder.Decode(v)
}

// findDev accepts MAC address as a unique ID and returns the device information
func findDev(mac string) (*SimpleDevInfo, error) {
	token := GetToken()
	baseURL := GetBBRootHost()

	fullURL, err := combineURL(baseURL, "/api/v1/devices")

	if err != nil {
		return nil, err
	}

	resp, err := clientutils.Get(fullURL, token, map[string]string{"dev": mac})
	if err != nil {
		return nil, err
	}
	// read the response body
	defer resp.Body.Close()
	// Check if the response status code is not OK.
	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode == 401 {
			return nil, errors.New("unauthorized check your token settings. (env BB_TOKEN)")
		}
	}

	var devInfo SimpleDevInfo
	err = decodeJSONBody(resp.Body, &devInfo)
	if err != nil {
		return nil, err
	}

	return &devInfo, nil
}

// Find accepts MAC address as a unique ID and returns the device information
func (d DevInfoProvider) Find(msg string) (string, error) {

	// try to unmarshal { mac-list:["xx-xx-xx-xx-xx-xx", action:"get-dev-info" ] }
	type macList struct {
		MacList []string `json:"mac-list"`
		Action  string   `json:"action"`
	}
	q.Q(msg)
	var req macList
	err := json.Unmarshal([]byte(msg), &req)
	if err != nil {
		return "", fmt.Errorf("input is not a JSON: %s", msg)
	}

	standardMacs := []string{}
	// check msg is MAC xx-xx-xx-xx-xx-xx of xx:xx:xx:xx:xx:xx
	for _, msg := range req.MacList {
		standardMac, err := normalizeMac(msg)
		if err != nil {
			return "", fmt.Errorf("invalid MAC address: %s", msg)
		}
		standardMacs = append(standardMacs, standardMac)

	}
	req.MacList = standardMacs

	if !strings.HasPrefix(req.Action, "get-device") {
		return "", fmt.Errorf("action is not get-device-info: %s", req.Action)
	}

	if len(req.MacList) > 0 {
		var foundDevices []SimpleDevInfo
		for _, mac := range req.MacList {
			devinfo, err := findDev(mac)
			if err != nil {
				continue
			}
			// simpleDevInfo := devinfo2SimpleDevInfo(devinfo)
			foundDevices = append(foundDevices, *devinfo)
		}
		if len(foundDevices) == 0 {
			return "", errors.New("no device found")
		}

		jsonBytes, err := json.Marshal(foundDevices)
		if err != nil {
			return "", err
		}
		return string(jsonBytes), nil
	}

	return "", nil

}
