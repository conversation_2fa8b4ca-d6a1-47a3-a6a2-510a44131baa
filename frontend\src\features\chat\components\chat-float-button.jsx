import { MessageOutlined } from "@ant-design/icons";
import { FloatButton, Badge } from "antd";
import React, { memo } from "react";
import { useAppStore } from "../../../store/store";
import { useChatStore } from "../../../store/chat-store";

const ChatFloatButton = memo(() => {
  const { toggleChat, isChatOpen } = useAppStore();
  const { messages } = useChatStore();

  // Count unread messages (for future enhancement)
  const unreadCount = 0; // This could be implemented later

  return (
    <Badge count={unreadCount} size="small">
      <FloatButton
        shape="circle"
        type={isChatOpen ? "default" : "primary"}
        tooltip={isChatOpen ? "Close Chat" : "Open AI Assistant"}
        style={{
          insetInlineEnd: 10,
          insetBlockEnd: 115, // Position above command float button
        }}
        icon={<MessageOutlined />}
        onClick={toggleChat}
      />
    </Badge>
  );
});

ChatFloatButton.displayName = "ChatFloatButton";

export default ChatFloatButton;
