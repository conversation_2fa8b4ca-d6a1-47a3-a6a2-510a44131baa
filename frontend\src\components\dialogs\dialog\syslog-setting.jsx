import { App, Checkbox, Form, Input, InputNumber, Modal, Select } from "antd";
import React, { useCallback, useEffect } from "react";
import PropTypes from "prop-types";
import { useSendCommand } from "../../../services/mutations";
import { handleCommandExecution } from "../../../utils/command-execution";
import {
  FORM_FIELDS,
  VALIDATION_RULES,
} from "../../../constants/syslog-setting";
import { generateCommand } from "../../../utils/generate-commands";

const SyslogSettingDialog = ({ data, onClose }) => {
  const { notification } = App.useApp();
  const sendCommand = useSendCommand();
  const [form] = Form.useForm();

  const handleSubmit = useCallback(
    (values) => {
      let commands = [];
      if (Array.isArray(data)) {
        data.map((item) => {
          const command = generateCommand(item, "", values, "syslog");
          commands.push(command);
        });
        commands = commands.flat();
      } else {
        commands = generateCommand(data.mac, "", values, "syslog");
      }
      handleCommandExecution(commands, sendCommand, notification);
      onClose();
    },
    [data, data?.mac, sendCommand, notification, onClose]
  );

  useEffect(() => {
    if (Array.isArray(data)) {
      return;
    }
    const { logLevel, logToFlash, logToServer, serverIp, serverPort } =
      data.syslogSetting;
    form.setFieldsValue({
      [FORM_FIELDS.LOG_LEVEL]: Number(logLevel),
      [FORM_FIELDS.LOG_TO_FLASH]: logToFlash === "1",
      [FORM_FIELDS.LOG_TO_SERVER]: logToServer === "1",
      [FORM_FIELDS.SERVER_IP]: serverIp,
      [FORM_FIELDS.SERVER_PORT]: Number(serverPort),
    });
  }, [form, data]);

  return (
    <Modal
      title="Syslog Setting"
      maskClosable={false}
      open
      destroyOnClose
      onCancel={onClose}
      okButtonProps={{ autoFocus: true, htmlType: "submit" }}
      style={{ top: 20 }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            handleSubmit(values);
            form.resetFields();
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form
        form={form}
        layout="vertical"
        name="syslog_setting_form"
        clearOnDestroy
      >
        <Form.Item name={FORM_FIELDS.LOG_TO_FLASH} valuePropName="checked">
          <Checkbox>Log to Flash</Checkbox>
        </Form.Item>
        <Form.Item name={FORM_FIELDS.LOG_LEVEL} label="Log Level">
          <Select>
            <Select.Option value={0}>0: (LOG EMERG)</Select.Option>
            <Select.Option value={1}>1: (LOG_ALERT)</Select.Option>
            <Select.Option value={2}>2: (LOG_CRIT)</Select.Option>
            <Select.Option value={3}>3: (LOG_ERR)</Select.Option>
            <Select.Option value={4}>4: (LOG_WARNING)</Select.Option>
            <Select.Option value={5}>5: (LOG_NOTICE)</Select.Option>
            <Select.Option value={6}>6: (LOG_INFO)</Select.Option>
            <Select.Option value={7}>7: (LOG_DEBUG)</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item name={FORM_FIELDS.LOG_TO_SERVER} valuePropName="checked">
          <Checkbox>Log to Server</Checkbox>
        </Form.Item>
        <Form.Item
          name={FORM_FIELDS.SERVER_IP}
          label="Server IP"
          rules={VALIDATION_RULES[FORM_FIELDS.SERVER_IP]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          name={FORM_FIELDS.SERVER_PORT}
          label="Server Service Port"
          rules={VALIDATION_RULES[FORM_FIELDS.SERVER_PORT]}
        >
          <InputNumber style={{ width: "100%" }} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

SyslogSettingDialog.propTypes = {
  data: PropTypes.shape({
    syslogSetting: PropTypes.shape({
      logLevel: PropTypes.string.isRequired,
      logToFlash: PropTypes.string.isRequired,
      logToServer: PropTypes.string.isRequired,
      serverIp: PropTypes.string.isRequired,
      serverPort: PropTypes.string.isRequired,
    }).isRequired,
  }).isRequired,
  onClose: PropTypes.func.isRequired,
};

export default React.memo(SyslogSettingDialog);
