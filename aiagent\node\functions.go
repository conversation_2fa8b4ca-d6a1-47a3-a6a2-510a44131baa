package node

import "fmt"

// NodeFunc is a function that takes input and query and returns a RunNodeOutput
// Note: When returning, consider how to handle errors. If you are unsure of the error's impact on the entire flow,
// you can directly return the query.
// NodeFunc mainly processes the content returned by LLM (usually a JSON object)
// and performs specific actions based on the LLM's response.
type NodeFunc func(input RunNodeOutput, query string) (RunNodeOutput, error)

var nodeFunctions = []NodeFuncInfo{
	GetWithTokenInfo,
	ExecuteAPIInfo,
}

type NodeFuncInfo struct {
	Name        string   `json:"name"`
	Description string   `json:"description"`
	Function    NodeFunc `json:"-"`
	Schema      string   `json:"schema"`
}

var NodeFuncs = []NodeFuncInfo{
	ExecuteAPIInfo,
}

type NodeFuncDoc struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Schema      string `json:"schema"`
}

// GetFunctionsDocument returns the document of all functions
func GetFunctionsDocument() []NodeFuncDoc {
	var docs []NodeFuncDoc
	for _, nf := range nodeFunctions {
		docs = append(docs, NodeFuncDoc{
			Name:        nf.Name,
			Description: nf.Description,
			Schema:      nf.Schema,
		})
	}
	return docs
}

// RunFunction runs the function with the given name
func RunFunction(name string, input RunNodeOutput, query string) (RunNodeOutput, error) {
	// check function exist
	var f NodeFunc
	for _, nf := range nodeFunctions {
		if nf.Name == name {
			f = nf.Function
			break
		}
	}
	if f == nil {
		return input, fmt.Errorf("function %s not found", name)
	}

	return f(input, query)

}
