package payload

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/google/gonids"
)

const (
	byteTestLittle    uint16 = 1 << iota // "little" endian value
	byteTestBig                          // "lit                   // "big" endian value
	byteTestString                       // "string" value
	byteTestRelative                     // "relative" offset
	byteTestDCE                          // DCE enabled
	byteTestBitmask                      // Bitmask supplied
	byteTestValueVar                     // Byte extract value enabled
	byteTestOffsetVar                    // Byte extract value enabled
	byteTestNBytesVar                    // Missing comment (add description)
)

type byteTest struct {
	nbytes              uint8  /**< Number of bytes to compare */
	op                  string /**< Operator used to compare */
	base                base   /**< String value base (oct|dec|hex) */
	bitmask_shift_count uint8  /**< bitmask trailing 0 count */
	flags               uint16 /**< Flags (big|little|relative|string|bitmask) */
	negop               bool
	offset              int32  /**< Offset in payload */
	bitmask             uint32 /**< bitmask value */
	value               uint64
	ck                  func(uint64, uint64) bool
}

func newByteTest(l *list, gb *gonids.ByteMatch) error {
	var offset string
	b, err := parseByteTest(gb, &offset)
	if err != nil {
		return err
	}
	if len(gb.Value) > 0 && gb.Value[0] != '-' && isalpha(gb.Value[0]) {
		index := uint8(0)
		if !byteRetrieveSMVar(gb.Value, l, &index) {
			return fmt.Errorf("unknown byte_test var "+
				"seen in byte_test - %s", gb.NumBytes)
		}
		b.value = uint64(index)
		b.flags |= byteTestValueVar
	} else {
		n, err := strconv.ParseUint(gb.Value, 0, 64)
		if err != nil {
			return err
		}
		b.value = uint64(n)
	}

	op := gb.Operator
	b.op = op
	if strings.HasPrefix(gb.Operator, "!") {
		b.negop = true
		op = strings.TrimLeft(op, "!")
	}
	ck, err := b.createVerify(op)
	if err != nil {
		return err
	}
	b.ck = ck
	d := &Detect{
		id:         l.id,
		postition:  gb.DataPosition,
		detectedID: detectByteTest,
		data:       b,
	}
	err = l.appendList(d)
	if err != nil {
		return err
	}
	return nil
}

func parseByteTest(gb *gonids.ByteMatch, offset *string) (*byteTest, error) {
	byt, err := strconv.Atoi(gb.NumBytes)
	if err != nil {
		return nil, err
	}
	b := &byteTest{
		op:     gb.Operator,
		nbytes: uint8(byt),
	}
	for _, op := range gb.Options {
		switch {
		case strings.HasPrefix(op, "offset"):
			args := strings.Fields(op)
			if len(args) != 2 {
				return nil, fmt.Errorf("invalid offset format: %s", op)
			}
			off := args[1]
			if len(off) > 0 && off[0] != '-' && isalpha(off[0]) {
				*offset = off
			} else {
				n, err := strconv.ParseInt(off, 10, 32)
				if err != nil {
					return nil, err
				}
				b.offset = int32(n)
			}
		case op == "relative":
			b.flags |= byteTestRelative
		case op == "string":
			b.flags |= byteTestString
		case op == "dce":
			b.flags |= byteTestDCE

		case strings.HasPrefix(op, "bitmask"):
			b.flags |= byteTestBitmask
			args := strings.Split(op, " ")
			if len(args) != 2 {
				return nil, fmt.Errorf("invalid bitmask format: %s", op)
			}
			v, err := strconv.ParseUint(args[1], 0, 32)
			if err != nil {
				return nil, err
			}
			b.bitmask = uint32(v)
		case op == "big":
			if b.flags&byteTestLittle == byteTestBig {
				b.flags = ^byteTestLittle
			}
			b.flags |= byteTestBig
		case op == "little":
			b.flags |= byteTestLittle
		case op == "oct":
			b.base = byteTestBaseOct
		case op == "dec":
			b.base = byteTestBaseDec
		case op == "hex":
			b.base = byteTestBaseHex
		}
	}

	if b.flags&byteTestBitmask == byteTestBitmask {
		if b.bitmask > 0 {
			bmask := b.bitmask
			for (bmask & 0x1) == 0 {
				bmask = bmask >> 1
				b.bitmask_shift_count++
			}
		}
	}
	return b, nil
}

func (b *byteTest) inspect(p *Packet, flags uint16, offset, nbytes int32, value uint64) int {
	if p.bufferLen == 0 {
		return 0
	}

	len := int32(0)
	ptr := uint32(0)
	if flags&byteTestRelative == byteTestRelative {
		ptr += p.bufferOffset
		len = int32(p.bufferLen) - int32(p.bufferOffset)

		ptr += uint32(offset)
		len -= offset
		if int32(ptr) < 0 || len <= 0 {
			return 0
		}

	} else {
		ptr = uint32(offset)
		len = int32(p.bufferLen) - offset
	}
	if int32(ptr) < 0 || int32(nbytes) > len {
		return 0
	}
	var val uint64
	buf := p.buffer[ptr:]
	if flags&byteTestString == byteTestString {
		extbytes := byteExtractStringUint64(buf, int(b.base), nbytes, &val)
		if extbytes <= 0 {
			if val == 0 {
				return 0
			} else {
				return -1
			}
		}
	} else {
		var end int
		if flags&byteTestLittle == byteTestLittle {
			end = byteLittleEndiad
		} else {
			end = byteBigEndiad
		}
		r := byteExtractUint64(buf, end, uint16(nbytes), &val)
		if r != int(nbytes) {
			return -1
		}

	}

	if b.flags&byteTestBitmask == byteTestBitmask {
		val &= uint64(b.bitmask)
		if val > 0 && b.bitmask_shift_count > 0 {
			val = val >> uint64(b.bitmask_shift_count)
		}
	}

	match := b.ck(val, value)

	if !b.negop && match || b.negop && !match {
		return 1
	}
	return 0
}

func (b *byteTest) createVerify(op string) (func(uint64, uint64) bool, error) {
	switch op {
	case "<":
		return func(p, value uint64) bool { return p < value }, nil
	case ">":
		return func(p, value uint64) bool { return p > value }, nil
	case "=":
		return func(p, value uint64) bool { return p == value }, nil
	case "<=":
		return func(p, value uint64) bool { return p <= value }, nil
	case ">=":
		return func(p, value uint64) bool { return p >= value }, nil
	case "&":
		return func(p, value uint64) bool { return p&value > 0 }, nil
	case "^":
		return func(p, value uint64) bool { return p^value > 0 }, nil

	}
	return nil, fmt.Errorf("invalid operator %s", op)
}
