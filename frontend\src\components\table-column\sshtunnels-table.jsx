import { App, theme } from "antd";
import React, { useCallback } from "react";
import { DeleteOutlined } from "@ant-design/icons";
import { useSendCommand } from "../../services/mutations";
import { handleCommandExecution } from "../../utils/command-execution";

export const useSSHTunnelColumn = () => {
  const { token } = theme.useToken();
  const { notification } = App.useApp();
  const sendCommand = useSendCommand();

  const handleDeleteSSHTunnel = useCallback(
    async (listen_port) => {
      const command = [
        { command: `ssh tunnel close ${listen_port}`, kind: "root" },
      ];
      handleCommandExecution(command, sendCommand, notification);
    },
    [sendCommand, notification]
  );

  return React.useMemo(
    () => [
      {
        title: "Dev Mac",
        dataIndex: "dev_mac",
        key: "dev_mac",
        exportable: true,
      },
      {
        title: "Listen Port",
        dataIndex: "listen_port",
        key: "listen_port",
        exportable: true,
      },
      {
        title: "Remote Addr",
        dataIndex: "remote_addr",
        key: "remote_addr",
        exportable: true,
      },
      {
        title: "Action",
        key: "action",
        align: "center",
        render: (_, { listen_port }) => {
          return (
            <DeleteOutlined
              style={{ color: token.colorError }}
              onClick={() => handleDeleteSSHTunnel(listen_port)}
            />
          );
        },
      },
    ],
    []
  );
};
