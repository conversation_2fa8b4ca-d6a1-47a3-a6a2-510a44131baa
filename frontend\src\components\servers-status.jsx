import React from "react";
import { <PERSON>lex, Badge, Tooltip, Typography } from "antd";
import { useSettingStore } from "../store/setting-store";
import { useGetServerStatus } from "../services/queries";

const { Text } = Typography;

/**
 * Server status indicator component that shows the connection status
 * to the Network Management System backend
 * @returns {React.ReactElement} Server status component
 */
const ServersStatus = () => {
  const { baseURL } = useSettingStore();
  const { isSuccess, isError } = useGetServerStatus(baseURL);

  const getStatusConfig = () => {
    if (isSuccess) return { status: "processing", color: "green" };
    if (isError) return { status: "error", color: "red" };
    return { status: "error", color: "red" };
  };

  const { status, color } = getStatusConfig();

  return (
    <Tooltip
      title={`Server URL: ${baseURL}`}
      data-testid="server-status-tooltip"
    >
      <Flex
        gap={10}
        align="center"
        justify="center"
        data-testid="server-status-container"
      >
        <Badge
          status={status}
          color={color}
          data-testid="server-status-badge"
        />
        <Text>{baseURL}</Text>
      </Flex>
    </Tooltip>
  );
};

export default ServersStatus;
