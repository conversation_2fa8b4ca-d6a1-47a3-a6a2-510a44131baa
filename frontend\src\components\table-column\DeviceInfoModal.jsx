import React from "react";
import { <PERSON><PERSON>, Card } from "antd";
import { ProDescriptions } from "@ant-design/pro-descriptions";
import { DEVICE_INFO_COLUMNS } from "./constants";
import { getDeviceImage } from "../../utils/get-device-image";

const DeviceInfoModal = ({ record }) => {
  const imageUrl = getDeviceImage(record.modelname);

  return (
    <Card style={{ minWidth: 550 }}>
      <Card.Meta
        avatar={<Avatar shape="square" src={imageUrl} size="large" />}
        title={`${record.mac} (${record.hostname})`}
        description={
          <ProDescriptions
            dataSource={record}
            column={2}
            columns={DEVICE_INFO_COLUMNS}
          />
        }
      />
    </Card>
  );
};

export const deviceInfoModal = (record) => ({
  icon: null,
  width: 600,
  title: "Device Info",
  style: { top: 20 },
  content: <DeviceInfoModal record={record} />,
});
