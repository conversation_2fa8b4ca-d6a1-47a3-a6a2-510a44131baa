// anomaly chain is a Chain implement for Nimbl anomaly detection.

package anomalychain

import (
	"context"
	"encoding/json"
	"fmt"
	"mnms/anomaly_detection/chain"
	"mnms/anomaly_detection/chain/prompt"
	"mnms/anomaly_detection/def"
	"mnms/anomaly_detection/retriever"
	"mnms/anomaly_detection/statistic"
	"mnms/anomaly_detection/store/anomstore"
	"mnms/llm"
	"os"

	"github.com/qeof/q"
)

type anomalyChain struct {
	m llm.CompleteEmbedder
	r def.Retriever
	s def.Store
}

// writeMsg
func writeMsg(debug string, msg string) {
	if debug == "" {
		return
	}
	f, err := os.OpenFile(debug, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		q.Q("open debug file error: ", err)
	}
	defer f.Close()
	// add timestamp
	_, err = f.WriteString(msg + "\n")
	return
}

// Run runs the anomaly chain.
func (c *anomaly<PERSON>hain) Run(ctx context.Context, input string, opts ...chain.OptionAssigner) (string, error) {
	o := &chain.Options{}

	for _, opt := range opts {
		opt(o)
	}
	debug := o.DebugOutputFile

	// get relevant documents
	score := statistic.LocalStatistic.Settings.Score
	writeMsg(debug, "query input: "+input)
	writeMsg(debug, "query relevant documents with threshold: "+fmt.Sprintf("%f", score))
	docs, err := c.r.GetReleventDocumentsWithThreshold(ctx, input, 5, score)
	if err != nil {
		writeMsg(debug, "get relevant documents error: "+err.Error())
		return "", err
	}
	writeMsg(debug, "relevant documents: "+fmt.Sprintf("%d", len(docs)))
	for _, doc := range docs {
		writeMsg(debug, "doc: "+doc.Content+" score: "+fmt.Sprintf("%f", doc.Score))
	}

	prmpt := prompt.GetPrompt()

	type meta struct {
		Normal bool   `json:"normal"`
		Reason string `json:"reason"`
	}

	normalMessages := []string{}
	anomalyMessages := []string{}

	for _, doc := range docs {
		m := doc.Metadata
		if m["normal"] == true {
			normalMessages = append(normalMessages, doc.Content)
		} else {
			anomalyMessages = append(anomalyMessages, doc.Content)
		}
	}

	prmpt.SetUserPrompt(input)
	if len(normalMessages) > 0 {
		prmpt.AddNormalMessages(normalMessages)
	}
	if len(anomalyMessages) > 0 {
		prmpt.AddAnomalies(anomalyMessages)
	}

	// query llm

	writeMsg(debug, fmt.Sprintf("system prompt: \n %s \n", prmpt.SystemPrompt))
	writeMsg(debug, fmt.Sprintf("user input: \n %s \n", input))
	answer, err := c.m.Complete(ctx, prmpt.Messages())
	if err != nil {
		writeMsg(debug, "complete error: "+err.Error())
		return answer, err
	}

	if o.AddRAG && len(docs) <= 0 {
		// add the input message to the store
		meta := map[string]any{}
		if err := json.Unmarshal([]byte(answer), &meta); err == nil {
			_, err := c.r.Add(ctx, input, meta)
			if err != nil {
				writeMsg(debug, "add document error: "+err.Error())
				return "", err
			}
		}

	}

	if err != nil {
		writeMsg(debug, "complete error: "+err.Error())
		return "", err
	}
	writeMsg(debug, "answer: "+answer)

	if len(o.ReportFile) > 0 {
		report := fmt.Sprintf("Q: %s\nA: %s\n", input, answer)
		writeMsg(o.ReportFile, report)
	}

	return answer, nil
}

// NewChain creates a new AnomalyChain.
func NewChain(m llm.LLMClient) chain.ChainRunner {
	s := &anomstore.LocalVectors
	r := retriever.NewRetriever(s, m)
	return &anomalyChain{
		m: m,
		r: r,
		s: s,
	}
}
