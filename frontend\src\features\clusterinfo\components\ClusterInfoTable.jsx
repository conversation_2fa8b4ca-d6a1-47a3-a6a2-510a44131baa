import React, { useState, useEffect, memo, useMemo } from "react";
import { theme } from "antd";
import ProTable from "@ant-design/pro-table";
import dayjs from "dayjs";
import { useClusterInfoColumns } from "../../../components/table-column/clusterinfo-table";
import { useGetClusterInfo } from "../../../services/queries";
import ExportData from "../../../components/export-data/export-data";

const ClusterInfoTable = memo(() => {
  const token = theme.useToken();
  const [exportClusterInfoData, setExportClusterInfoData] = useState([]);
  const columns = useClusterInfoColumns();

  const {
    data: clusterData = [],
    isFetching: clusterInfoFetching,
    refetch: handleRefreshClick,
  } = useGetClusterInfo();

  useEffect(() => {
    if (clusterData.length > 0) {
      const expClusterInfo = clusterData.map((item) => {
        const formatedStartDate = dayjs(item.start * 1000).format(
          "YYYY/MM/DD HH:mm:ss"
        );
        const formatedNowDate = dayjs(item.now * 1000).format(
          "YYYY/MM/DD HH:mm:ss"
        );
        return {
          ...item,
          start: formatedStartDate,
          now: formatedNowDate,
        };
      });
      setExportClusterInfoData(expClusterInfo);
    }
  }, [clusterData]);

  return (
    <ProTable
      cardProps={{
        style: { boxShadow: token?.Card?.boxShadow },
      }}
      columns={columns}
      dataSource={clusterData}
      rowKey="name"
      headerTitle="Cluster Information"
      loading={clusterInfoFetching}
      size="small"
      scroll={{ x: 1100 }}
      toolbar={{
        actions: useMemo(
          () => [
            <ExportData
              key="export-data"
              Columns={columns}
              DataSource={exportClusterInfoData}
              title="Cluster_Info"
            />,
          ],
          [columns, exportClusterInfoData]
        ),
      }}
      options={useMemo(
        () => ({
          reload: () => {
            handleRefreshClick();
          },
          fullScreen: false,
        }),
        [handleRefreshClick]
      )}
      search={false}
      dateFormatter="string"
      columnsState={{
        persistenceKey: "clusterinfo-table",
        persistenceType: "localStorage",
      }}
    />
  );
});

export default ClusterInfoTable;
