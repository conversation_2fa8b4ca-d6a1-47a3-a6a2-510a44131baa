import { useState, useMemo } from "react";
import { useGetDevices } from "../../../services/queries";
import { useSettingStore } from "../../../store/setting-store";
import { deviceFilterColumns } from "../../../components/table-column/device-table";

export const useDeviceFilters = () => {
  const [inputSearch, setInputSearch] = useState("");
  const { inventoryType } = useSettingStore();
  const { data = [], isFetching, refetch } = useGetDevices(inventoryType);

  const filteredData = useMemo(() => {
    const searchTerm = inputSearch.toLowerCase();
    return data.filter((row) =>
      deviceFilterColumns.some((column) =>
        String(row[column.dataIndex]).toLowerCase().includes(searchTerm)
      )
    );
  }, [data, inputSearch]);

  return {
    filteredData,
    isFetching,
    refetch,
    inputSearch,
    setInputSearch,
  };
};
