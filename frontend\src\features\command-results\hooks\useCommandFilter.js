import { useMemo, useState, useCallback } from "react";
import { useGetAllCommands } from "../../../services/queries";

export const useCommandFilters = (columns) => {
  const [inputSearch, setInputSearch] = useState("");
  const { data = [], isFetching, refetch, error } = useGetAllCommands();

  const handleSearch = useCallback((searchValue) => {
    setInputSearch(searchValue?.trim() ?? "");
  }, []);

  const filteredData = useMemo(() => {
    if (!Array.isArray(data)) {
      console.error("Expected data to be an array");
      return [];
    }

    if (!inputSearch) return data;

    const searchTerm = inputSearch.toLowerCase();
    return data.filter((row) =>
      columns.some((column) => {
        const value = row[column.dataIndex];
        return (
          value != null && String(value).toLowerCase().includes(searchTerm)
        );
      })
    );
  }, [data, inputSearch, columns]);

  return {
    filteredData,
    isFetching,
    error,
    refetch,
    inputSearch,
    setInputSearch: handleSearch,
  };
};
