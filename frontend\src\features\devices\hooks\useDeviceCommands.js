import { useCallback } from "react";
import { App } from "antd";
import { useSendCommand } from "../../../services/mutations";
import { handleCommandExecution } from "../../../utils/command-execution";

export const DEVICE_COMMANDS = {
  BEEP: (mac) => `beep ${mac}`,
  RESET: (mac) => `reset ${mac}`,
  SNMP_ENABLE: (mac) => `snmp enable ${mac}`,
  SAVE_CONFIG: (mac) => `config save ${mac}`,
  OPEN_WEB_TUNNEL: (mac) => `agent ssh reverse websrv ${mac}`,
  DEVICE_DELETE: (mac) => `device delete ${mac}`,
};

export const useDeviceCommands = () => {
  const { notification } = App.useApp();
  const sendCommand = useSendCommand();

  const executeCommand = useCallback(
    (commandFn, mac) => {
      const command = [{ command: commandFn(mac) }];
      return handleCommandExecution(command, sendCommand, notification);
    },
    [sendCommand, notification]
  );

  const executeMassCommand = useCallback(
    (commandFn, macs) => {
      const commands = macs.map((mac) => ({ command: commandFn(mac) }));
      return handleCommandExecution(commands, sendCommand, notification);
    },
    [sendCommand, notification]
  );

  const handleWebOpen = useCallback((url) => window.open(url, "_blank"), []);

  return {
    executeCommand,
    executeMassCommand,
    handleWebOpen,
  };
};
