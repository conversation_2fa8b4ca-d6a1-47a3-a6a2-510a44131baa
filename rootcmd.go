package mnms

import (
	"encoding/json"
	"sort"
	"strconv"
	"strings"

	anm "mnms/anomaly_detection"
	anmreport "mnms/anomaly_detection/report"

	"github.com/qeof/q"
)

// runRootCmd
func runRootCmd(cmdinfo *CmdInfo) *CmdInfo {
	defer sendCmdSyslog("RunCmd", cmdinfo)

	if !QC.IsRoot {
		q.Q("not running as root, will not retrieveRootCmd")
		cmdinfo.Status = "error: not running as root"
		return cmdinfo
	}
	if cmdinfo.Kind != "root" {
		q.Q("not a root cmd", cmdinfo)
		cmdinfo.Status = "error: not a root cmd"
		return cmdinfo
	}
	if cmdinfo.Command == "" {
		q.Q("error: empty command", cmdinfo)
		cmdinfo.Status = "error: empty command"
		return cmdinfo
	}
	switch {
	case strings.HasPrefix(cmdinfo.Command, "config local syslog "):
		return RootConfigSyslogCmd(cmdinfo)
	case strings.HasPrefix(cmdinfo.Command, "wg "):
		return WgCmd(cmdinfo)
	case strings.HasPrefix(cmdinfo.Command, "anomaly reports"):
		return AnomalyReportCmd(cmdinfo)
	case strings.HasPrefix(cmdinfo.Command, "ssh "):
		return SshCmd(cmdinfo)
	case strings.HasPrefix(cmdinfo.Command, "firewall "):
		return FirewallCmd(cmdinfo)
	case strings.HasPrefix(cmdinfo.Command, "service "):
		return ServiceCmd(cmdinfo)
	default:
		q.Q("unrecognized", cmdinfo.Command)
		cmdinfo.Status = "error: unknown command"
		return cmdinfo
	}
}

func RootConfigSyslogCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command

	if QC.IsRoot {
		if strings.HasPrefix(cmd, "config local syslog path") {
			return SyslogSetPathCmd(cmdinfo)
		}
		if strings.HasPrefix(cmd, "config local syslog maxsize") {
			return SyslogSetMaxSizeCmd(cmdinfo)
		}

		if strings.HasPrefix(cmd, "config local syslog compress") {
			return SyslogSetCompressCmd(cmdinfo)
		}

		if strings.HasPrefix(cmd, "config local syslog read") {
			return ReadSyslogCmd(cmdinfo)
		}

		if strings.HasPrefix(cmd, "config local syslog remote") {
			return SyslogSetRemoteCmd(cmdinfo)
		}

		if strings.HasPrefix(cmd, "config local syslog backup-after-forward") {
			return SyslogSetBakAfterFwdCmd(cmdinfo)
		}
	}

	q.Q("unrecognized", cmd, len(cmd))
	cmdinfo.Status = "error:  this command requires to run kind is root or unknown command "
	return cmdinfo
}

// AnomalyReportCmd handle anomaly reports command
func AnomalyReportCmd(cmdinfo *CmdInfo) *CmdInfo {
	words := strings.Fields(cmdinfo.Command)
	counttxt := ""
	if len(words) >= 3 {
		counttxt = words[2]
	}

	count := len(anm.AnomalyReports)
	var err error
	if counttxt != "" {
		count, err = strconv.Atoi(counttxt)
		if err != nil {
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		if count <= 0 {
			count = len(anm.AnomalyReports)
		}
		if count > len(anm.AnomalyReports) {
			count = len(anm.AnomalyReports)
		}
	}
	responseReports := make([]anmreport.ReportSummary, len(anm.AnomalyReports))

	for _, report := range anm.AnomalyReports {
		responseReports = append(responseReports, *report.GetSummary())
	}

	sort.Sort(anmreport.SortReportSummaryByUntil(responseReports))
	jsonBytes, err := json.Marshal(responseReports[:count])
	if err != nil {
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	cmdinfo.Status = "ok"
	cmdinfo.Result = string(jsonBytes)
	return cmdinfo
}
