import React, { memo } from "react";
import { Card, List, Typography, Tag, Space, Empty } from "antd";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";

const { Text } = Typography;

// Extend dayjs with relative time plugin
dayjs.extend(relativeTime);

/**
 * Component to display alert messages from the socket store
 * @param {Object} props Component props
 * @param {Array} props.messages Array of alert messages
 * @returns {JSX.Element} MessageAlerts component
 */
const MessageAlerts = ({ messages = [] }) => {
  return (
    <Card
      title="Alert Messages"
      variant="borderless"
      style={{
        height: "calc(100vh - 80px)",
        display: "flex",
        flexDirection: "column",
        position: "sticky",
        top: 16,
      }}
      styles={{
        body: {
          flex: 1,
          padding: "12px 24px",
          overflow: "hidden",
        },
      }}
    >
      {messages.length === 0 ? (
        <Empty description="No alert messages" style={{ height: "100%" }} />
      ) : (
        <div
          className="custom-scrollbar"
          style={{ height: "100%", overflow: "auto", paddingRight: 4 }}
        >
          <List
            dataSource={messages}
            renderItem={(item) => (
              <List.Item>
                <List.Item.Meta
                  title={
                    <Space>
                      <Text strong>{item.title}</Text>
                      <Tag color="blue">{dayjs(item.time_stamp).fromNow()}</Tag>
                    </Space>
                  }
                  description={item.message}
                />
              </List.Item>
            )}
          />
        </div>
      )}
    </Card>
  );
};

export default memo(MessageAlerts);
