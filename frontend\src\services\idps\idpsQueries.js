import { useQuery } from "@tanstack/react-query";
import { IdpsService } from "./idpsService";

/**
 * Custom hook for fetching IDPS report data
 * @returns {Object} Query result with IDPS data
 */
export const useIdpsReport = () => {
  return useQuery({
    queryKey: ["idps", "report"],
    queryFn: IdpsService.getReport,
    refetchInterval: 30000, // Refresh every 30 seconds
    retry: 3,
    staleTime: 5000, // Consider data stale after 5 seconds
  });
};

/**
 * Custom hook for processing IDPS data for a specific service
 * @param {Object} idpsData - Raw IDPS data
 * @param {string} selectedService - Selected service name
 * @returns {Object} Processed service data
 */
export const useProcessedIdpsData = (idpsData, selectedService) => {
  const selectedServiceData = selectedService && idpsData ? idpsData[selectedService] : null;

  // Process rules data
  const rulesData = selectedServiceData?.rules || [];

  // Process events data
  const eventsData = selectedServiceData?.event || [];

  // Process packet data for chart
  const packetData = selectedServiceData?.rulepackets || [];

  // Process record list data
  const recordListData = selectedServiceData?.recordlist || [];

  // Calculate statistics
  const statistics = {
    totalEvents: eventsData.length,
    alertEvents: eventsData.filter((event) => event.type === "alert").length,
    dropEvents: eventsData.filter((event) => event.type === "drop").length,
  };

  return {
    selectedServiceData,
    rulesData,
    eventsData,
    packetData,
    recordListData,
    statistics,
  };
};
