package documents

import (
	"context"
	"mnms/llm"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

// getllmsettings
func getllmsettings(t *testing.T) *llm.LargeLanguageModel {
	// check os.Getenv("OPENAI_API_KEY")
	if os.Getenv("OPENAI_API_KEY") == "" {
		t.Skip("OPENAI_API_KEY is not set")
	}
	llmsettings := llm.GPTSettings()
	llmsettings.Model = "gpt-4o"
	return llmsettings
}

// TestRelevantDocuments tests the relevant documents
func TestRelevantDocuments(t *testing.T) {
	llmsettings := getllmsettings(t)
	var relDoc RelevantDocumentAdder = NewLocalRelevantDocuments(llmsettings)

	// Add some documents
	assert.NoError(t, relDoc.AddDoc(context.Background(), &RelevantDoc{
		Category: "test-cate-1",
		Content:  "test1",
		Doc:      "test1 document",
	}))
	assert.NoError(t, relDoc.AddDoc(context.Background(), &RelevantDoc{
		Category: "test-cate-2",
		Content:  "test2",
		Doc:      "test2 document",
	}))

	// Testing add doc with reference
	doc1 := `
	Create chat completion
post https://api.openai.com/v1/chat/completions
Creates a model response for the given chat conversation. Learn more in the text generation, vision, and audio guides.

Parameter support can differ depending on the model used to generate the response, particularly for newer reasoning models. Parameters that are only supported for reasoning models are noted below. For the current state of unsupported parameters in reasoning models, refer to the reasoning guide.

Request body
messages
array

Required
A list of messages comprising the conversation so far. Depending on the model you use, different message types (modalities) are supported, like text, images, and audio.
	`
	indexes, err := relDoc.(*LocalRelevantDocuments).IndexingDocument(context.Background(), doc1, "", 5)
	assert.NoError(t, err)
	t.Log(indexes)
	err = relDoc.AddDocWithReference(context.Background(), &RelevantDocWithReference{
		RelevantDoc: RelevantDoc{
			Category: "API",
			Content:  "chat-completions",
			Doc:      doc1,
		},
		References: indexes,
	})
	assert.NoError(t, err)

	doc2 := `
	List fine-tuning events
get
 
https://api.openai.com/v1/fine_tuning/jobs/{fine_tuning_job_id}/events
Get status updates for a fine-tuning job.

Path parameters
fine_tuning_job_id
string

Required
The ID of the fine-tuning job to get events for.

Query parameters
after
string

Optional
Identifier for the last event from the previous pagination request.

limit
integer

Optional
Defaults to 20
Number of events to retrieve.
	`
	indexes, err = relDoc.(*LocalRelevantDocuments).IndexingDocument(context.Background(), doc2, "", 5)
	assert.NoError(t, err)
	t.Log(indexes)
	err = relDoc.AddDocWithReference(context.Background(), &RelevantDocWithReference{
		RelevantDoc: RelevantDoc{
			Category: "API",
			Content:  "List fine-tuning events",
			Doc:      doc2,
		},
		References: indexes,
	})
	assert.NoError(t, err)

	rets, err := relDoc.(RelevantDocumentsFinder).FindWithSimilarity(context.Background(), "chat completions", "API", 2, 0.8)
	assert.NoError(t, err)
	t.Log(rets)

}

// TestIndexing tests the indexing
func TestIndexing(t *testing.T) {
	llmsettings := getllmsettings(t)
	var relDoc RelevantDocumentAdder = NewLocalRelevantDocuments(llmsettings)
	// Testing IndexingDocument
	queries, err := relDoc.(*LocalRelevantDocuments).IndexingDocument(context.Background(), `
測試時段：2025 年 2 月 7 日 ～ 2025 年 2 月 10 日、
　　　　　2025 年 2 月 14 日 ～ 2025 年 2 月 17 日
對應平台：PS5 / Xbox Series X|S / STEAM
 
　　OBT2 中會新增即使是遊玩過 OBT1 的玩家也能享受的新要素，此外還可獲得在正式版中使用的新特典。目前已確定的是，在 OBT2 中除了能夠體驗與 2024 年 11 月舉行的 OBT1 相同內容，還可以狩獵復活魔物 —— 毒怪鳥。

此外，針對曾參與第一回公開測試的玩家，CAPCOM 也強調：「非常感謝各位獵人的踴躍參與。我們開發團隊全體已認真拜讀各位提出的寶貴意見和需求，並正致力於提高正式版的品質，亦因此未能把 OBT1 的改善內容套用於 OBT2 中，還請各位見諒。」
	`, "", 5)
	assert.NoError(t, err)
	t.Log(len(queries))
	for _, query := range queries {
		t.Log(query)
	}
}

// TestSerialize tests the serialization
func TestRelevantDocumentSerialize(t *testing.T) {
	llmsettings := getllmsettings(t)
	var relDoc = NewLocalRelevantDocuments(llmsettings)

	relDoc.AddDoc(context.Background(), &RelevantDoc{
		Category: "t1",
		Content:  "test1",
		Doc:      "test1 document",
	})
	relDoc.AddDocWithReference(context.Background(), &RelevantDocWithReference{
		RelevantDoc: RelevantDoc{
			Category: "t2",
			Content:  "test2",
			Doc:      "test2 document",
		},
		References: []string{"test2", "test-2", "test two", "second test"},
	})

	data, err := relDoc.Serialize()
	assert.NoError(t, err)

	relDoc2, err := DeserializeLocalRelevantDocuemts(data)
	assert.NoError(t, err)

	cates := relDoc2.ListCategories()
	categoryNames := []string{}
	for _, cate := range cates {
		categoryNames = append(categoryNames, cate.Name)
	}
	assert.Contains(t, categoryNames, "t1")
	assert.Contains(t, categoryNames, "t2")
}
