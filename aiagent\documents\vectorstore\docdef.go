package vectorstore

import "encoding/json"

type DocumentType string

const (
	RefType  DocumentType = "document_ref"
	TextType DocumentType = "document_text"
	JSONType DocumentType = "document_json"
)

// WhatTypeOfDocument is a function that returns the type of the document
func WhatTypeOfDocument(meta map[string]string) DocumentType {
	if _, ok := meta["item_id"]; ok {
		return RefType
	}
	if _, ok := meta["text"]; ok {
		return TextType
	}
	return JSONType
}

// This file contains document defines on the chromem store

// DocumentRef is a reference to a document
type DocumentRef struct {
	// ItemID is the ID of the document itme for example: agent_config_network
	ItemID string `json:"item_id"`
	// DocKey is the key of the document for example: actions
	DocKey string `json:"doc_key"`
	// Query is the query to the document it will calculate the embedding of the query and compare it to the embedding of the document
	Query string `json:"query"`
}

type DocumentText struct {
	Text    string `json:"text"`
	Content string `json:"content"`
}

// MapStingString returns a map[string]string representation of the DocumentText
func (d *DocumentText) MapStingString() map[string]string {
	m := make(map[string]string)
	m["text"] = d.Text
	m["content"] = d.Content
	return m
}

// JsonBytes returns the json bytes of the DocumentRef
func (d *DocumentRef) JsonBytes() ([]byte, error) {
	return json.Marshal(d)
}

// MapStringString returns a map[string]string representation of the DocumentRef
func (d *DocumentRef) MapStringString() map[string]string {
	m := make(map[string]string)
	m["item_id"] = d.ItemID
	m["doc_key"] = d.DocKey
	m["query"] = d.Query
	return m
}
