import React, { memo } from "react";
import { Card, Space, Tag, Button, Row, Col } from "antd";
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  SyncOutlined,
} from "@ant-design/icons";
import ProList from "@ant-design/pro-list";
import { useMib<PERSON>rowser } from "../hooks/useMibBrowser";

/**
 * Command Result Item component
 * @param {Object} props - Component props
 * @param {string} props.title - Item title
 * @param {React.ReactNode} props.value - Item value
 * @returns {JSX.Element} Command Result Item component
 */
const CommandResultItem = memo(({ title, value }) => (
  <Row>
    <Col span={8} style={{ fontWeight: "bold" }}>
      {title}
    </Col>
    <Col span={16}>{value}</Col>
  </Row>
));

/**
 * Status tag component based on command status
 * @param {string} status - Command status
 * @returns {JSX.Element} Status tag component
 */
const StatusTag = memo(({ status }) => {
  if (status === "" || status === "running") {
    return (
      <Tag icon={<SyncOutlined spin />} color="processing">
        processing
      </Tag>
    );
  } else if (status === "ok") {
    return (
      <Tag icon={<CheckCircleOutlined />} color="success">
        ok
      </Tag>
    );
  } else {
    return (
      <Tag icon={<CloseCircleOutlined />} color="error">
        {status}
      </Tag>
    );
  }
});

/**
 * Command Results List component
 * @returns {JSX.Element} Command Results List component
 */
const CommandResultsList = () => {
  const { cmdResponse, resultData, setCmdResult } = useMibBrowser();

  return (
    <Card title="Command Results" variant="borderless">
      <ProList
        rowKey="command"
        dataSource={cmdResponse}
        metas={{
          title: {
            dataIndex: "command",
            render: (text) => (
              <div style={{ fontWeight: "bold", wordBreak: "break-all" }}>
                {text}
              </div>
            ),
          },
          description: {
            render: (_, item) => {
              return resultData && resultData.command === item.command ? (
                <Space direction="vertical" style={{ width: "100%" }}>
                  <CommandResultItem
                    title="Command:"
                    value={resultData.command}
                  />
                  <CommandResultItem
                    title="Service Name:"
                    value={resultData.client}
                  />
                  <CommandResultItem
                    title="Status:"
                    value={<StatusTag status={resultData.status} />}
                  />
                  <CommandResultItem
                    title="Result:"
                    value={resultData.result || "No result found"}
                  />
                </Space>
              ) : null;
            },
          },
          extra: {
            render: (_, item) => (
              <Button type="primary" onClick={() => setCmdResult(item)}>
                View Result
              </Button>
            ),
          },
          actions: { cardActionProps: "extra" },
        }}
        pagination={{
          pageSize: 5,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} commands`,
        }}
        split
      />
    </Card>
  );
};

export default memo(CommandResultsList);
