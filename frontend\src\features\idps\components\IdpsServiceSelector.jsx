import { memo, useState } from "react";
import {
  Card,
  Select,
  Space,
  Typography,
  Tag,
  Button,
  Input,
  Modal,
  Flex,
  Row,
  Col,
} from "antd";
import {
  CloudServerOutlined,
  ReloadOutlined,
  ImportOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";

const { Text } = Typography;

/**
 * IDPS Service Selector Component
 * Allows users to select between different IDPS services with import and refresh functionality
 * @param {Object} props Component props
 * @param {Array} props.services Available IDPS services
 * @param {string} props.selectedService Currently selected service
 * @param {Function} props.onSelectService Callback for service selection
 * @param {Function} props.onRefresh Callback for refresh action
 * @param {Function} props.onImportRules Callback for import rules action
 * @param {Object} props.selectedServiceData Data for the selected service
 * @param {boolean} props.loading Loading state
 * @param {boolean} props.isImporting Import loading state
 * @returns {JSX.Element} Service selector component
 */
const IdpsServiceSelector = ({
  services = [],
  selectedService,
  onSelectService,
  onRefresh,
  onImportRules,
  selectedServiceData,
  loading = false,
  isImporting = false,
}) => {
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importUrl, setImportUrl] = useState("");

  const serviceOptions = services.map((service) => ({
    value: service,
    label: (
      <Space>
        <CloudServerOutlined />
        {service}
      </Space>
    ),
  }));

  const handleImportRules = () => {
    if (!importUrl.trim()) {
      return;
    }
    onImportRules(importUrl.trim(), selectedService);
    setImportUrl("");
    setImportModalVisible(false);
  };

  const startTime = selectedServiceData?.start_time;

  return (
    <>
      <Card variant="borderless">
        <Space direction="vertical" size="middle" style={{ width: "100%" }}>
          <Flex justify="space-between" align="center">
            <Text strong>IDPS Service</Text>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={onRefresh}
                loading={loading}
                size="small"
              >
                Refresh
              </Button>
              {selectedService && (
                <Button
                  icon={<ImportOutlined />}
                  onClick={() => setImportModalVisible(true)}
                  loading={isImporting}
                  size="small"
                  type="primary"
                >
                  Import Rules
                </Button>
              )}
            </Space>
          </Flex>

          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={12} md={8}>
              <Select
                style={{ width: "100%" }}
                placeholder="Select IDPS Service"
                value={selectedService}
                onChange={onSelectService}
                options={serviceOptions}
                loading={loading}
                disabled={loading || services.length === 0}
              />
            </Col>

            {selectedService && (
              <Col xs={24} sm={12} md={8}>
                <Tag color="blue" icon={<CloudServerOutlined />}>
                  Active: {selectedService}
                </Tag>
              </Col>
            )}

            {startTime && (
              <Col xs={24} md={8}>
                <Space>
                  <ClockCircleOutlined />
                  <Text type="secondary">
                    Started: {dayjs(startTime).format("YYYY-MM-DD HH:mm:ss")}
                  </Text>
                </Space>
              </Col>
            )}
          </Row>

          {services.length === 0 && !loading && (
            <Text type="secondary">No IDPS services available</Text>
          )}
        </Space>
      </Card>

      <Modal
        title="Import Rules"
        open={importModalVisible}
        onOk={handleImportRules}
        onCancel={() => {
          setImportModalVisible(false);
          setImportUrl("");
        }}
        confirmLoading={isImporting}
        okButtonProps={{
          disabled: !importUrl.trim(),
        }}
      >
        <Space direction="vertical" style={{ width: "100%" }}>
          <Text>
            Import rules for service: <Text strong>{selectedService}</Text>
          </Text>
          <Input
            placeholder="Enter rules URL"
            value={importUrl}
            onChange={(e) => setImportUrl(e.target.value)}
            onPressEnter={handleImportRules}
          />
          <Text type="secondary" style={{ fontSize: "12px" }}>
            Command will be executed: idps rules import {importUrl || "[URL]"}
          </Text>
        </Space>
      </Modal>
    </>
  );
};

export default memo(IdpsServiceSelector);
