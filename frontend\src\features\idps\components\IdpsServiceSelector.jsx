import React, { memo } from "react";
import { Card, Select, Space, Typography, Tag } from "antd";
import {} from "@ant-design/icons";
import { CloudServerOutlined } from "@ant-design/icons";

const { Text } = Typography;

/**
 * IDPS Service Selector Component
 * Allows users to select between different IDPS services
 * @param {Object} props Component props
 * @param {Array} props.services Available IDPS services
 * @param {string} props.selectedService Currently selected service
 * @param {Function} props.onSelectService Callback for service selection
 * @param {boolean} props.loading Loading state
 * @returns {JSX.Element} Service selector component
 */
const IdpsServiceSelector = ({
  services = [],
  selectedService,
  onSelectService,
  loading = false,
}) => {
  const serviceOptions = services.map((service) => ({
    value: service,
    label: (
      <Space>
        <CloudServerOutlined />
        {service}
      </Space>
    ),
  }));

  return (
    <Card variant="borderless">
      <Space direction="vertical" size="small" style={{ width: "100%" }}>
        <Text strong>IDPS Service</Text>
        <Space align="center">
          <Select
            style={{ minWidth: 200 }}
            placeholder="Select IDPS Service"
            value={selectedService}
            onChange={onSelectService}
            options={serviceOptions}
            loading={loading}
            disabled={loading || services.length === 0}
          />
          {selectedService && (
            <Tag color="blue" icon={<CloudServerOutlined />}>
              Active: {selectedService}
            </Tag>
          )}
        </Space>
        {services.length === 0 && !loading && (
          <Text type="secondary">No IDPS services available</Text>
        )}
      </Space>
    </Card>
  );
};

export default memo(IdpsServiceSelector);
