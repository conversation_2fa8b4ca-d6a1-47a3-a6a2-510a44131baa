import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";
import { persist } from "zustand/middleware";

/**
 * @typedef {Object} ChatMessage
 * @property {string} id - Unique message ID
 * @property {string} content - Message content
 * @property {'user'|'assistant'} role - Message sender role
 * @property {Date} timestamp - Message timestamp
 * @property {boolean} isLoading - Whether message is being processed
 * @property {string} [error] - Error message if any
 */

/**
 * @typedef {Object} ChatState
 * @property {ChatMessage[]} messages - Array of chat messages
 * @property {string} inputMessage - Current input message
 * @property {boolean} isLoading - Whether chat is processing
 * @property {boolean} isConnected - Whether chat service is connected
 * @property {string} [error] - Current error message
 * @property {string} selectedModel - Currently selected LLM model
 * @property {string[]} availableModels - List of available LLM models
 */

/**
 * @typedef {Object} ChatActions
 * @property {(message: string) => void} setInputMessage - Set input message
 * @property {(message: ChatMessage) => void} addMessage - Add a message to chat
 * @property {(id: string, content: string) => void} updateMessage - Update a message
 * @property {() => void} clearMessages - Clear all messages
 * @property {(isLoading: boolean) => void} setLoading - Set loading state
 * @property {(isConnected: boolean) => void} setConnected - Set connection state
 * @property {(error: string | null) => void} setError - Set error state
 * @property {() => string} generateMessageId - Generate unique message ID
 * @property {(model: string) => void} setSelectedModel - Set selected LLM model
 * @property {(models: string[]) => void} setAvailableModels - Set available LLM models
 */

/** @type {ChatState} */
const initialState = {
  messages: [
    {
      id: "welcome_msg",
      content:
        "Hello! I'm your AI assistant. I can help you with network management tasks, device commands, and answer questions about your infrastructure. What would you like to know?",
      role: "assistant",
      timestamp: new Date(),
      isLoading: false,
    },
  ],
  inputMessage: "",
  isLoading: false,
  isConnected: false,
  error: null,
  selectedModel: "gpt-4o-mini", // Default model
};

/**
 * Generate a unique message ID
 * @returns {string} Unique ID
 */
const generateId = () =>
  `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

/**
 * Create chat store with initial state and actions
 * @param {Function} set - Zustand set function
 * @returns {ChatState & ChatActions} Store with state and actions
 */
const createChatStore = (set) => ({
  ...initialState,

  setInputMessage: (message) =>
    set((state) => {
      state.inputMessage = message;
    }),

  addMessage: (message) =>
    set((state) => {
      state.messages.push({
        ...message,
        id: message.id || generateId(),
        timestamp: message.timestamp || new Date(),
      });
    }),

  updateMessage: (id, content) =>
    set((state) => {
      const messageIndex = state.messages.findIndex((msg) => msg.id === id);
      if (messageIndex !== -1) {
        state.messages[messageIndex].content = content;
        state.messages[messageIndex].isLoading = false;
      }
    }),

  clearMessages: () =>
    set((state) => {
      state.messages = [];
    }),

  setLoading: (isLoading) =>
    set((state) => {
      state.isLoading = isLoading;
    }),

  setConnected: (isConnected) =>
    set((state) => {
      state.isConnected = isConnected;
    }),

  setError: (error) =>
    set((state) => {
      state.error = error;
    }),

  generateMessageId: () => generateId(),

  setSelectedModel: (model) =>
    set((state) => {
      state.selectedModel = model;
    }),
});

/**
 * Chat store for managing chat state and messages
 * @type {import("zustand").UseBoundStore<ChatState & ChatActions>}
 */
export const useChatStore = create(
  devtools(
    immer(
      persist(
        (...args) => ({
          ...createChatStore(...args),
        }),
        {
          name: "nimbl-chat",
          partialize: (state) => ({
            messages: state.messages,
            selectedModel: state.selectedModel,
          }),
        }
      )
    ),
    {
      name: "ChatStore",
      enabled: process.env.NODE_ENV === "development",
    }
  )
);
