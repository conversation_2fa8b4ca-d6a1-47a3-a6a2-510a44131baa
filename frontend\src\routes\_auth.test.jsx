import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen } from "@testing-library/react";
import { AuthenticatedRouteComponent } from "./_auth";
import { useSettingStore } from "../store/setting-store";
import { useGetRootInfo, useGetSyslogAlert } from "../services/queries";
import { useSocketStore } from "../store/socket-store";
import useWebSocket from "react-use-websocket";

// Mock dependencies
vi.mock("@tanstack/react-router", () => ({
  Outlet: vi.fn(() => <div data-testid="outlet">Outlet Content</div>),
  createFileRoute: vi.fn(() => () => ({})),
  redirect: vi.fn(),
}));

vi.mock("../layout/main-layout", () => ({
  default: vi.fn(({ children }) => (
    <div data-testid="main-layout">
      <div>Main Layout</div>
      {children}
    </div>
  )),
}));

vi.mock("../store/setting-store", () => ({
  useSettingStore: vi.fn(),
}));

vi.mock("../services/queries", () => ({
  useGetRootInfo: vi.fn(),
  useGetSyslogAlert: vi.fn(),
}));

vi.mock("../store/socket-store", () => ({
  useSocketStore: vi.fn(),
}));

vi.mock("react-use-websocket", () => ({
  default: vi.fn(),
  ReadyState: {
    CONNECTING: 0,
    OPEN: 1,
    CLOSING: 2,
    CLOSED: 3,
    UNINSTANTIATED: 4,
  },
}));

describe("AuthenticatedRouteComponent", () => {
  const mockSetEnabledFeatures = vi.fn();
  const mockSetLicenseError = vi.fn();
  const mockSetClearLicenseError = vi.fn();
  const mockAddMessage = vi.fn();
  const mockSetMessages = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock store values
    useSettingStore.mockReturnValue({
      wsURL: "ws://localhost:27182",
    });

    useSocketStore.mockReturnValue({
      setEnabledFeatures: mockSetEnabledFeatures,
      setLicenseError: mockSetLicenseError,
      setClearLicenseError: mockSetClearLicenseError,
      addMessage: mockAddMessage,
      setMessages: mockSetMessages,
      messages: [],
    });

    // Mock query results
    useGetRootInfo.mockReturnValue({
      data: {
        license: {
          enabledFeatures: "feature1,feature2",
        },
      },
    });

    useGetSyslogAlert.mockReturnValue({
      data: [
        {
          kind: "alert",
          message: "Test alert",
          timestamp: "2023-01-01T00:00:00Z",
        },
      ],
    });

    // Mock WebSocket
    useWebSocket.mockReturnValue({
      lastMessage: null,
      readyState: 1, // OPEN
    });
  });

  it("renders the main layout with outlet", () => {
    render(<AuthenticatedRouteComponent />);

    // Check if the main layout is rendered
    expect(screen.getByTestId("main-layout")).toBeInTheDocument();
    expect(screen.getByText("Main Layout")).toBeInTheDocument();

    // Check if the outlet is rendered
    expect(screen.getByTestId("outlet")).toBeInTheDocument();
  });

  it("initializes WebSocket with correct URL", () => {
    render(<AuthenticatedRouteComponent />);

    // Check if WebSocket was initialized with the correct URL
    expect(useWebSocket).toHaveBeenCalledWith(
      "ws://localhost:27182/api/v1/ws",
      expect.objectContaining({
        shouldReconnect: expect.any(Function),
        share: true,
      })
    );
  });

  it("processes root info and sets enabled features", () => {
    render(<AuthenticatedRouteComponent />);

    // Check if enabled features were set from root info
    expect(mockSetEnabledFeatures).toHaveBeenCalledWith([
      "feature1",
      "feature2",
    ]);
  });

  it("processes syslog alerts and sets messages", () => {
    render(<AuthenticatedRouteComponent />);

    // Check if messages were set from syslog alerts
    expect(mockSetMessages).toHaveBeenCalledWith(
      expect.arrayContaining([
        expect.objectContaining({
          title: "alert",
          message: "Test alert",
        }),
      ])
    );
  });
});
