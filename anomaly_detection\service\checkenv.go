package service

import (
	"mnms/anomaly_detection/utils"
)

// IsOpenAIKeyWork checks if the OpenAI key is working
func IsOpenAIKeyWork() bool {
	// make prompt
	prompt := []utils.CompletionMessage{
		{
			Role:    "user",
			Content: "hello OpenAI",
		},
	}
	//please determine if it is normal or an anomaly based on the criteria provided.
	//Consider a message to be an anomaly if it indicates unexpected system behavior,
	// errors, or security concerns. Normal messages are routine logs that do not suggest any issues.

	_, err := utils.CompletionRequest(prompt)
	if err != nil {
		return false
	}
	return true

}
