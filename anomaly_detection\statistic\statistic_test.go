package statistic

import (
	"testing"

	"github.com/mitchellh/mapstructure"
)

// TestMapToStruct tests the MapToStruct function
func TestMapToStruct(t *testing.T) {
	// Test the MapToStruct function
	m := map[string]interface{}{"a": 1, "b": 2, "c": "three"}
	s := struct {
		A int
		B int
		C string
		D bool
		E string
	}{
		A: 0,
		D: true,
		E: "E",
	}

	err := mapstructure.Decode(m, &s)
	if err != nil {
		t.Error("MapToStruct failed")
	}
	if s.A != 1 || s.B != 2 || s.C != "three" || s.D != true || s.E != "E" {
		t.Error("MapToStruct failed")
	}
	// testing statistics.Settings
	m = map[string]interface{}{
		"db_settings": map[string]interface{}{
			"host": "localhost",
		},
		"last_error": "error",
		"detect":     false,
		"not_in_s":   "not_in_s",
	}
	s1 := Settings{

		LastError:        "dummyerror",
		Detect:           true,
		Root:             "dummyroot",
		Token:            "dummytoken",
		PullIntervalMins: 5,
	}
	err = mapstructure.Decode(m, &s1)
	if err != nil {
		t.Error("MapToStruct failed")
	}

	if s1.Root != "dummyroot" || s1.Token != "dummytoken" || s1.PullIntervalMins != 5 {
		t.Error("MapToStruct failed")
	}

}
