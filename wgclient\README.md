# WG Client Guide

wgclient is a tool to join mnms wireguard network automatically.
Given config file and command can operate wiregaurd.

## Usage

### Flag

- config : specific wgclient config file

#### Config file

- required:
    - root_url : the url of mnms
    - root_name : the name of the mnms root
    - client_name : the name of the mnms network service (client) you want to connect to
- optional
    - name : a name for nms to identify this client, default name is `wgclient`
    - interface : the wireguard interface name, default interface name is `wg0`
    - address : wireguard interface address, if not specified, wgclient will automatically generate one
    - dns : wgclient interface dns, should be an array
    - endpoints : wireguard server, if not specified, wgclient will copy from the client
    - persistent_keepalive : wg network keep alive, if not specified, wgclient will copy from the client
    - pre_up : wgclient runs Wireguard preup, should be an array
    - post_up : wgclient runs Wireguard postup, should be an array
    - pre_down : wgclient runs Wireguard predown, should be an array
    - post_down : wgclient runs Wireguard postdown, should be an array


For example:
```json
{
    "name": "wgclient",
    "interface": "wgclient-wg0",
    "root_url": "http://***************:27182",
    "root_name": "root",
    "client_name": "clientABC",
}
```

Note that the optional configuration will be auto generated by default as the client. If any of them is set, the correspond configuration will be overwritten.


### Command

- start : start wireguard with user and pass from mnms config
- stop : stop wireguard from mnms config

### Example

```shell
# Run Wireguard and auto join mnms wg
wgclient -config wgclient.json start
# Stop WireGurad
wgclient -config wgclient.json stop
# Stop WireGuard and Remove current WireGuard configuration file
wgclient -config wgclient.json clean
```