package actions

import (
	_ "embed"
	"testing"
)

//go:embed data/actions.json
var testpredefinedActionsData []byte

func TestReadPredefinedActions(t *testing.T) {

	actions, err := DeserializeActionDefineds(testpredefinedActionsData)
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	t.Log("Total actions: ", len(actions.Actions))

	// try to find
	foundActions, err := actions.FindAction("gwd")
	if err != nil {
		t.<PERSON>al(err)
	}
	t.Log("gwd Actions # ", len(foundActions))
	for _, action := range foundActions {
		t.Log(action.String())
	}
}
