package project

import (
	"context"
	"encoding/json"
	"fmt"
	"mnms/aiagent/documents"
	"mnms/aiagent/flow"
	"mnms/aiagent/node"
	"mnms/aiagent/session"
	"mnms/llm"
	"mnms/llm/envconfig"
	"mnms/llm/history"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/qeof/q"
	"go.uber.org/zap"
)

// projectsFolder
const projectsFolder = "projects"

// projectFileWriteMutex
var projectFileWriteMutex = &sync.Mutex{}

//

// ExportProject writes a project to a file
func ExportProject(project *Project) error {
	projectFileWriteMutex.Lock()
	defer projectFileWriteMutex.Unlock()
	if len(project.Name) == 0 {
		return fmt.Errorf("project name is empty")
	}
	filename := project.Name + ".json"
	data, err := json.Marshal(project)
	if err != nil {
		return fmt.Errorf("failed to marshal project %s : %w", project.Name, err)
	}
	// make sure the projects folder exists
	err = os.Mkdir<PERSON>ll(projectsFolder, 0755)
	if err != nil {
		return fmt.Errorf("failed to create projects folder: %w", err)
	}

	filepath := filepath.Join(projectsFolder, filename)
	err = os.WriteFile(filepath, data, 0644)
	if err != nil {
		return fmt.Errorf("failed to write project %s : %w", project.Name, err)
	}
	return nil
}

// ImportDefaultProject reads a default project from a file
func ImportDefaultProject(llmvendor string) (*Project, error) {
	model, err := envconfig.LLMModel()
	if err != nil {
		return nil, fmt.Errorf("failed to get LLM model: %w, please set NIMBL_LLM_MODEL environment variable", err)
	}
	fmt.Fprintf(os.Stderr, "Initialilzing AI assistant with model %s ...\n", model)
	defaultProject := `
	{
  "env": {
    "llm_settings": {},
    "number_relevant_docs": 5,
    "relevant_similarity": 0.75
  },
  "name": "aiagent",
  "description": "NIMBL aiagent",
  "last_modified": "2025-03-07T02:38:57.925Z",
  "author": "<EMAIL>",
  "flows": [
    {
      "name": "default",
      "description": "default flow",
      "nodes": [
        {
          "name": "get-devinfo",
          "description": "Get nimbl device info",
          "output_format": "text",
          "system_prompt": "You are a system device assistant that configures devices or retrieves and analyzes device information based on user input.\n\n- Device with MAC 11-22-33-44-55-66 is fake device, ignore this device",
          "json_schema": {},
          "relevant_doc_categories": [],
          "number_relevant_docs": 5,
          "relevant_similarity": 0.75,
          "functions": [],
          "mcp_servers": [
            "nimbl"
          ],
          "id": "1",
          "type": "",
          "data": {
           
          },

          "mix_output": "output"

        }
      ],
      "edges": []
    },
    {
      "name": "cmd",
      "description": "",
      "nodes": [
        {
          "name": "cmd-processing",
          "description": "Processing Nimbl command related prompt",
          "output_format": "text",
          "system_prompt": "You are a system command assistant; your job is Referring relevant document to:\n- Answer user's question\n- Correct or complete command\n- Clarify user's intend",
          "json_schema": {},
          "relevant_doc_categories": [],
          "number_relevant_docs": 5,
          "relevant_similarity": 0.75,
          "functions": null,
          "mcp_servers": [
            "nimbl"
          ],
          "id": "1",
          "type": "",
          "data": {
            "label": "cmd-processing"
          },
          "mix_output": "output"
        }
      ],
      "edges": []
    }
  ]
}
	`
	proj := &Project{}
	err = json.Unmarshal([]byte(defaultProject), proj)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal default project: %w", err)
	}
	llmSettings, err := llm.CreateLargeLanguageModelWithModel(llmvendor, model)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize LLM: %w", err)
	}
	llmClient, err := llm.NewLLMClient(llmSettings)
	if err != nil {
		return nil, fmt.Errorf("failed to create LLM client: %w", err)
	}
	// valid llm connection
	err = llm.ValidateLLMConnection(llmClient)
	if err != nil {
		return nil, fmt.Errorf("failed to connect %s: %w", llmvendor, err)
	}
	proj.Env.LLMSettings = llmSettings

	return proj, nil
}

// ImportProject reads a project from a file
func ImportProject(filename string) (*Project, error) {
	// if filename is not json file, append .json
	if filepath.Ext(filename) != ".json" {
		filename += ".json"
	}
	// read project from file
	filepath := filepath.Join(projectsFolder, filename)
	data, err := os.ReadFile(filepath)
	if err != nil {
		return nil, fmt.Errorf("failed to read project %s : %w", filename, err)
	}
	// unmarshal project
	project := &Project{}
	err = json.Unmarshal(data, project)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal project %s : %w", filename, err)
	}
	err = project.Validate()
	if err != nil {
		return nil, fmt.Errorf("failed to validate project %s : %w", filename, err)
	}
	return project, nil
}

// Project a AI agent project structure
type Project struct {
	Env          *ProjectEnvironment `json:"env"`
	Meta         map[string]any      `json:"meta,omitempty"`
	Name         string              `json:"name"`
	Description  string              `json:"description,omitempty"`
	LastModified string              `json:"last_modified,omitempty"`
	Author       string              `json:"author,omitempty"`
	Flows        []*flow.Flow        `json:"flows"`
}

// DefaultEmptyProject creates a default empty project
func DefaultEmptyProject(llmkind string) *Project {
	var llmSettings *llm.LargeLanguageModel
	switch llmkind {
	case "open-ai":
		llmSettings = llm.GPTSettings()
	case "ollama":
		llmSettings = llm.OllamaSettings("")
	default:
		llmSettings = &llm.LargeLanguageModel{}
	}

	return &Project{
		Env: &ProjectEnvironment{
			LLMSettings:              llmSettings, // Assuming no initial settings for LLM
			RelevantDocumentProvider: nil,
			NumberRelevantDocs:       0,
			RelevantSimilarity:       0.0,
			m:                        nil,
		},
		Meta:         make(map[string]any),            // Empty metadata
		Name:         "Untitled Project",              // Default project name
		Description:  "A new AI agent project",        // Default description
		LastModified: time.Now().Format(time.RFC3339), // Current timestamp
		Author:       "Unknown",                       // Default author

		Flows: []*flow.Flow{}, // Empty flow
	}
}

// NewProject creates a new project object
func NewProject(llmsetting *llm.LargeLanguageModel, name, descr string) (*Project, error) {
	err := llmsetting.Valid()
	if err != nil {
		return nil, fmt.Errorf("invalid LLM settings: %w", err)
	}
	relevant := documents.NewLocalRelevantDocuments(llmsetting)

	llmClient, err := llm.NewLLMClient(llmsetting)
	if err != nil {
		return nil, fmt.Errorf("failed to create LLM client: %w", err)
	}
	env := &ProjectEnvironment{
		LLMSettings:              llmsetting,
		RelevantDocumentProvider: relevant,
		//defualt values
		NumberRelevantDocs: 5,
		RelevantSimilarity: 0.75,
		m:                  llmClient,
	}
	return &Project{
		Env:          env,
		Name:         name,
		Description:  descr,
		LastModified: time.Now().Format(time.RFC3339),

		Flows: []*flow.Flow{},
	}, nil
}

// UpsertFlow adds a flow to the project
func (p *Project) UpsertFlow(flow *flow.Flow) {
	for i, f := range p.Flows {
		if f.Name == flow.Name {
			p.Flows[i] = flow
			return
		}
	}

	p.Flows = append(p.Flows, flow)
}

// RemoveFlow removes a flow from the project
func (p *Project) RemoveFlow(flowName string) {
	for i, f := range p.Flows {
		if f.Name == flowName {
			p.Flows = append(p.Flows[:i], p.Flows[i+1:]...)
			return
		}
	}
}

// Validate checks if the project is valid
func (p *Project) Validate() error {
	if p.Env == nil {
		return fmt.Errorf("project environment is not set")
	}

	if p.Env.LLMSettings == nil {
		return fmt.Errorf("LLM settings are not set")
	}

	// check
	if p.Env.RelevantDocumentProvider == nil {
		p.Env.RelevantDocumentProvider = documents.NewLocalRelevantDocuments(p.Env.LLMSettings)
	}

	return nil
}

// CheckLLM checks if the LLM settings are valid
func (p *Project) CheckLLM() (string, error) {
	if p.Env == nil || p.Env.LLMSettings == nil {
		return "", fmt.Errorf("LLM settings are not set")
	}
	if p.Env.LLMSettings == nil {
		return "", fmt.Errorf("LLM settings are not set")

	}
	client, err := llm.NewLLMClient(p.Env.LLMSettings)
	if err != nil {
		return "", fmt.Errorf("failed to create LLM client: %w", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	ret, err := client.Complete(ctx, []llm.Message{
		{Role: "user", Content: "who are you?"},
	})
	q.Q(ret)

	return ret, err
}

// GetLLMCompleter returns the LLM client
func (p *Project) GetLLMCompleter() (llm.Completer, error) {
	if p.Env.m == nil {
		client, err := llm.NewLLMClient(p.Env.LLMSettings)
		if err != nil {
			return nil, err
		}
		p.Env.m = client
	}
	return p.Env.m, nil
}

// GetRelevantDocument returns the relevant document for a Flow
func (p *Project) GetRelevantDocumentProvider() documents.RelevantDocumentProvider {
	return p.Env.RelevantDocumentProvider
}

// GetFlow returns a flow from the project
func (p *Project) GetFlow(name string) (*flow.Flow, error) {
	for _, flow := range p.Flows {
		if flow.Name == name {
			return flow, nil
		}
	}
	return nil, fmt.Errorf("flow %s not found", name)
}

// GetNodeRelevantDocuments returns the relevant documents for a node
func (p *Project) GetNodeRelevantDocuments(ctx context.Context, query string, nodeProvider node.NodeProvider) ([]string, error) {
	if nodeProvider == nil {
		return []string{}, fmt.Errorf("node is null")
	}

	rd := nodeProvider.GetRelevantDocumentInfo()
	info := nodeProvider.Info()
	if len(rd.RelevantDocCategories) == 0 {
		return []string{}, fmt.Errorf("node %s does not have relevant documents.", info.Name)
	}

	nResults := p.Env.NumberRelevantDocs
	similarity := p.Env.RelevantSimilarity
	// if node has a custom number of relevant docs override the default
	if rd.NumberRelevantDocs != 0 {
		nResults = rd.NumberRelevantDocs
	}
	// if node has a custom similarity override the default
	if rd.RelevantSimilarity != 0 {
		similarity = rd.RelevantSimilarity
	}
	q.Q("GetNodeRelevantDocuments", rd.RelevantDocCategories, nResults, similarity)
	var results []string
	for _, docCategory := range rd.RelevantDocCategories {
		q.Q("GetNodeRelevantDocuments", docCategory)
		if p.Env.RelevantDocumentProvider == nil {
			q.Q("RelevantDocumentProvider is nil")
			return []string{}, fmt.Errorf("relevant document provider is nil")
		}
		docs, err := p.Env.RelevantDocumentProvider.FindWithSimilarity(ctx, query, docCategory, nResults, similarity)
		if err != nil {
			q.Q("Can't find relevant documents for ", docCategory, " node:", info.Name)
			continue
		}
		q.Q("GetNodeRelevantDocuments", docs)
		results = append(results, docs...)
	}
	return results, nil
}

type FlowStep struct {
	Input       string             `json:"input"`
	Output      node.RunNodeOutput `json:"output"`
	RelevantDoc string             `json:"relevant_doc"`
	NodeName    string             `json:"node_name"`
	NodeID      string             `json:"node_id"`
	ElapsedTime string             `json:"elapsed_time"`
}

// RunFlowResult a result of running a flow
type RunFlowResult struct {
	Steps   []FlowStep `json:"steps"`
	Error   string     `json:"error"`
	Result  string     `json:"result"`
	History string     `json:"history"`
}

// AddStep adds a step to the flow result
func (r *RunFlowResult) AddStep(step FlowStep) {
	r.Steps = append(r.Steps, step)
	r.History += "\n" + step.NodeName + ":\n" + step.Output.String() + "\n"
	r.Result = step.Output.String()
}

// AddQuery adds a query to the flow result
func (r *RunFlowResult) AddQuery(query string) {
	r.History += "\nQuery:\n" + query
}

// RunStreamFlow runs a flow and calls stepCallback for each step.
// It streams the processing steps as they are generated.
func (p *Project) RunStreamFlow(ctx context.Context, flowName, nodeID, input string, stepCallback func(step FlowStep)) (*RunFlowResult, error) {
	f, err := p.GetFlow(flowName)
	if err != nil {
		return nil, err
	}

	if len(f.Nodes) == 0 {
		return nil, fmt.Errorf("flow %s has no nodes", flowName)
	}

	result := &RunFlowResult{
		Steps: []FlowStep{},
	}

	// Get the first node
	var runningNode node.NodeProvider
	if nodeID == "" {
		headNodes := f.HeadNodes()
		if len(headNodes) == 0 {
			return nil, fmt.Errorf("flow %s has no start node", flowName)
		}
		runningNode = headNodes[0]
	} else {
		node, err := f.GetNodeWithID(nodeID)
		if err != nil {
			return nil, err
		}
		runningNode = node
	}

	// Copy input to query
	query := input
	stepInput := input
	// Loop until there is no next node.
	for runningNode != nil {
		zap.L().Debug("RunStreamFlow", zap.String("query", stepInput))
		start := time.Now()
		var err error
		runNodeResult, err := p.RunNode(ctx, runningNode, stepInput)
		if err != nil {
			result.Error = err.Error()
			return result, nil
		}
		zap.L().Debug("RunStreamFlow", zap.Any("output", runNodeResult))
		info := runningNode.Info()
		// Build the step
		step := FlowStep{
			Input:       runNodeResult.Input,
			Output:      runNodeResult.Output,
			RelevantDoc: runNodeResult.RelevantDoc,
			NodeName:    info.Name,
			NodeID:      info.ID,
			ElapsedTime: time.Since(start).String(),
		}
		// Append the step to the result and call the callback.
		result.AddStep(step)
		stepCallback(step)

		// Run functions
		fns := runningNode.RunFunctions()

		var funcsOutput = runNodeResult.Output
		funcsInput := runNodeResult.Output
		for _, f := range fns {
			fmt.Printf("RunStreamFlow function: %s len(%d)\n", f, len(fns))
			start = time.Now()
			funcsOutput, err = node.RunFunction(f, funcsInput, query)
			if err != nil {
				result.Error = err.Error()
				return result, nil
			}

			step := FlowStep{
				Input:       funcsInput.String(),
				Output:      funcsOutput,
				RelevantDoc: "",
				NodeName:    f,
				NodeID:      "function",
				ElapsedTime: time.Since(start).String(),
			}
			// Append the step to the result and call the callback.
			result.AddStep(step)
			stepCallback(step)
			if funcsOutput.FlowControl == "break" {
				break
			}

			funcsInput = funcsOutput
		}
		stepOut := runningNode.Output(stepInput, funcsOutput)
		result.Result = stepOut.String()

		runNodeResult.Output = stepOut
		// genrate output
		stepInput = runNodeResult.Output.String()
		nextNodes := f.NextNodes(info.ID)
		if len(nextNodes) == 0 {
			break
		}
		if len(nextNodes) > 1 {
			return nil, fmt.Errorf("flow %s does not support branching", flowName)
		}
		runningNode = nextNodes[0]
	}

	return result, nil
}

// RunStreamFlowWithMCP runs a flow and calls stepCallback for each step.
// It streams the processing steps as they are generated.
func (p *Project) RunStreamFlowWithMCP(
	ctx context.Context,
	currentSession *session.AIAgentSession,
	input string,
	messages *[]history.HistoryMessage,
	autoMode bool,
	callback func(kind, content string),
) (string, error) {
	flowName := currentSession.Meta.Flow
	nodeID := currentSession.Meta.NodeID
	// Get the flow by name and validate it exists
	f, err := p.GetFlow(flowName)
	if err != nil {
		return "", err
	}

	// Validate flow has nodes
	if len(f.Nodes) == 0 {
		return "", fmt.Errorf("flow %s has no nodes", flowName)
	}

	// Determine the starting node - either specified by ID or first head node
	var runningNode node.NodeProvider
	if nodeID == "" {
		headNodes := f.HeadNodes()
		if len(headNodes) == 0 {
			return "", fmt.Errorf("flow %s has no start node", flowName)
		}
		runningNode = headNodes[0]
	} else {
		node, err := f.GetNodeWithID(nodeID)
		if err != nil {
			return "", err
		}
		runningNode = node
	}

	// Get LLM provider for processing
	provider, err := p.Env.GetLLMProvider()
	if err != nil {
		return "", err
	}

	// Log the starting message count
	q.Q("RunStreamFlowWithMCP starting", flowName, input, "message_count:", len(*messages))

	// Initialize variables for the processing loop
	stepInput := input
	var result string

	// Main processing loop - continues until no more nodes to process
	for runningNode != nil {
		currentSession.SetNodeID(runningNode.Info().ID)
		// Notify callback about current node being processed
		callback("info", fmt.Sprintf("Running node: %s\n", runningNode.Info().Name))
		q.Q("RunStreamFlowWithMCP", stepInput)

		// Get relevant documents for the current node
		var err error
		var relevantDocs = ""
		q.Q("RAG", input, runningNode.Info().Name)
		ragList, err := p.GetNodeRelevantDocuments(ctx, input, runningNode)
		q.Q("RAG result", ragList)
		if err == nil {
			callback("rag", fmt.Sprintf("Found %d relevant documents\n", len(ragList)))
			if len(ragList) > 0 {
				firstDoc := ragList[0]
				if len(firstDoc) > 15 {
					firstDoc = firstDoc[:15]
				}
				callback("rag", fmt.Sprintf("Most relevant document:\n%s...\n", firstDoc))
				relevantDocs = "## Relevant Documents: \n" + strings.Join(ragList, "\n")
			}
		}

		// Run the current node with the input and relevant documents
		runNodeResult, err := runningNode.RunPrompt(ctx, provider, relevantDocs, input, messages, callback, autoMode)
		if err != nil {
			return "", err
		}

		q.Q("RunStreamFlow", runNodeResult)

		info := runningNode.Info()
		callback("info", fmt.Sprintf("Node %s response:\n%s\n", info.Name, runNodeResult))

		// Prepare for next node in the flow
		stepInput = runNodeResult
		result = runNodeResult
		nextNodes := f.NextNodes(info.ID)
		if len(nextNodes) == 0 {
			break
		}
		if len(nextNodes) > 1 {
			return "", fmt.Errorf("flow %s does not support branching", flowName)
		}
		runningNode = nextNodes[0]
		callback("info", fmt.Sprintf("%s is thinking...\n", provider.Name()))
	}

	if len(currentSession.Permissions) == 0 {
		session.ClearToolCallMessages(currentSession.ID)
	}
	// Log completion of flow processing
	q.Q("RunStreamFlowWithMCP completed", flowName, "message_count:", len(*messages))

	return result, nil
}

// RunNodeResult
type RunNodeResult struct {
	Input       string             `json:"input"`
	Output      node.RunNodeOutput `json:"output"`
	RelevantDoc string             `json:"relevant_doc"`
}

// RunNode testing run a input Node
func (p *Project) RunNode(ctx context.Context, nodeProvider node.NodeProvider, query string) (*RunNodeResult, error) {
	if nodeProvider == nil {
		return nil, fmt.Errorf("node is null")
	}

	relevantDoc := ""
	relevantDocs, err := p.GetNodeRelevantDocuments(ctx, query, nodeProvider)
	if err == nil {
		relevantDoc = "## Relevant Documents: \n" + strings.Join(relevantDocs, "\n")
	}

	llmClient, err := p.GetLLMCompleter()
	if err != nil {
		return nil, err
	}

	ret, err := nodeProvider.Run(ctx, llmClient, relevantDoc, query)
	if err != nil {
		return nil, err
	}

	return &RunNodeResult{
		Input:       query,
		Output:      *ret,
		RelevantDoc: relevantDoc,
	}, nil
}

// RunNodeWithNodeName runs a node in the Flow
func (p *Project) RunNodeWithNodeName(ctx context.Context, flowName, nodeName string, query string) (*node.RunNodeOutput, error) {

	f, err := p.GetFlow(flowName)
	if err != nil {
		return nil, err
	}

	n, err := f.GetNodeWithName(nodeName)
	if err != nil {
		return nil, err
	}

	relevantDoc := ""
	relevantDocs, err := p.GetNodeRelevantDocuments(ctx, query, n)
	if err == nil {
		relevantDoc = "## Relevant Documents: \n" + strings.Join(relevantDocs, "\n")
	}

	llmClient, err := p.GetLLMCompleter()
	if err != nil {
		return nil, err
	}

	return n.Run(ctx, llmClient, relevantDoc, query)

}

// RunNodeWithRelevantDocs runs a node in the Flow with relevant documents
func (p *Project) RunNodeWithRelevantDocs(ctx context.Context, flowName, nodeName string, query string, relevantDocs string) (*node.RunNodeOutput, error) {
	f, err := p.GetFlow(flowName)
	if err != nil {
		return nil, err
	}

	n, err := f.GetNodeWithName(nodeName)
	if err != nil {
		return nil, err
	}

	llmClient, err := p.GetLLMCompleter()
	if err != nil {
		return nil, err
	}

	return n.Run(ctx, llmClient, relevantDocs, query)
}

// ProjectSummary a summary of a project
type ProjectSummary struct {
	Name         string `json:"name"`
	Description  string `json:"description"`
	Auhor        string `json:"author"`
	LastModified string `json:"last_modified"`
}

// Summary
func (p *Project) Summary() ProjectSummary {
	return ProjectSummary{
		Name:         p.Name,
		Description:  p.Description,
		Auhor:        p.Author,
		LastModified: p.LastModified,
	}
}

// GetSavedProjects lists all the projects
func GetSavedProjects() ([]*Project, error) {
	// go through all the files in the projects folder
	files, err := os.ReadDir(projectsFolder)
	if err != nil {
		return nil, fmt.Errorf("failed to read projects folder: %w", err)
	}

	if len(files) == 0 {
		return []*Project{}, nil
	}

	var projects []*Project
	for _, file := range files {
		if file.IsDir() {
			continue
		}
		project, err := ImportProject(file.Name())
		if err != nil {
			zap.L().Error("failed to import project", zap.String("project", file.Name()), zap.Error(err))
			continue
		}
		projects = append(projects, project)
	}
	return projects, nil
}

// ProjectExists checks if a project exists
func ProjectExists(p *Project) bool {
	// go through all the files in the projects folder
	files, err := os.ReadDir(projectsFolder)
	if err != nil {
		q.Q(err)
		return false
	}
	for _, file := range files {
		// project name is file name without extension
		if strings.TrimSuffix(file.Name(), filepath.Ext(file.Name())) == p.Name {
			return true
		}
	}
	return false
}

// ResumeSession resumes a chat session
func (p *Project) ResumeSession(ctx context.Context, sessionID string, autoMode bool, callback func(kind, content string)) (string, error) {
	// Get the session
	s, err := session.GetAIAgentSession(sessionID)
	if err != nil {
		return "", fmt.Errorf("failed to get session: %w", err)
	}

	// Get the flow by name and validate it exists
	f, err := p.GetFlow(s.Meta.Flow)
	if err != nil {
		return "", err
	}

	// Validate flow has nodes
	if len(f.Nodes) == 0 {
		return "", fmt.Errorf("flow %s has no nodes", s.Meta.Flow)
	}

	// Determine the starting node - either specified by ID or first head node
	var runningNode node.NodeProvider
	if s.Meta.NodeID == "" {
		headNodes := f.HeadNodes()
		if len(headNodes) == 0 {
			return "", fmt.Errorf("flow %s has no start node", s.Meta.Flow)
		}
		runningNode = headNodes[0]
	} else {
		node, err := f.GetNodeWithID(s.Meta.NodeID)
		if err != nil {
			return "", err
		}
		runningNode = node
	}

	// Get LLM provider for processing
	provider, err := p.Env.GetLLMProvider()
	if err != nil {
		return "", err
	}

	// Log the starting message count
	q.Q("ResumeSession starting", s.Meta.Flow, "message_count:", len(*s.Messages))

	// Initialize variables for the processing loop
	var result string
	stepInput := ""
	// Main processing loop - continues until no more nodes to process
	for runningNode != nil {
		s.SetNodeID(runningNode.Info().ID)
		messages := s.Messages
		rejectToolCalls := s.GerRejectToolCalls()
		approvedToolCalls := s.GetApprovedToolCalls()
		q.Q("resume session tool calls", "rejectToolCalls", len(rejectToolCalls), "approvedToolCalls", len(approvedToolCalls))

		if len(rejectToolCalls)+len(approvedToolCalls) > 0 {
			callback("info", fmt.Sprintf("Processing %d tool calls... ", len(approvedToolCalls)))
			toolUseContent, toolResults := runningNode.ProcessToolCalls(ctx, approvedToolCalls, rejectToolCalls, callback)
			if len(toolUseContent) != len(toolResults) {
				q.Q("resume session has issue, toolUseContent not equal toolResults", len(toolUseContent), len(toolResults))
				callback("error", fmt.Sprintf("tool use content and tool results length mismatch: %d != %d", len(toolUseContent), len(toolResults)))
				return "", fmt.Errorf("tool use content and tool results length mismatch: %d != %d", len(toolUseContent), len(toolResults))
			}
			s.ClearPermissions()
			q.Q("resume session tool len(toolResults)", len(toolResults))
			if len(toolResults) > 0 {
				var messageContent []history.ContentBlock
				messageContent = append(messageContent, toolUseContent...)
				// Add assistant's message to history
				*messages = append(*messages, history.HistoryMessage{
					Role:    "assistant",
					Content: messageContent,
				})
				*messages = append(*messages, history.HistoryMessage{
					Role:    "user",
					Content: toolResults,
				})
			}
		}
		// Notify callback about current node being processed
		callback("info", fmt.Sprintf("Running node: %s\n", runningNode.Info().Name))
		q.Q("ResumeSession", runningNode.Info().Name)

		// Get relevant documents for the current node
		var err error
		q.Q("ResumeSession messages", messages)
		session.UpdateChatSession(sessionID, s)
		// Run the current node with the input and relevant documents
		runNodeResult, err := runningNode.RunPrompt(ctx, provider, "", stepInput, s.Messages, callback, autoMode)
		if err != nil {
			return "", err
		}

		q.Q("ResumeSession", runNodeResult)

		info := runningNode.Info()
		callback("info", fmt.Sprintf("Node %s response:\n%s\n", info.Name, runNodeResult))
		stepInput = runNodeResult
		// Prepare for next node in the flow
		result = runNodeResult
		nextNodes := f.NextNodes(info.ID)
		if len(nextNodes) == 0 {
			break
		}
		if len(nextNodes) > 1 {
			return "", fmt.Errorf("flow %s does not support branching", s.Meta.Flow)
		}
		runningNode = nextNodes[0]
		callback("info", fmt.Sprintf("%s is thinking...\n", provider.Name()))
	}
	if len(s.Permissions) == 0 {
		session.ClearToolCallMessages(sessionID)
	}
	// Log completion of flow processing
	q.Q("ResumeSession completed", s.Meta.Flow, "message_count:", len(*s.Messages))

	return result, nil
}
