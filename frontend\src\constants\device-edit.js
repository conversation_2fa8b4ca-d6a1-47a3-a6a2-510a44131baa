export const IP_REGEX =
  /^((?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){1}$/;

export const SPACE_CHECK = /^\S*$/;

export const FORM_FIELDS = {
  MODEL_NAME: "modelname",
  HOST_NAME: "hostname",
  NETMASK: "netmask",
};

export const VALIDATION_RULES = {
  [FORM_FIELDS.MODEL_NAME]: [
    { required: true, message: "Please input the model name!" },
    { pattern: SPACE_CHECK, message: "space not allowed!" },
  ],
  [FORM_FIELDS.HOST_NAME]: [
    { required: true, message: "Please input the host name!" },
    { pattern: SPACE_CHECK, message: "space not allowed!" },
  ],
  [FORM_FIELDS.NETMASK]: [
    { required: true, message: "Please input the netmask!" },
    { pattern: IP_REGEX, message: "input should be ip address!" },
  ],
};
