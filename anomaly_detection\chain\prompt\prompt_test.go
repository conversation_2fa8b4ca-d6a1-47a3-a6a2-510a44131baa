package prompt

import (
	"testing"
)

// TestCheckPromptFile tests the CheckPromptFile function
func TestCheckPromptFile(t *testing.T) {
	prompt, err := CheckPromptFile("prompt.yaml")
	if err != nil {
		t.Fatalf("Failed to check prompt file: %v", err)
	}
	t.Logf("Prompt: %v", prompt)
}

// TestAddAnomalies tests the AddAnomalies function
func TestAddAnomalies(t *testing.T) {
	prompt, err := CheckPromptFile("prompt.yaml")
	if err != nil {
		t.Fatalf("Failed to check prompt file: %v", err)
	}
	normalMessages := []string{
		"<5>May 15 15:23:05 jilllocal InsertDev: new device: 00-60-E9-2E-BE-E0",
		"<5>May 15 15:40:47 root main: exiting main() root",
		"<134>Jul 17 06:13:37 combo ftpd[23575]: connection from 83.116.207.11 (aml-sfh-3310b.adsl.wanadoo.nl) at Sun Jul 17 06:13:37 2005",
	}
	anomalies := []string{
		"<5>May 15 14:25:03 jilllocal main: ERROR: Failed to write to /var/log/system.log",
	}
	prompt.AddNormalMessages(normalMessages)
	prompt.AddAnomalies(anomalies)
	t.Logf("%v", prompt.SystemPrompt)
}
