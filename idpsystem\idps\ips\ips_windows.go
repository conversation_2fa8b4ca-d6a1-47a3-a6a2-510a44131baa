//go:build windows
// +build windows

package ips

import (
	"mnms/idpsystem/idps/detect"

	"github.com/google/gonids"
	"github.com/yanlinLiu0424/godivert/windivert"
)

var defaultfilter = "!loopback && (ip || ipv6)"
var lofilter = "loopback && (ip || ipv6)"

func NewIps() (Ipser, error) {
	w := &WIps{filter: defaultfilter}
	d, err := detect.NewDetectEngineCtx()
	if err != nil {
		return nil, err
	}
	w.detect = d
	return w, nil
}

type WIps struct {
	filter string
	windiv *windivert.WinDivertHandle
	detect *detect.DetectEngineCtx
	event  Eventfunc
}

func (w *WIps) Start() error {
	//init windivert
	winDivert, err := windivert.NewWinDivertHandle(w.filter)
	if err != nil {
		return err
	}
	w.windiv = winDivert
	packetChan, err := w.windiv.PacketExs()
	if err != nil {
		return err
	}
	go w.checkPacket(packetChan)
	return nil
}

func (w *WIps) Enablelo(b bool) {
	if b {
		w.filter = lofilter
	} else {
		w.filter = defaultfilter
	}
}

func (w *WIps) AddGonidsRule(r *gonids.Rule) error {
	return w.detect.LoadGoNidRule(*r)
}

// RunAllRules make all rules work
func (w *WIps) ApplyRules() error {
	return w.detect.Apply()
}

// RunAllRules make all rules work
func (w *WIps) Build() error {
	return w.detect.Build()
}

func (w *WIps) Close() error {
	if w.windiv == nil {
		return ErrorNotOpen
	}

	err := w.windiv.Close()
	if err != nil && err.Error() != "The operation completed successfully." {
		return err
	}
	return nil
}

func (w *WIps) checkPacket(packetChan <-chan *windivert.Packet) {
	for packet := range packetChan {
		go func(wd *windivert.WinDivertHandle, p *windivert.Packet) {
			w.detect.DetectPacket(newNetLinker(w.event, wd, p), p.Raw)
		}(w.windiv, packet)
	}
}

func (w *WIps) RegisterMatchEvent(e Eventfunc) {
	w.event = e
}

func newNetLinker(event Eventfunc, wd *windivert.WinDivertHandle, p *windivert.Packet) detect.NetLinker {
	return &netlink{wd: wd, p: p, event: event}
}

type netlink struct {
	wd    *windivert.WinDivertHandle
	p     *windivert.Packet
	event Eventfunc
}

func (n *netlink) Default() {
	n.wd.Send(n.p)
}

func (n *netlink) Pass() {
	n.wd.Send(n.p)

}

func (n *netlink) Drop() {
}

func (n *netlink) Alert(e detect.InfoMatched) {
	n.match(e)
}
func (n *netlink) match(e detect.InfoMatched) {
	if n.event != nil {
		go n.event(convertToEvent(int(n.p.Addr.Network().InterfaceIndex), e))
	}
}
