package report

import (
	"fmt"
	"sort"
	"time"

	"mnms/anomaly_detection/def"

	"github.com/google/uuid"
	"github.com/qeof/q"
)

// ReportMessage is a message in report
type ReportMessage struct {
	Message   string `json:"message"`   // message
	State     string `json:"state"`     // normal, anomaly or error
	Reason    string `json:"reason"`    // reason of anomaly or error
	TimeStamp string `json:"timestamp"` // RFC3339
}

// MessageStore is a store of messages
type MessageStore interface {
	Add(msg ReportMessage) (*ReportMessage, error)
	Update(index int, msg ReportMessage) error
	Get(index int) (ReportMessage, error)
	GetAll() ([]ReportMessage, error)
	IsFull() bool
	IsEmpty() bool
	Clear()
	Len() int
}

// MessageArray
type MessageArray struct {
	Messages []ReportMessage
}

// NewMessageArray create a new MessageArray
func NewMessageArray() *MessageArray {
	return &MessageArray{
		Messages: []ReportMessage{},
	}
}

// Update update a message in MessageArray
func (m *MessageArray) Update(index int, msg ReportMessage) error {
	if index < 0 || index >= len(m.Messages) {
		return fmt.Errorf("index out of range")
	}
	m.Messages[index] = msg
	return nil
}

// Add add a message to MessageArray
func (m *MessageArray) Add(msg ReportMessage) (*ReportMessage, error) {
	m.Messages = append(m.Messages, msg)
	return nil, nil
}

// Get get a message from MessageArray
func (m *MessageArray) Get(index int) (ReportMessage, error) {
	if index < 0 || index >= len(m.Messages) {
		return ReportMessage{}, fmt.Errorf("index out of range")
	}
	return m.Messages[index], nil
}

// GetAll get all messages from MessageArray
func (m *MessageArray) GetAll() ([]ReportMessage, error) {
	return m.Messages, nil
}

// IsFull check if MessageArray is full
func (m *MessageArray) IsFull() bool {
	return false
}

// IsEmpty check if MessageArray is empty
func (m *MessageArray) IsEmpty() bool {
	return len(m.Messages) == 0
}

// Clear clear all messages in MessageArray
func (m *MessageArray) Clear() {
	m.Messages = []ReportMessage{}
}

// Len get length of MessageArray
func (m *MessageArray) Len() int {
	return len(m.Messages)
}

// Report is a report of anomaly detection
type Report struct {
	ClientName   string       `json:"client_name"`
	Source       string       `json:"source"` // source: a url, file or event to identify the report
	ID           string       `json:"id"`
	Distance     float32      `json:"distance"`
	Until        string       `json:"until"` // RFC3339
	Since        string       `json:"since"` // RFC3339
	TotalCount   int          `json:"total_count"`
	TotalAnomaly int          `json:"total_anomaly"`
	TotalError   int          `json:"total_error"`
	Error        string       `json:"error"`
	Status       string       `json:"status"`
	Message      string       `json:"message"`
	Messages     MessageStore `json:"-"`
}

type ReportSummary struct {
	ClientName   string `json:"client_name"`
	Source       string `json:"source"`
	ID           string `json:"id"`
	Distance     string `json:"distance"`
	Until        string `json:"until"`
	Since        string `json:"since"`
	TotalCount   int    `json:"total_count"`
	TotalAnomaly int    `json:"total_anomaly"`
	TotalError   int    `json:"total_error"`
	Status       string `json:"status"`
}

type ExportedReport struct {
	ReportSummary
	Messages []ReportMessage `json:"messages"`
}

// GetSummary get report summary
func (r Report) GetSummary() *ReportSummary {
	return &ReportSummary{
		ClientName:   r.ClientName,
		Source:       r.Source,
		ID:           r.ID,
		Distance:     fmt.Sprintf("%f", r.Distance),
		Until:        r.Until,
		Since:        r.Since,
		TotalCount:   r.TotalCount,
		TotalAnomaly: r.TotalAnomaly,
		TotalError:   r.TotalError,
		Status:       r.Status,
	}
}

// Export export report
func (r Report) Export() (*ExportedReport, error) {
	exportReport := ExportedReport{
		ReportSummary: *r.GetSummary(),
		Messages:      []ReportMessage{},
	}
	messages, err := r.Messages.GetAll()
	if err != nil {
		return nil, err
	}
	exportReport.Messages = messages
	return &exportReport, nil
}

// Import import report
func (r *Report) Import(exportedReport *ExportedReport) error {
	r.ClientName = exportedReport.ClientName
	r.Source = exportedReport.Source
	r.ID = exportedReport.ID
	r.Distance = 0
	r.Until = exportedReport.Until
	r.Since = exportedReport.Since
	r.TotalCount = exportedReport.TotalCount
	r.TotalAnomaly = exportedReport.TotalAnomaly
	r.TotalError = exportedReport.TotalError
	r.Status = exportedReport.Status
	r.Messages.Clear()
	for _, msg := range exportedReport.Messages {
		r.Messages.Add(msg)
	}
	return nil
}

// GetMessages get report message
func (r Report) GetMessages(pageSize, page int) ([]ReportMessage, error) {
	if pageSize <= 0 {
		return []ReportMessage{}, fmt.Errorf("page size must be greater than 0")
	}
	sortedMessages, err := r.Messages.GetAll()
	if err != nil {
		return []ReportMessage{}, err
	}
	sort.Sort(SortMessagesByTime(sortedMessages))

	start := (page - 1) * pageSize
	if start < 0 {
		start = 0
	}

	end := page * pageSize
	if start > len(sortedMessages) {
		return []ReportMessage{}, fmt.Errorf("page out of range (%d,%d)", start, end)
	}
	if end > len(sortedMessages) {
		end = len(sortedMessages)
	}

	return sortedMessages[start:end], nil
}

// GetXXXMessages get anomaly messages
func (r Report) GetXXXMessages(kind string, pageSize, page int) ([]ReportMessage, error) {
	if pageSize <= 0 {
		return []ReportMessage{}, fmt.Errorf("page size must be greater than 0")
	}
	start := (page - 1) * pageSize
	end := page * pageSize

	total := 0
	switch kind {
	case "all":
		total = r.TotalCount
	case "anomaly":
		total = r.TotalAnomaly
	case "error":
		total = r.TotalError
	case "normal":
		total = r.TotalCount - r.TotalAnomaly - r.TotalError
	default:
		return []ReportMessage{}, fmt.Errorf("kind must be anomaly, error or normal")
	}

	if start > total {
		return []ReportMessage{}, fmt.Errorf("page out of range (%d,%d)", start, end)
	}
	if end > total {
		end = total
	}

	var msgs []ReportMessage
	count := 0
	// get messsage, and reverse
	for i := r.Messages.Len() - 1; i >= 0; i-- {
		msg, err := r.Messages.Get(i)
		if err != nil {
			return []ReportMessage{}, err
		}
		if msg.State == kind || kind == "all" {
			if count >= start && count < end {
				msgs = append(msgs, msg)
			}
			count++
		}
	}

	return msgs, nil
}

type SortReportSummaryByUntil []ReportSummary

func (a SortReportSummaryByUntil) Len() int      { return len(a) }
func (a SortReportSummaryByUntil) Swap(i, j int) { a[i], a[j] = a[j], a[i] }
func (a SortReportSummaryByUntil) Less(i, j int) bool {
	t1, err := time.Parse(time.RFC3339, a[i].Since)
	if err != nil {
		return false
	}
	t2, err := time.Parse(time.RFC3339, a[j].Since)
	if err != nil {
		return false
	}
	return t1.After(t2)
}

// SortMessagesByTime sort messages by time
type SortMessagesByTime []ReportMessage

func (a SortMessagesByTime) Len() int      { return len(a) }
func (a SortMessagesByTime) Swap(i, j int) { a[i], a[j] = a[j], a[i] }
func (a SortMessagesByTime) Less(i, j int) bool {
	t1, err := time.Parse(time.RFC3339, a[i].TimeStamp)
	if err != nil {
		return false
	}
	t2, err := time.Parse(time.RFC3339, a[j].TimeStamp)
	if err != nil {
		return false
	}
	return t1.After(t2)
}

const DefaultScore = 0.4

// NewErrorReport create a new error report
func NewErrorReport(clientName, source string, err error) *Report {
	msg := fmt.Sprintf("error: %v", err)
	return &Report{

		ClientName: clientName,
		Source:     source,
		ID:         uuid.New().String(),
		Since:      time.Now().Format(time.RFC3339),
		Error:      msg,
		Status:     "fail",
		TotalCount: 0,

		Messages: NewMessageArray(),
	}
}

func NewCircularReport(clientName string, source string, distance float32) *Report {
	s := NewCircularMessages(5000)
	return &Report{

		ClientName: clientName,
		Source:     source,
		ID:         uuid.New().String(),
		Since:      time.Now().Format(time.RFC3339),
		Distance:   distance,
		Status:     "running",
		TotalCount: 0,

		Messages: s,
	}
}

func NewStandardReport(clientName string, source string, distance float32) *Report {
	s := NewMessageArray()
	return &Report{
		ClientName: clientName,
		Source:     source,
		ID:         uuid.New().String(),
		Since:      time.Now().Format(time.RFC3339),
		Distance:   distance,
		Status:     "running",
		TotalCount: 0,
		Messages:   s,
	}
}

// NewReport create a new report
func NewReport(clientName string, source string, distance float32, store MessageStore) *Report {
	return &Report{

		ClientName: clientName,
		Source:     source,
		ID:         uuid.New().String(),
		Since:      time.Now().Format(time.RFC3339),
		Distance:   distance,
		Status:     "running",
		TotalCount: 0,

		Messages: store,
	}
}

func (r *Report) AddMessage(message string, state string, reason string) error {
	full := r.Messages.IsFull()
	pre, err := r.Messages.Add(ReportMessage{
		Message:   message,
		State:     state,
		Reason:    reason,
		TimeStamp: time.Now().Format(time.RFC3339),
	})

	q.Q("AddMessage", message, " pre: ", pre, "isFull: ", r.Messages.IsFull())

	if full && pre != nil {

		// store full
		switch pre.State {
		case "anomaly":
			r.TotalAnomaly--
			if r.TotalAnomaly < 0 {
				r.TotalAnomaly = 0
			}
		case "error":
			r.TotalError--
			if r.TotalError < 0 {
				r.TotalError = 0
			}

		}
	} else {
		// store not full add
		r.TotalCount++
		switch state {
		case "anomaly":
			r.TotalAnomaly++
		case "error":
			r.TotalError++

		}
	}
	return err

}

// Reset reset report
func (r *Report) Reset() {
	r.Messages.Clear()
	r.TotalCount = 0
	r.TotalAnomaly = 0
	r.TotalError = 0
	r.Status = "running"
}

// AddError add error to report
func (r *Report) AddError(log string, err error) error {

	return r.AddMessage(log, "error", err.Error())
}

// AddResult add result to report
func (r *Report) AddResult(result *def.LogAnomalyResult) error {
	if result.Normal {
		return r.AddMessage(result.Text, "normal", "")
	} else {
		return r.AddMessage(result.Text, "anomaly", result.Reason)
	}
}

var realtimeReport *Report

// GetRealtimeReport get realtime report
func GetRealtimeReport() *Report {
	if realtimeReport == nil {

		realtimeReport = NewCircularReport("realtime", "realtime", 0.1)
	}
	return realtimeReport
}

// init
func init() {
	realtimeReport = NewCircularReport("realtime", "realtime", 0.1)
}
