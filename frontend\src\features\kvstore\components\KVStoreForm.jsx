import { memo } from "react";
import { <PERSON><PERSON>, Card, Form, Flex, Input } from "antd";
import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import { useKVStore } from "../hooks/useKVStore";

/**
 * Form component for adding key-value pairs to the KV store
 * @returns {JSX.Element} KVStore form component
 */
const KVStoreForm = () => {
  const { form, handleAddKeyValues } = useKVStore();

  return (
    <Card title="Add Key-Value Pairs">
      <Form
        form={form}
        layout="vertical"
        id="key-store-form"
        autoComplete="off"
        onFinish={handleAddKeyValues}
      >
        <Form.List
          name="keyStore"
          rules={[
            {
              validator: async (_, names) => {
                if (!names || names.length <= 0) {
                  return Promise.reject(
                    new Error("Add at least 1 key-value pair!")
                  );
                }
              },
            },
          ]}
        >
          {(fields, { add, remove }, { errors }) => (
            <>
              {fields.map(({ key, name, ...restField }) => (
                <Flex justify="space-between" key={key} align="flex-start">
                  <Form.Item
                    required
                    label="Key Name"
                    {...restField}
                    name={[name, "keyName"]}
                    rules={[
                      {
                        required: true,
                        message: "Please input your key name!",
                      },
                    ]}
                    style={{ flex: 1, marginRight: 8 }}
                  >
                    <Input placeholder="Enter key name" />
                  </Form.Item>
                  <Form.Item
                    required
                    label="Key Value"
                    {...restField}
                    name={[name, "keyValue"]}
                    rules={[
                      {
                        required: true,
                        message: "Please input your key value!",
                      },
                    ]}
                    style={{ flex: 1, marginRight: 8 }}
                  >
                    <Input placeholder="Enter key value" />
                  </Form.Item>
                  <Button
                    type="text"
                    danger
                    icon={<MinusCircleOutlined />}
                    onClick={() => remove(name)}
                    style={{ marginTop: 29 }}
                  />
                </Flex>
              ))}
              <Form.Item>
                <Button
                  type="dashed"
                  onClick={() => add()}
                  block
                  icon={<PlusOutlined />}
                >
                  Add Key-Value Pair
                </Button>
                <Form.ErrorList errors={errors} />
              </Form.Item>
            </>
          )}
        </Form.List>
        <Form.Item>
          <Button type="primary" htmlType="submit" block>
            Save
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default memo(KVStoreForm);
