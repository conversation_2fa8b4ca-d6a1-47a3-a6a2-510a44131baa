import { describe, it, expect, vi } from "vitest";
import { render, screen } from "@testing-library/react";
import { ClusterInfoComponent } from "./clusterinfo";

// Mock dependencies
vi.mock("@tanstack/react-router", () => ({
  createFileRoute: vi.fn(() => () => ({})),
}));

vi.mock("antd", () => ({
  Col: vi.fn(({ children }) => <div data-testid="col">{children}</div>),
  Row: vi.fn(({ children }) => <div data-testid="row">{children}</div>),
}));

vi.mock("../../features/clusterinfo", () => ({
  ClusterInfoTable: vi.fn(() => (
    <div data-testid="cluster-info-table">Cluster Info Table</div>
  )),
  RootInfoCard: vi.fn(() => (
    <div data-testid="root-info-card">Root Info Card</div>
  )),
}));

describe("ClusterInfoComponent", () => {
  it("renders the ClusterInfoLayout with RootInfoCard and ClusterInfoTable", () => {
    render(<ClusterInfoComponent />);

    // Check if the layout components are rendered
    expect(screen.getByTestId("row")).toBeInTheDocument();
    expect(screen.getAllByTestId("col")).toHaveLength(2);

    // Check if the content components are rendered
    expect(screen.getByTestId("root-info-card")).toBeInTheDocument();
    expect(screen.getByTestId("cluster-info-table")).toBeInTheDocument();

    // Check if the text content is rendered
    expect(screen.getByText("Root Info Card")).toBeInTheDocument();
    expect(screen.getByText("Cluster Info Table")).toBeInTheDocument();
  });
});
