const DEFAULT_ERROR_MESSAGE = "An error occurred while executing the command";
const SUCCESS_MESSAGE = "Command sent successfully!";

/**
 * Handles the execution of network commands
 * @param command - The network command to execute
 * @param sendCommand - The mutation function to send the command
 * @param notification - The notification service
 * @returns Promise<CommandResponse>
 */
export const handleCommandExecution = async (
  command,
  sendCommand,
  notification
) => {
  try {
    // Validate command input
    if (!Array.isArray(command) || command.length === 0) {
      throw new Error("Invalid command format");
    }

    const response = await sendCommand.mutateAsync(command);

    notification.success({
      message: SUCCESS_MESSAGE,
    });

    return {
      success: true,
      message: SUCCESS_MESSAGE,
      data: response,
    };
  } catch (error) {
    const errorMessage = getErrorMessage(error);

    notification.error({
      message: errorMessage,
    });

    return {
      success: false,
      message: errorMessage,
    };
  }
};

/**
 * Extracts error message from different error types
 */
const getErrorMessage = (error) => {
  if (error instanceof Error) {
    return error.message;
  }

  if (typeof error === "object" && error !== null) {
    const errorObj = error;
    return (
      errorObj.data?.error ||
      errorObj.data?.toString() ||
      errorObj.message ||
      DEFAULT_ERROR_MESSAGE
    );
  }

  return DEFAULT_ERROR_MESSAGE;
};
