import React from "react";
import { Space, Typography, theme } from "antd";
import Icon from "@ant-design/icons";

const { Title, Text } = Typography;

const DeviceConfirmationModal = ({ icon: IconComponent, title, text }) => {
  const { token } = theme.useToken();

  return (
    <Space align="center" direction="vertical" style={{ width: "100%" }}>
      <Icon
        component={IconComponent}
        style={{
          color: token.colorWarning,
          fontSize: 64,
        }}
      />
      <Title level={4}>{title}</Title>
      <Text strong>{text}</Text>
    </Space>
  );
};

export default DeviceConfirmationModal;
