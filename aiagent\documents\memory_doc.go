package documents

import (
	"encoding/json"
	"fmt"
	"mnms/aiagent/documents/def"
	"strings"
	"sync"
)

// Implmentation of DocumentProvider interface on the local memory

type LocalMemoryDocumentProvider struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	// map of document id to document
	Docs  map[string]string `json:"docs"`
	mutex sync.Mutex
}

// NewLocalMemoryDocumentProvider creates a new LocalMemoryDocumentProvider
func NewLocalMemoryDocumentProvider(name, description string) *LocalMemoryDocumentProvider {
	return &LocalMemoryDocumentProvider{
		Name:        name,
		Description: description,
		Docs:        make(map[string]string),
		mutex:       sync.Mutex{},
	}
}

// Delete deletes a document by its id
func (l *LocalMemoryDocumentProvider) Delete(id string) error {
	l.mutex.Lock()
	defer l.mutex.Unlock()
	delete(l.Docs, id)
	return nil
}

// Implementtaion of Querier interface
func (l *LocalMemoryDocumentProvider) Query(query string) ([]string, error) {
	var res []string
	// fuzzy search id and content
	for k, v := range l.Docs {
		if query == "" {
			res = append(res, v)
			continue
		}
		if strings.Contains(strings.ToLower(k), strings.ToLower(query)) || strings.Contains(strings.ToLower(v), strings.ToLower(query)) {
			res = append(res, v)
		}
	}
	return res, nil
}

// Implementation fo updater interface
func (l *LocalMemoryDocumentProvider) Update(id, content string) error {
	l.mutex.Lock()
	defer l.mutex.Unlock()
	l.Docs[id] = content
	return nil
}

// Implementation of Finder interface
func (l *LocalMemoryDocumentProvider) Find(id string) (string, error) {
	if res, ok := l.Docs[id]; ok {
		return res, nil
	}
	return "", fmt.Errorf("document %s not found", id)
}

// Implementation of DocumentDescriber interface
func (l *LocalMemoryDocumentProvider) GetDescription() string {
	return l.Description
}

func (l *LocalMemoryDocumentProvider) GetName() string {
	return l.Name
}

// Serialize returns a serialized version of the document provider
func (l *LocalMemoryDocumentProvider) Serialize() ([]byte, error) {
	l.mutex.Lock()
	defer l.mutex.Unlock()
	return json.Marshal(l)
}

// Deserialize deserializes the document provider
func DeserializeLocalMemoryDocumentProvider(data []byte) (*LocalMemoryDocumentProvider, error) {
	l := &LocalMemoryDocumentProvider{}
	err := json.Unmarshal(data, l)
	return l, err
	//return json.Unmarshal(data, l)
}

// TotalCount returns the total number of documents in the provider
func (l *LocalMemoryDocumentProvider) TotalCount() int {
	return len(l.Docs)
}

// GetPage returns a page of documents
func (l *LocalMemoryDocumentProvider) GetPage(page, pageSize int) ([]def.DocumentItem, error) {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	// check page and pageSize
	if page < 0 || pageSize < 0 {
		return nil, fmt.Errorf("invalid page or pageSize")
	}
	if pageSize == 0 {
		return nil, fmt.Errorf("pageSize cannot be 0")
	}

	res := make([]def.DocumentItem, 0)
	for k, v := range l.Docs {
		if page > 0 {
			page--
			continue
		}
		if pageSize == 0 {
			return res, nil
		}
		res = append(res, def.DocumentItem{
			ID:      k,
			Content: v})
		pageSize--
	}
	return res, nil
}
