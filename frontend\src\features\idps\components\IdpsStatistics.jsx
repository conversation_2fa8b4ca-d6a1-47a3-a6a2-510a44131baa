import { memo } from "react";
import { <PERSON>, Col, Row, Statistic, Spin } from "antd";
import {
  AlertOutlined,
  StopOutlined,
  FileTextOutlined,
} from "@ant-design/icons";

/**
 * IDPS Statistics Component
 * Displays key statistics for the selected IDPS service
 * @param {Object} props Component props
 * @param {Object} props.data Statistics data
 * @param {boolean} props.loading Loading state
 * @returns {JSX.Element} Statistics component
 */
const IdpsStatistics = ({ data = {}, loading = false }) => {
  const {
    totalRules = 0,
    totalEvents = 0,
    totalPackets = 0,
    alertEvents = 0,
    dropEvents = 0,
  } = data;

  const statisticsData = [
    {
      title: "Total Rules",
      value: totalRules,
      prefix: <FileTextOutlined />,
      color: "#1890ff",
    },
    {
      title: "Total Events",
      value: totalEvents,
      prefix: <FileTextOutlined />,
      color: "#52c41a",
    },
    {
      title: "Total Packets",
      value: totalPackets,
      prefix: <FileTextOutlined />,
      color: "#722ed1",
    },
    {
      title: "Alert Events",
      value: alertEvents,
      prefix: <AlertOutlined />,
      color: "#faad14",
    },
    {
      title: "Drop Events",
      value: dropEvents,
      prefix: <StopOutlined />,
      color: "#f5222d",
    },
  ];

  return (
    <Spin spinning={loading}>
      <Row gutter={[16, 16]}>
        {statisticsData.map((stat, index) => (
          <Col xs={24} sm={12} md={8} lg={4.8} key={index}>
            <Card variant="borderless">
              <Statistic
                title={stat.title}
                value={stat.value}
                prefix={stat.prefix}
                valueStyle={{ color: stat.color }}
              />
            </Card>
          </Col>
        ))}
      </Row>
    </Spin>
  );
};

export default memo(IdpsStatistics);
