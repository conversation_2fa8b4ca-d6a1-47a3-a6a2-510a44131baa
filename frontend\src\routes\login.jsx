import { createFileRoute } from "@tanstack/react-router";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Flex, Form, Image, Input } from "antd";
import { LockOutlined, UserOutlined } from "@ant-design/icons";
import { useState } from "react";
import { useThemeMode } from "antd-style";
import { useLogin } from "../services/mutations";
import { useAuthStore } from "../store/auth-store";
import atopDarklogo from "../assets/images/darkmode-logo.svg";
import logo from "../assets/images/NIMBL_Logo.svg";
import ServersStatus from "../components/servers-status";
import SettingsComp from "../components/SettingsComp";
import { useAppStore } from "../store/store";

const LOCK_DURATION = 10000; // 10 seconds
const DEFAULT_REDIRECT = "/dashboard/device";

export const Route = createFileRoute("/login")({
  beforeLoad: async ({ context: { auth } }) => {
    await auth?.clearAuthData();
  },
  component: LoginComponent,
});

export function LoginComponent() {
  const [isLocked, setIsLocked] = useState(false);
  const { openDialogs } = useAppStore();
  const navigate = Route.useNavigate();
  const loginMutation = useLogin();
  const auth = useAuthStore();
  const { appearance } = useThemeMode();
  const [form] = Form.useForm();
  const { notification } = App.useApp();

  const handleLoginSubmit = async (values) => {
    const { user, password } = values;

    try {
      const authData = await loginMutation.mutateAsync({ user, password });
      if (authData.sessionID) {
        openDialogs({ id: "validate2fa", data: authData });
      } else {
        await auth.setAuthData(authData);
        await navigate({ to: DEFAULT_REDIRECT });
      }
    } catch (error) {
      handleLoginError(error);
    } finally {
      form.resetFields();
    }
  };

  const handleLoginError = (error) => {
    console.log(error);
    const errorMessage = getErrorMessage(error);
    showErrorNotification(errorMessage);
    lockLoginTemporarily();
  };

  const getErrorMessage = (error) => {
    if (error.response?.data) return error.response.data;
    if (error.message) return error.message;
    return "Something went wrong!";
  };

  const showErrorNotification = (message) => {
    notification.error({
      message: "Login Error",
      description: message,
    });
  };

  const lockLoginTemporarily = () => {
    setIsLocked(true);
    setTimeout(() => setIsLocked(false), LOCK_DURATION);
  };

  return (
    <Flex justify="center" align="center" style={{ height: "100vh" }}>
      <SettingsButton />
      <LoginCard
        appearance={appearance}
        form={form}
        onSubmit={handleLoginSubmit}
        isLoading={loginMutation.isPending}
        isLocked={isLocked}
      />
    </Flex>
  );
}

const SettingsButton = () => (
  <div style={{ position: "fixed", right: "20px", top: "20px" }}>
    <SettingsComp />
  </div>
);

const LoginCard = ({ appearance, form, onSubmit, isLoading, isLocked }) => (
  <Card variant="borderless">
    <Flex vertical gap={16}>
      <Logo appearance={appearance} />
      <ServersStatus />
      <LoginForm
        form={form}
        onSubmit={onSubmit}
        isLoading={isLoading}
        isLocked={isLocked}
      />
    </Flex>
  </Card>
);

const Logo = ({ appearance }) => (
  <Image
    height={56}
    alt="NIMBL"
    src={appearance === "dark" ? atopDarklogo : logo}
    preview={false}
  />
);

const LoginForm = ({ form, onSubmit, isLoading, isLocked }) => (
  <Form name="login_form" autoComplete="off" onFinish={onSubmit} form={form}>
    <Form.Item
      name="user"
      rules={[
        {
          required: true,
          message: "Please input your username!",
        },
      ]}
    >
      <Input prefix={<UserOutlined />} placeholder="Username" />
    </Form.Item>
    <Form.Item
      name="password"
      rules={[
        {
          required: true,
          message: "Please input your Password!",
        },
      ]}
    >
      <Input.Password
        prefix={<LockOutlined />}
        type="password"
        placeholder="Password"
      />
    </Form.Item>
    <Form.Item>
      <Button
        type="primary"
        htmlType="submit"
        block
        loading={isLoading}
        disabled={isLocked}
      >
        Sign in
      </Button>
    </Form.Item>
  </Form>
);

export default LoginComponent;
