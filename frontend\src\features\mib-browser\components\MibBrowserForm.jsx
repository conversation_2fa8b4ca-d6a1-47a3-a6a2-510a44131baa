import React, { memo } from "react";
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Row,
  Col,
  Space,
  Divider,
} from "antd";
import {
  useMib<PERSON>rowser,
  SNMP_OPERATIONS,
  SNMP_VALUE_TYPES,
} from "../hooks/useMibBrowser";

/**
 * MIB Browser Form component for SNMP operations
 * @returns {JSX.Element} MIB Browser Form component
 */
const MibBrowserForm = () => {
  const { formValues, handleFieldChange, handleGoClick } = useMibBrowser();
  const { ip_address, oid, operation, value, valueType } = formValues;

  const showValueFields = operation === "set";

  return (
    <Card variant="borderless" title="MIB Browser">
      <Form layout="vertical">
        <Form.Item
          label="IP Address"
          required
          tooltip="IP address of the SNMP device"
        >
          <Input
            value={ip_address}
            onChange={(e) =>
              handleFieldChange({ ip_address: e.target.value.trimEnd() })
            }
            placeholder="Enter device IP address"
          />
        </Form.Item>

        <Form.Item
          label="OID"
          required
          tooltip="Object Identifier for the SNMP operation"
        >
          <Input
            value={oid}
            onChange={(e) => handleFieldChange({ oid: e.target.value })}
            placeholder="Enter OID (e.g., .*******.2.1)"
          />
        </Form.Item>

        <Form.Item
          label="Operation"
          required
          tooltip="SNMP operation to perform"
        >
          <Select
            style={{ width: "100%" }}
            value={operation}
            onChange={(value) => handleFieldChange({ operation: value })}
            options={SNMP_OPERATIONS}
          />
        </Form.Item>

        {showValueFields && (
          <>
            <Divider style={{ margin: "12px 0" }} />
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="Value"
                  required={showValueFields}
                  tooltip="Value to set for the OID"
                >
                  <Input
                    value={value}
                    onChange={(e) =>
                      handleFieldChange({ value: e.target.value })
                    }
                    placeholder="Enter value to set"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Value Type"
                  required={showValueFields}
                  tooltip="Data type of the value to set"
                >
                  <Select
                    style={{ width: "100%" }}
                    value={valueType}
                    onChange={(value) =>
                      handleFieldChange({ valueType: value })
                    }
                    options={SNMP_VALUE_TYPES}
                  />
                </Form.Item>
              </Col>
            </Row>
          </>
        )}

        <Form.Item>
          <Space direction="vertical" style={{ width: "100%" }}>
            <Button type="primary" onClick={handleGoClick} size="large" block>
              Execute SNMP {operation.toUpperCase()}
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default memo(MibBrowserForm);
