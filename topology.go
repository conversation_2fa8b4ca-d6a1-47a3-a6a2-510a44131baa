package mnms

import (
	"bytes"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/qeof/q"
)

type Link struct {
	Source      string `json:"source"`      // source mac
	Target      string `json:"target"`      // target mac
	SourcePort  string `json:"sourcePort"`  // source port
	TargetPort  string `json:"targetPort"`  // target port
	Edge        string `json:"edge"`        // edge data
	BlockedPort string `json:"blockedPort"` // blocked port
	LinkType    string `json:"linkType"`    // link type (agent,manual, etc)
}

type Topology struct {
	Id          string `json:"id"`          // id for node
	IpAddress   string `json:"ipAddress"`   // IpAddress for node
	ModelName   string `json:"modelname"`   // ModelName for node
	Services    string `json:"services"`    // Network service name
	LastUpdated int    `json:"lastUpdated"` // Network service name
	LinkData    []Link `json:"linkData"`    // original topology link data
	TopoType    string `json:"topoType"`    // type of topology
}

func PublishTopology(topologyData Topology) error {
	// send all topology info to root
	if QC.RootURL == "" {
		return fmt.Errorf("skip publishing devices, no root")
	}
	topologyData.Services = QC.Name
	topologyData.TopoType = "agent"
	for i, link := range topologyData.LinkData {
		edgeData := createEdge(link.Source, link.Target)
		topologyData.LinkData[i].Edge = edgeData
		topologyData.LinkData[i].LinkType = "agent"
	}

	jsonBytes, err := json.Marshal(topologyData)
	if err != nil {
		q.Q(err)
		return err
	}
	resp, err := PostWithToken(QC.RootURL+"/api/v1/topology", QC.AdminToken, bytes.NewBuffer(jsonBytes))
	if err != nil {
		q.Q(err, QC.RootURL)
	}
	if resp != nil {
		res := make(map[string]interface{})
		_ = json.NewDecoder(resp.Body).Decode(&res)
		// q.Q(res)
		// save close here
		defer resp.Body.Close()
	}
	return nil
}

func InsertTopology(topoDesc Topology) error {
	// add all device as node to improve the topology
	AddAllDeviceAsNode()

	// INFO: not checking equal or exist because updating the latest timestamp so if divice offline we can remove that topology
	// INFO: this is specially for when agent client will not send toplogy information if device offline
	topoDesc.LastUpdated = int(time.Now().Unix())
	// insert agent topology as it is
	if topoDesc.TopoType == "agent" {
		QC.TopologyMutex.Lock()
		QC.TopologyData[topoDesc.Id] = topoDesc
		QC.TopologyMutex.Unlock()
		return nil
	}
	// insert manual topology link
	dev, err := FindDev(topoDesc.Id)
	if err != nil || dev == nil {
		return fmt.Errorf("error: not found device %s", topoDesc.Id)
	}
	QC.TopologyMutex.Lock()
	topodata, ok := QC.TopologyData[topoDesc.Id]
	QC.TopologyMutex.Unlock()
	// check if device is in topology, if not create new topology
	if !ok {
		// create new topology
		topodata = Topology{
			Id:          topoDesc.Id,
			IpAddress:   dev.IPAddress,
			ModelName:   dev.ModelName,
			LastUpdated: int(time.Now().Unix()),
			Services:    dev.ScannedBy,
			TopoType:    "manual",
			LinkData:    topoDesc.LinkData,
		}
	}
	topodata.TopoType = "manual"
	topodata.LinkData = topoDesc.LinkData
	QC.TopologyMutex.Lock()
	QC.TopologyData[topodata.Id] = topodata
	QC.TopologyMutex.Unlock()
	return nil
}

func AddAllDeviceAsNode() {
	for _, dev := range QC.DevData {
		if dev.Mac != "11-22-33-44-55-66" {
			QC.TopologyMutex.Lock()
			topodata, ok := QC.TopologyData[dev.Mac]
			// check if device is in topology, if not create new node
			if !ok {
				// create new node
				topodata = Topology{
					Id:          dev.Mac,
					IpAddress:   dev.IPAddress,
					ModelName:   dev.ModelName,
					LastUpdated: int(time.Now().Unix()),
					Services:    dev.ScannedBy,
				}
			}
			QC.TopologyData[topodata.Id] = topodata
			QC.TopologyMutex.Unlock()
		}
	}
}

// Helper function to create edge identifier
func createEdge(source, target string) string {
	if source < target {
		return source + "_" + target
	}
	return target + "_" + source
}

// TopologyMcpTool is a function that returns a list of MCPToolMetadata

func TopologyMcpTool() []MCPToolMetadata {
	tools := []MCPToolMetadata{
		{
			Name:        "update_topology",
			Description: "Add or update topology information for a device",
			Parameters: mcp.ToolInputSchema{
				Properties: map[string]any{
					"id": map[string]any{
						"type":        "string",
						"description": "The unique identifier for the node or mac address for the node",
					},
					"linkData": map[string]any{
						"type": "array",
						"items": map[string]any{
							"type": "object",
							"properties": map[string]any{
								"target": map[string]any{
									"type":        "string",
									"description": "Target MAC address",
								},
								"sourcePort": map[string]any{
									"type":        "string",
									"description": "Source port",
								},
								"targetPort": map[string]any{
									"type":        "string",
									"description": "Target port",
								},
							},
							"required": []string{"target", "sourcePort", "targetPort"},
						},
						"description": "Array of link data describing topology connections",
					},
				},
				Required: []string{"id", "linkData"},
				Type:     "object",
			},
		},
		{
			Name:        "save_topology",
			Description: "Save the current topology to the file",
			Parameters: mcp.ToolInputSchema{
				Type: "object",
				Properties: map[string]any{
					"filename": map[string]any{
						"type":        "string",
						"description": "The name of the file to save the topology to",
					},
				},
				Required: []string{"filename"},
			},
		},
		{
			Name:        "restore_topology",
			Description: "Restore the topology from the file! load the topology from the file",
			Parameters: mcp.ToolInputSchema{
				Type: "object",
				Properties: map[string]any{
					"filename": map[string]any{
						"type":        "string",
						"description": "The name of the file to restore the topology from",
					},
				},
				Required: []string{"filename"},
			},
		},
	}
	return tools
}

func handleMcpTopologyUpdate(args map[string]interface{}) (Topology, error) {
	topology := Topology{}
	if len(args) > 2 {
		return topology, fmt.Errorf("expected 2 arguments, got %d", len(args))
	}
	id, ok := args["id"].(string)
	if !ok {
		return topology, fmt.Errorf("invalid id")
	}
	linkData, ok := args["linkData"].([]interface{})
	if !ok {
		return topology, fmt.Errorf("invalid linkData")
	}
	var links []Link
	for _, link := range linkData {
		linkMap, ok := link.(map[string]interface{})
		if !ok {
			return topology, fmt.Errorf("invalid link data")
		}
		if len(args) > 3 {
			return topology, fmt.Errorf("expected linkData 3 arguments, got %d", len(args))
		}
		target, _ := linkMap["target"].(string)
		sourcePort, _ := linkMap["sourcePort"].(string)
		targetPort, _ := linkMap["targetPort"].(string)
		links = append(links, Link{
			Source:      id,
			Target:      strings.TrimSpace(target),
			SourcePort:  strings.TrimSpace(sourcePort),
			TargetPort:  strings.TrimSpace(targetPort),
			BlockedPort: "false",
			LinkType:    "manual",
			Edge:        createEdge(id, strings.TrimSpace(target)),
		})
	}
	topology = Topology{
		Id:       id,
		LinkData: links,
		TopoType: "manual",
	}
	err := InsertTopology(topology)
	if err != nil {
		return Topology{}, err
	}
	return topology, nil
}

// Define the directory and file path
const dirPath = "topology"

func handleMcpSaveTopology(args map[string]interface{}) (string, error) {
	if len(args) != 1 {
		return "", fmt.Errorf("expected 1 argument, got %d", len(args))
	}
	filename, ok := args["filename"].(string)
	if !ok {
		return "", fmt.Errorf("invalid filename")
	}
	if !strings.HasSuffix(filename, ".json") {
		filename += ".json"
	}
	fullPath := filepath.Join(dirPath, filename)
	// Ensure the directory exists
	if err := os.MkdirAll(dirPath, os.ModePerm); err != nil {
		return "", fmt.Errorf("failed to create directory: %v", err)
	}
	// filter manual topology data and create data to save
	var filteredTopology []Topology
	for _, topo := range QC.TopologyData {
		if topo.TopoType == "manual" {
			filteredTopology = append(filteredTopology, topo)
		}
	}
	// Convert the struct to JSON
	jsonData, err := json.MarshalIndent(filteredTopology, "", "  ")
	if err != nil {
		return "", fmt.Errorf("failed to marshal topology data: %v", err)
	}
	if err := os.WriteFile(fullPath, jsonData, os.ModePerm); err != nil {
		return "", fmt.Errorf("failed to write to file: %v", err)
	}
	return fmt.Sprintf("Network topology successfully saved to '%s'. Total devices saved: %d",
		filename, len(filteredTopology)), nil
}

func handleMcpRestoreTopology(args map[string]interface{}) (string, error) {
	if len(args) != 1 {
		return "", fmt.Errorf("expected 1 argument, got %d", len(args))
	}
	filename, ok := args["filename"].(string)
	if !ok {
		return "", fmt.Errorf("invalid filename")
	}
	if !strings.HasSuffix(filename, ".json") {
		filename += ".json"
	}
	fullPath := filepath.Join(dirPath, filename)
	// Read the file
	jsonData, err := os.ReadFile(fullPath)
	if err != nil {
		return "", fmt.Errorf("failed to read file: %v", err)
	}
	var topologies []Topology
	if err := json.Unmarshal(jsonData, &topologies); err != nil {
		return "", fmt.Errorf("failed to unmarshal JSON data: %v", err)
	}
	for _, topo := range topologies {
		// checking restore device topology device exist or not
		if _, err := FindDev(topo.Id); err == nil {
			QC.TopologyMutex.Lock()
			QC.TopologyData[topo.Id] = topo
			QC.TopologyMutex.Unlock()
		}
	}
	return fmt.Sprintf("Successfully restored topology from '%s'. Total devices restored: %d",
		filename, len(topologies)), nil
}
