package node

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/qeof/q"
	"github.com/spf13/viper"
)

const executeAPIJSONSchema = `
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "$id": "",
  "title": "execute_API",
  "description": "Send RESTful API request and retrieve response.",
  "properties": {
    "do_request": {
      "description": "If need to send API to query something do_request should be true.",
      "type": "boolean",
      "additionalProperties": false
    },
    "body": {
      "type": "object",
      "additionalProperties": false
    },
    "complete": {
      "description": "put llm result here",
      "type": "string",
      "additionalProperties": false
    },
    "headers": {
      "type": "object",
      "additionalProperties": false
    },
    "method": {
      "type": "string",
      "enum": [
        "GET",
        "POST",
        "PUT",
        "PATCH",
        "DELETE"
      ],
      "additionalProperties": false
    },
    "query_params": {
      "type": "object",
      "additionalProperties": false
    },
    "url": {
      "description": "The API URL",
      "type": "string",
      "additionalProperties": false
    }
  },
  "required": [
    "url",
    "method",
    "do_request",
    "complete"
  ],
  "additionalProperties": false
}`

const executeAPISystemPrompt = `
You are an assistant that receives user questions or messages.
Your task is to determine whether the user's input is referencing a specific information that can be found with an API call.

Please refer to an extra document for API that is able to retrieve necessary information.
You must then produce a structured JSON response according to the following rules:
- action: If need to send API to query something action should be yes. otherwise no.
- If need more than one API call, you can response a list of JOSN to describe each API call.
`

var ExecuteAPIInfo = NodeFuncInfo{
	Name:        "execAPI",
	Description: "Send RESTful API request and retrieve response.",
	Function:    SendAPIRequest,
	Schema:      executeAPIJSONSchema,
}

// Auth represents optional authentication settings.
type Auth struct {
	// Type can be "basic" or "bearer"
	Type     string `json:"type,omitempty"`
	Username string `json:"username,omitempty"` // Required for basic auth
	Password string `json:"password,omitempty"` // Required for basic auth
	Token    string `json:"token,omitempty"`    // Required for bearer auth
}

// APIRequest describes an API call.
type APIRequest struct {
	DoRequset   bool              `json:"do_request"`             // required
	Complete    string            `json:"complete"`               // required
	URL         string            `json:"url"`                    // required
	Method      string            `json:"method"`                 // required: GET, POST, PUT, PATCH, DELETE
	Headers     map[string]string `json:"headers,omitempty"`      // optional headers
	QueryParams map[string]string `json:"query_params,omitempty"` // optional query parameters
	Body        json.RawMessage   `json:"body,omitempty"`         // optional body (must be valid JSON)
	Auth        *Auth             `json:"auth,omitempty"`         // optional authentication settings
	Timeout     int               `json:"timeout,omitempty"`      // optional timeout in seconds
}

// SendAPIRequest sends an HTTP request based on the provided APIRequest.
func SendAPIRequest(input RunNodeOutput, query string) (RunNodeOutput, error) {

	jsonBytes, err := json.Marshal(input.Data)
	if err != nil {
		return input, fmt.Errorf("failed to marshal data: %w", err)
	}

	var apiReq APIRequest
	if err := json.Unmarshal(jsonBytes, &apiReq); err != nil {
		msg := fmt.Sprintf("Input %s is not a valid parameter for ExecuteAPI node", query)
		return input, fmt.Errorf("%s: %v", msg, err)
	}

	if !apiReq.DoRequset {
		input.Data = query
		input.Format = "text"
		input.FlowControl = "break"
		return input, nil
	}

	if apiReq.URL == "" {
		return input, errors.New("url is required")
	}
	if apiReq.Method == "" {
		return input, errors.New("method is required")
	}

	// Normalize HTTP method.
	method := strings.ToUpper(apiReq.Method)
	allowedMethods := map[string]bool{"GET": true, "POST": true, "PUT": true, "PATCH": true, "DELETE": true}
	if !allowedMethods[method] {
		return input, fmt.Errorf("unsupported method: %s", method)
	}

	// Parse URL and add query parameters if present.
	parsedURL, err := url.Parse(apiReq.URL)
	if err != nil {
		return input, fmt.Errorf("invalid url: %w", err)
	}
	if len(apiReq.QueryParams) > 0 {
		q := parsedURL.Query()
		for key, value := range apiReq.QueryParams {
			q.Set(key, value)
		}
		parsedURL.RawQuery = q.Encode()
	}

	// Prepare the request body if provided.
	var bodyReader io.Reader
	if len(apiReq.Body) > 0 {
		bodyReader = bytes.NewBuffer(apiReq.Body)
	}

	// Create the HTTP request.
	req, err := http.NewRequest(method, parsedURL.String(), bodyReader)
	if err != nil {
		return input, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers if provided.
	if len(apiReq.Headers) > 0 {
		for key, value := range apiReq.Headers {
			req.Header.Set(key, value)
		}
	}

	// If a body is provided and Content-Type is not set, default to application/json.
	if len(apiReq.Body) > 0 && req.Header.Get("Content-Type") == "" {
		req.Header.Set("Content-Type", "application/json")
	}

	// Set authentication if provided.
	if apiReq.Auth != nil {
		switch strings.ToLower(apiReq.Auth.Type) {
		case "basic":
			if apiReq.Auth.Username == "" || apiReq.Auth.Password == "" {
				return input, errors.New("basic auth requires username and password")
			}
			req.SetBasicAuth(apiReq.Auth.Username, apiReq.Auth.Password)
		case "bearer":
			if apiReq.Auth.Token == "" {
				return input, errors.New("bearer auth requires token")
			}
			req.Header.Set("Authorization", "Bearer "+apiReq.Auth.Token)
		default:
			return input, fmt.Errorf("unsupported auth type: %s", apiReq.Auth.Type)
		}
	}

	// Create an HTTP client with optional timeout.
	client := &http.Client{}
	if apiReq.Timeout > 0 {
		client.Timeout = time.Duration(apiReq.Timeout) * time.Second
	}

	// Send the HTTP request.
	resp, err := client.Do(req)
	if err != nil {
		return input, fmt.Errorf("failed to send request: %w", err)
	}

	defer resp.Body.Close()

	// Read the response body.
	buf := new(bytes.Buffer)
	if _, err := buf.ReadFrom(resp.Body); err != nil {
		return input, fmt.Errorf("failed to read response body: %w", err)
	}

	// make response

	response := "Run API : " + apiReq.Method + " " + apiReq.URL + "\n"
	response += "Response: " + "\n"
	response += buf.String()
	return RunNodeOutput{
		Format:      "text",
		Data:        response,
		FlowControl: input.FlowControl,
	}, nil

}

type GetWithTokenParams struct {
	Pass        bool              `json:"pass"`
	URL         string            `json:"url"`
	QueryParams map[string]string `json:"query_params"`
}

var getWithTokenJSONSchema = `{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "$id": "http://example.com/default_node.schema.json",
  "title": "get-with-token-param",
  "description": "Schema for getWithToken function",
  "properties": {
    "pass": {
      "description": "If no action is taken, then the value of pass is true.",
      "type": "boolean",
      "additionalProperties": false
    },
    "query_params": {
      "type": "object",
      "additionalProperties": false
    },
    "url": {
      "description": "The API's URL",
      "type": "string",
      "additionalProperties": false
    }
  },
  "required": [
    "pass",
    "url"
  ],
  "additionalProperties": false
}`

var GetWithTokenInfo = NodeFuncInfo{
	Name:        "GET",
	Description: "Send a GET request with a bearer token. the token should be store in the environment variable AAAGENT_API_TOKEN or config file api_token field.",
	Function:    GetWithToken,
	Schema:      getWithTokenJSONSchema,
}

func GetWithToken(input RunNodeOutput, query string) (RunNodeOutput, error) {
	jsonBytes, err := json.Marshal(input.Data)
	if err != nil {
		return input, fmt.Errorf("failed to marshal data: %w", err)
	}

	var param GetWithTokenParams
	if err := json.Unmarshal(jsonBytes, &param); err != nil {
		msg := fmt.Sprintf("Input %s is not a valid parameter for getWithToken function", query)
		return input, fmt.Errorf("%s: %v", msg, err)
	}

	if !param.Pass {
		return RunNodeOutput{
			Format:      "text",
			Data:        "",
			FlowControl: input.FlowControl,
		}, nil
	}

	res, err := getWithToken(param.URL, param.QueryParams)
	if err != nil {
		return RunNodeOutput{
			Format:      "text",
			Data:        fmt.Sprintf("Failed to get data: %v", err),
			FlowControl: input.FlowControl,
		}, nil
	}

	return RunNodeOutput{
		Format:      "text",
		Data:        res,
		FlowControl: input.FlowControl,
	}, nil
}

// getWithToken performs an HTTP GET request using the provided URL and query parameters.
// It adds an Authorization header with the bearer token (from environment variable or viper)
// and returns the response body.
func getWithToken(urlStr string, queryParams map[string]string) (string, error) {
	// Validate required URL.
	if urlStr == "" {
		return "", errors.New("URL is required")
	}

	// Retrieve token from environment variable AAAGENT_API_TOKEN, or from viper.
	token := os.Getenv("AAAGENT_API_TOKEN")
	if token == "" {
		token = viper.GetString("api_token")
	}
	q.Q("API token: ", token)

	// Parse the URL.
	u, err := url.Parse(urlStr)
	if err != nil {
		return "", fmt.Errorf("failed to parse URL: %w", err)
	}

	// If URL is missing the scheme or host, use AAAGENt_API_HOST or api_host config as the base
	if u.Scheme == "" || u.Host == "" {
		// Retrieve the API host.
		host := os.Getenv("AAAGENT_API_HOST")
		if host == "" {
			host = viper.GetString("api_host")
		}
		if host == "" {
			return "", errors.New("API host is required when using a relative URL")
		}
		// Parse the base host URL.
		base, err := url.Parse(host)
		if err != nil {
			return "", fmt.Errorf("failed to parse API host: %w", err)
		}
		// Resolve the relative URL against the base URL.
		u = base.ResolveReference(u)
	}

	// Add query parameters.
	qry := u.Query()
	for key, value := range queryParams {
		qry.Set(key, value)
	}
	u.RawQuery = qry.Encode()

	client := &http.Client{}
	q.Q("API host: ", u.String())

	// Create the GET request.
	req, err := http.NewRequest("GET", u.String(), nil)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	// Set the Authorization header if a token is available.
	if token != "" {
		req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", token))
	}

	// Execute the request.
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	// Check for a non-successful HTTP status.
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("non-OK HTTP status: %s, body: %s", resp.Status, string(bodyBytes))
	}

	// Read the response body.
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	return string(bodyBytes), nil
}
