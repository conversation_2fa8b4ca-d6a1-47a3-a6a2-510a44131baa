import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";
import { persist } from "zustand/middleware";

/**
 * Initial state for the Script store
 * @type {Object}
 */
const initialState = {
  // Command response data
  cmdResponse: [],

  // Command input
  inputCmd: "",

  // Command flags
  cmdFlags: {
    cc: "", // client
    ct: "", // tag
    ck: "", // kind
    cns: false, // no syslog
    cno: false, // no overwrite
  },
};

/**
 * Script store for managing script commands and results
 * @type {import("zustand").UseBoundStore}
 */
export const useScriptStore = create(
  devtools(
    persist(
      immer((set) => ({
        // Initial state
        ...initialState,

        /**
         * Set command response data
         * @param {Array} response - Command response data
         */
        setCmdResponse: (response) =>
          set((state) => {
            state.cmdResponse = response;
          }),

        /**
         * Set command input text
         * @param {string} input - Command input text
         */
        setInputCmd: (input) =>
          set((state) => {
            state.inputCmd = input;
          }),

        /**
         * Set command flags
         * @param {Object} flags - Command flags
         */
        setCmdFlags: (flags) =>
          set((state) => {
            state.cmdFlags = flags;
          }),

        /**
         * Update specific command flag
         * @param {string} key - Flag key
         * @param {any} value - Flag value
         */
        updateCmdFlag: (key, value) =>
          set((state) => {
            state.cmdFlags[key] = value;
          }),

        /**
         * Clear all command flags
         */
        clearCmdFlags: () =>
          set((state) => {
            state.cmdFlags = initialState.cmdFlags;
          }),

        /**
         * Clear command input
         */
        clearInputCmd: () =>
          set((state) => {
            state.inputCmd = initialState.inputCmd;
          }),
      })),
      {
        name: "nimbl-script-store",
        partialize: (state) => ({
          // Only persist these fields
          inputCmd: state.inputCmd,
          cmdFlags: state.cmdFlags,
        }),
      }
    ),
    {
      name: "script-store",
      enabled: process.env.NODE_ENV === "development",
    }
  )
);

/**
 * Legacy function for compatibility with the app store
 * @deprecated Use useScriptStore directly instead
 */
export const createScriptStore = (set) => ({
  scripts: {
    ...initialState,
    setCmdResponse: (response) =>
      set((state) => {
        state.scripts.cmdResponse = response;
      }),
    setInputCmd: (input) =>
      set((state) => {
        state.scripts.inputCmd = input;
      }),
    setCmdFlags: (flags) =>
      set((state) => {
        state.scripts.cmdFlags = flags;
      }),
    clearCmdFlags: () =>
      set((state) => {
        state.scripts.cmdFlags = initialState.cmdFlags;
      }),
    clearInputCmd: () =>
      set((state) => {
        state.scripts.inputCmd = initialState.inputCmd;
      }),
  },
});
