import { create } from "zustand";
import { createJSONStorage, devtools, persist } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";

/**
 * @typedef {Object} AuthUser
 * @property {string} id - User ID
 * @property {string} username - Username
 * @property {string} email - User email
 */

/**
 * @typedef {Object} AuthState
 * @property {string} token - Authentication token
 * @property {AuthUser} user - User information
 * @property {string} role - User role
 * @property {string|null} sessionid - Session ID
 * @property {(data: Partial<AuthState>) => void} setAuthData - Set auth data
 * @property {() => void} clearAuthData - Clear auth data
 */

/** @type {AuthState} */
const initialState = {
  token: "",
  user: "",
  role: "",
  sessionid: null,
};

/**
 * Authentication store for managing user session and permissions
 * @returns {AuthState} Auth store state and actions
 */
export const useAuthStore = create(
  devtools(
    immer(
      persist(
        (set) => ({
          ...initialState,
          setAuthData: (data) =>
            set((state) => {
              Object.keys(data).forEach((key) => {
                if (key in initialState) {
                  state[key] = data[key];
                }
              });
            }),
          clearAuthData: () => set(initialState),
        }),
        {
          name: "nimbl-auth",
          storage: createJSONStorage(() => sessionStorage),
          partialize: (state) => ({
            token: state.token,
            user: state.user,
            role: state.role,
            sessionid: state.sessionid,
          }),
        }
      )
    ),
    {
      name: "AuthStore",
      enabled: process.env.NODE_ENV === "development",
    }
  )
);
