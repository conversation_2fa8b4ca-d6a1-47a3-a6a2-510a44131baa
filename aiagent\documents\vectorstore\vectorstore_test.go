package vectorstore

import (
	"bytes"
	"context"
	"mnms/llm"
	"os"
	"runtime"
	"testing"

	"github.com/austinjan/chromem-go"
)

// TestSerialize is a function that serializes a ChromemeStore
func TestSerializeChromemStore(t *testing.T) {
	apikey := os.Getenv("OPENAI_API_KEY")
	if apikey == "" {
		t.Skip("OPENAI_API_KEY is not set")
	}
	lmSettings := llm.GPTSettings()
	lmSettings.APIKey = apikey
	store := NewChromeme(lmSettings)
	if store == nil {
		t.Fatal("store is nil")
	}
	// Add some data to the store
	collection, err := store.DB.GetOrCreateCollection("test",
		nil,
		chromem.NewEmbeddingFuncOpenAI(lmSettings.GetAPIKey(), chromem.EmbeddingModelOpenAI2Ada))
	if err != nil {
		t.Fatal(err)
	}
	err = collection.AddDocuments(context.Background(), []chromem.Document{
		{
			ID:      "1",
			Content: "This is a test on ID-1",
			Metadata: map[string]string{
				"data": "value1",
			},
		},
		{
			ID:      "2",
			Content: "This is a test on ID-2",
			Metadata: map[string]string{
				"data": "value2",
			},
		},
	}, runtime.NumCPU())
	if err != nil {
		t.Fatal(err)
	}
	// Serialize the store
	buffer := bytes.Buffer{}

	err = store.Serialize(&buffer)
	if err != nil {
		t.Fatal(err)
	}
	// Deserialize the store
	store2, err := DeserializeLocalChromemeStore(&buffer, "")
	if err != nil {
		t.Fatal(err)
	}
	if store2 == nil {
		t.Fatal("store2 is nil")
	}
	// Check if the data is the same
	collection2 := store2.DB.GetCollection("test", chromem.NewEmbeddingFuncOpenAI(lmSettings.GetAPIKey(), chromem.EmbeddingModelOpenAI2Ada))

	if collection2 == nil {
		t.Fatal("collection2 is nil")
	}
	results, err := collection2.Query(context.Background(), "This is a test on ID-1", 1, nil, nil)
	if err != nil {
		t.Fatal(err)
	}
	if len(results) != 1 {
		t.Fatalf("expected 1 result, got %d", len(results))
	}
	if results[0].ID != "1" {
		t.Fatalf("expected ID-1, got %s", results[0].ID)
	}
	if results[0].Content != "This is a test on ID-1" {
		t.Fatalf("expected This is a test on ID-1, got %s", results[0].Content)
	}
	t.Log(store2.LLMSettings)
}
