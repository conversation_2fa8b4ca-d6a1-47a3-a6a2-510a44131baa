package service

import (
	"fmt"
	"sync"
	"time"
)

// DetectJob
type DetectJob struct {
	Cmd       string    `json:"cmd"`
	StartTime time.Time `json:"start_time"`
}

// Timeout check if the job is timeout
func (j *DetectJob) Timeout(timeout time.Duration) bool {
	return time.Now().Sub(j.StartTime) > timeout
}

// DetectJobQueue
type DetectJobQueue struct {
	Jobs []DetectJob `json:"jobs"`
	lock sync.Mutex
}

// NewDetectJobQueue
func NewDetectJobQueue() *DetectJobQueue {
	q := &DetectJobQueue{}
	q.Jobs = make([]DetectJob, 0)
	q.lock = sync.Mutex{}
	return q
}

// EnqueueCmd
func (q *DetectJobQueue) EnqueueCmd(cmd string) {
	q.lock.Lock()
	defer q.lock.Unlock()
	job := DetectJob{
		Cmd:       cmd,
		StartTime: time.Now(),
	}
	q.Jobs = append(q.Jobs, job)
}

// Enqueue
func (q *DetectJobQueue) Enqueue(job DetectJob) {
	q.lock.Lock()
	defer q.lock.Unlock()
	q.Jobs = append(q.Jobs, job)
}

// Dequeue
func (q *DetectJobQueue) Dequeue() *DetectJob {
	q.lock.Lock()
	defer q.lock.Unlock()
	if len(q.Jobs) == 0 {
		return nil
	}
	job := q.Jobs[0]
	q.Jobs = q.Jobs[1:]
	return &job
}

var gDetectJobs = NewDetectJobQueue()

// GenSyslogGenurlCmd is a function to generate a log file from syslog. return command's key
func GenSyslogGenurlCmd(interval time.Duration) string {
	startTime := time.Now().Add(-interval)
	endTime := time.Now()
	startTimeStr := startTime.Format("2006/01/02 15:04:05")
	endTimeStr := endTime.Format("2006/01/02 15:04:05")
	cmd := fmt.Sprintf(`syslog geturl all %s %s`, startTimeStr, endTimeStr)
	return cmd
}

// EnqueuDetectCmd is a function to enqueue a detect job
func EnqueuDetectCmd(cmd string) {
	gDetectJobs.EnqueueCmd(cmd)
}

// EnqueueDetectJob is a function to enqueue a detect job
func EnqueueDetectJob(job DetectJob) {
	gDetectJobs.Enqueue(job)
}

// DequeueDetectJob is a function to dequeue a detect job
func DequeueDetectJob() *DetectJob {
	return gDetectJobs.Dequeue()
}
