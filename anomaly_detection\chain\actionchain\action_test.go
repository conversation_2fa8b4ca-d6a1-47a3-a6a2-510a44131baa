package actionchain

import (
	"encoding/json"
	"fmt"
	"mnms/encrypt"
	"mnms/llm"

	"strconv"
	"testing"
)

// ConvertMapAnyToMapString converts a map[string]any to a map[string]string
func ConvertMapAnyToMapString(AMap map[string]any) (map[string]string, error) {
	var AStringMap map[string]string = make(map[string]string)
	for key, value := range AMap {
		switch v := value.(type) {
		case string:
			AStringMap[key] = v
		case int, int32, int64, float64:
			AStringMap[key] = fmt.Sprintf("%v", v) // Use %v to format any type as a string
		case bool:
			AStringMap[key] = strconv.FormatBool(v)
		default:
			// Optionally handle other types or log an error
			return nil, fmt.Errorf("field %s has unsupported type: %T", key, v)
		}
	}
	return AStringMap, nil
}

// TestGoJSONMarshal tests the GoJSONMarshal function
func TestGoJSONMarshal(t *testing.T) {
	AJsonText := `
{
	"string": "string",
	"int": 1,
	"float": 1.1,
	"bool": true
}	`

	//try to unmarshal the json text to map[string]any
	var AMap map[string]any = make(map[string]any)
	err := json.Unmarshal([]byte(AJsonText), &AMap)
	if err != nil {
		t.Errorf("error unmarshalling json: %v", err)
	}
	// t.Log every filed's data type
	for key, value := range AMap {
		t.Logf("%s: %T", key, value)
	}
	// convert map[string]any to map[string]string
	AStringMap, err := ConvertMapAnyToMapString(AMap)
	if err != nil {
		t.Errorf("error converting map[string]any to map[string]string: %v", err)
	}
	t.Log(AStringMap)

}

// TestTaskExecutionDescription tests the DescribeTaskExecution function
func TestTaskExecutionDescription(t *testing.T) {
	jsonText := `{
  "task": "Change device's IP address, subnet mask, default gateway, hostname, and DHCP setting",
  "actions": [
    {
      "type": "command",
      "action": "config network set 00-12-34-56-78-9a *********** *********** ************* ***********54 test-switch 1"
    },
    {
      "type": "command",
      "action": "agent devinfo send 00-12-34-56-78-9a"
    }
  ],
  "validation": {
    "description": "check all settings were set correctly",
    "action": {
      "type": "API",
      "endpoint": "/api/vi/devices",
      "method": "GET",
      "query-params": "dev=00-12-34-56-78-9a"
    },
    "expects": {
      "ip": "***********",
      "mask": "*************",
      "host": "test-switch",
      "dhcp": "1"
    }
  }
}`
	var task Task
	err := json.Unmarshal([]byte(jsonText), &task)
	if err != nil {
		t.Errorf("error unmarshalling json: %v", err)
	}
	t.Log(DescribeTaskExecution(task))
}

// TestFirstline tests the Firstline function
func TestFirstline(t *testing.T) {
	for _, doc := range supportedTasks {
		// msg is firs line of the doc
		msg := firstLine(doc)
		t.Log(msg)
	}
}

// TestClarifyIntent tests LLM to clarify input's intent
func TestClarifyIntent(t *testing.T) {
	key, err := encrypt.DecryptedKey(llm.DemoAPIKey)
	if err != nil {
		t.Fatal(err)
	}
	gptsettings := llm.GPTSettings()
	gptsettings.APIKey = key
	gptsettings.Model = "gpt-4o"
	mod, err := llm.NewLLMClient(gptsettings)
	if err != nil {
		t.Fatal(err)
	}

	input := `I would like to setup following devices: 
{
 "old-ip": "***********",
 "new-ip": "***********",
 "mask": "*************",
 "gateway": "***********54",
 "hostname": "test-switch",
 "dhcp": 1,
 "mac": "00-12-34-56-78-9a"
}
{
 "old-ip": "************", 
 "new-ip": "************", 
 "mask": "*************",
 "gateway": "***********54",
 "hostname": "test-switch2", 
 "dhcp": 1,
 "mac": "00-12-34-56-78-91" 
}
 `

	ret, err := ClarifyInput(mod, input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(input)
	t.Log("Result: ")
	t.Log(ret)

	input = `Please write following settings to the device which has MAC address 00-12-34-56-78-9a: 
   new ip: *********
	 mask: *************
	`

	ret, err = ClarifyInput(mod, input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(input)
	t.Log("Result: ")
	t.Log(ret)

}
