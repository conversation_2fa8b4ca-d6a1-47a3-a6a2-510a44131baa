/**
 * Retrieves saved topology positions from localStorage
 * @returns {Array<{id: string, style: {x: number, y: number}}>}
 */
export const getSavedPositions = () => {
  try {
    const STORAGE_KEY = "nimbl-topologyPositions";
    const saved = localStorage.getItem(STORAGE_KEY);
    return saved ? JSON.parse(saved) : {};
  } catch (error) {
    console.error("Failed to retrieve saved positions:", error);
    return {};
  }
};

/**
 * Processes topology data with saved positions
 * @param {Array} data - Raw topology data
 * @param {string} selectedServices - Selected service type
 * @returns {{nodes: Array, edges: Array}} Processed topology data
 */
export const filteredTopologyData = (data, selectedServices) => {
  if (!data?.length) {
    return { nodes: [], edges: [] };
  }

  try {
    // Create lookup map for saved positions
    const savedPositionsData = getSavedPositions()[selectedServices] || [];
    const savedPositions = new Map(
      Array.isArray(savedPositionsData)
        ? savedPositionsData.map((pos) => [pos.id, pos])
        : []
    );
    // Process nodes with saved positions
    const nodeData = data.map((item) => ({
      ...item,
      macAddress: item.id,
      ...(savedPositions.get(item.id) || {}),
    }));

    // Create Set for efficient node lookup
    const nodeIds = new Set(nodeData.map((item) => item.id));

    // Process and deduplicate links
    const uniqueLinkData = new Map();
    data.forEach((device) => {
      if (!Array.isArray(device.linkData)) return;

      device.linkData.forEach((link) => {
        const { edge, source, target, blockedPort } = link;
        const isValidLink = nodeIds.has(source) && nodeIds.has(target);

        if (
          isValidLink &&
          (!uniqueLinkData.has(edge) || blockedPort === "true")
        ) {
          uniqueLinkData.set(edge, link);
        }
      });
    });

    return {
      nodes: nodeData,
      edges: Array.from(uniqueLinkData.values()),
    };
  } catch (error) {
    console.error("Error processing topology data:", error);
    return { nodes: [], edges: [] };
  }
};

export const getFilename = (title) => {
  const now = new Date();
  const datePart = now.toLocaleDateString("en-GB").split("/").join("-");
  const timePart = now.toLocaleTimeString("en-GB").split(":").join("-");
  return `${title}_${datePart}_${timePart}`;
};

export const createManualTpologyData = (values) => {
  const id = values.mac.trim();
  const linkData = values.linkData.map((item) => ({
    source: id,
    target: item.target.trim(),
    sourcePort: item.sourcePort.trim(),
    targetPort: item.targetPort.trim(),
    blockedPort: "false",
    linkType: "manual",
    edge: createEdge(id, item.target.trim()),
  }));
  return { id, topoType: "manual", linkData };
};

export const createEdge = (source, target) => {
  if (source < target) {
    return source + "_" + target;
  }
  return target + "_" + source;
};
