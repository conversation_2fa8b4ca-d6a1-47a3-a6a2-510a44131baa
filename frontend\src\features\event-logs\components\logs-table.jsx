import React from "react";
import { useLogsTable } from "../hooks/useLogsTable";
import ProTable from "@ant-design/pro-table";
import { TABLE_CONFIG } from "../constant";
import ExportData from "../../../components/export-data/export-data";
import { theme } from "antd";
import { LogPageFilter } from "./log-filter";

const LogsTable = () => {
  const { token } = theme.useToken();
  const {
    filteredData,
    setInputSearch,
    columns,
    handleSetParams,
    isFetching,
    refetch,
    resetParams,
  } = useLogsTable();

  const tableProps = {
    cardProps: { style: { boxShadow: token?.Card?.boxShadow } },
    headerTitle: "Log List",
    columns,
    dataSource: filteredData,
    rowKey: "uuid",
    size: "small",
    options: { reload: refetch, fullScreen: false },
    loading: isFetching,
    pagination: {
      ...TABLE_CONFIG.pagination,
      total: filteredData.length,
    },
    scroll: TABLE_CONFIG.scroll,
    toolbar: {
      search: {
        allowClear: true,
        onSearch: setInputSearch,
        onClear: () => setInputSearch(""),
      },
      actions: [
        <LogPageFilter refetch={handleSetParams} reset={resetParams} />,
        <ExportData
          key="export"
          Columns={columns}
          DataSource={filteredData}
          title="User_List"
        />,
      ].filter(Boolean),
    },
    search: false,
    dateFormatter: "string",
    columnsState: TABLE_CONFIG.columnsState,
  };
  return <ProTable {...tableProps} />;
};

export default LogsTable;
