# IDPS Service Module

This module provides a clean, organized structure for handling all IDPS-related functionality in the application.

## Structure

```
frontend/src/services/idps/
├── index.js           # Main exports
├── idpsService.js     # Core service class with API methods
├── idpsMutations.js   # React Query mutations using handleCommandExecution
├── idpsQueries.js     # React Query queries
└── README.md          # This documentation
```

## Features

### Service Class (`idpsService.js`)
- `IdpsService.getReport()` - Fetch IDPS report data
- `IdpsService.executeCommand()` - Execute IDPS commands
- `IdpsService.importRules()` - Import rules for a service
- `IdpsService.deleteRule()` - Delete a rule from a service
- `IdpsService.searchFile()` - Search files in records
- `IdpsService.deleteRecords()` - Delete records with various options
- `IdpsService.filterEvents()` - Filter events by filename and date range

### Mutations (`idpsMutations.js`)
All mutations use `handleCommandExecution` for consistent command handling:
- `useImportRules()` - Import rules mutation
- `useDeleteRule()` - Delete rule mutation
- `useSearchFile()` - Search file mutation
- `useDeleteRecords()` - Delete records mutation
- `useFilterEvents()` - Filter events mutation

### Queries (`idpsQueries.js`)
- `useIdpsReport()` - Fetch IDPS report data with auto-refresh
- `useProcessedIdpsData()` - Process raw IDPS data for specific service

## Command Structure

All IDPS commands follow this structure:
```javascript
[{
  command: "idps [action] [parameters]",
  client: "serviceName"
}]
```

### Supported Commands

1. **Rules Management**:
   - `idps rules import ${url}`
   - `idps rules delete ${ruleName}`

2. **Records Management**:
   - `idps records delete` (all records)
   - `idps records delete -d ${date}` (by date)
   - `idps records delete -f ${filename} -d ${date}` (by file & date)

3. **Search Operations**:
   - `idps records search -f ${filename} -st ${startDate}-00:00 -et ${endDate}-23:59`

## Usage Example

```javascript
import { 
  useIdpsReport, 
  useProcessedIdpsData, 
  useImportRules 
} from '../../../services/idps';

const MyComponent = () => {
  const { data: idpsData, isLoading } = useIdpsReport();
  const { rulesData, eventsData } = useProcessedIdpsData(idpsData, selectedService);
  
  const importRulesMutation = useImportRules(() => {
    // Success callback
    console.log('Rules imported successfully');
  });

  const handleImport = (url, serviceName) => {
    importRulesMutation.mutate({ url, serviceName });
  };

  return (
    // Component JSX
  );
};
```

## Benefits

1. **Separation of Concerns**: Clear separation between API calls, mutations, and queries
2. **Consistent Command Handling**: All commands use `handleCommandExecution`
3. **Reusability**: Service methods can be used across different components
4. **Type Safety**: Better TypeScript support with defined interfaces
5. **Error Handling**: Centralized error handling through `handleCommandExecution`
6. **Testing**: Easier to mock and test individual service methods

## Migration Notes

The refactoring replaced the previous inline mutations in `useIdpsData.js` with:
- Dedicated service class for API calls
- Reusable mutation hooks
- Consistent command execution pattern
- Better error handling and notifications

All existing functionality remains the same, but with improved code organization and maintainability.
