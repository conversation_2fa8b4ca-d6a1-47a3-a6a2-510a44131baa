package anomstore

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"mnms/anomaly_detection/store/algo"
	"testing"
)

func TestCosinSimilarity(t *testing.T) {
	a := []float32{1, 2, 3}
	b := []float32{1, 2, 3}
	cosin := algo.CosineSimilarityCalculator{}
	result := cosin.Calculate(a, b)
	expected := float64(0)
	// Since a and b are identical, their cosine similarity should be 1
	if diff := float32(math.Abs(float64(result) - float64(expected))); diff > 1e-6 {
		t.Errorf("Unexpected result: got %v, want %v, diff: %v", result, expected, diff)
	}

	// Add other test
	c := []float32{1, 2, 2.2}
	result = cosin.Calculate(a, c)
	expected = float64(0.01168268)
	if diff := float32(math.Abs(float64(result) - float64(expected))); diff > 1e-6 {
		t.<PERSON><PERSON><PERSON>("Unexpected result: got %v, want %v, diff: %v", result, expected, diff)
	}

	// Test l2
	l2 := algo.L2DistanceCalculator{}
	result = l2.Calculate(a, b)
	expected = float64(0)
	if diff := float32(math.Abs(float64(result) - float64(expected))); diff > 1e-6 {
		t.Errorf("Unexpected result: got %v, want %v, diff: %v", result, expected, diff)
	}

	result = l2.Calculate(a, c)
	expected = float64(0.799999)
	if diff := float32(math.Abs(float64(result) - float64(expected))); diff > 1e-6 {
		t.Errorf("Unexpected result: got %v, want %v, diff: %v", result, expected, diff)
	}

}

// TestVectorsList tests the VectorsList struct
func TestVectorsList(t *testing.T) {
	aList := VectorList{
		Items:  make(map[int64]Vector),
		NextID: 0,
	}

	// Add some
	for i := 0; i < 10; i++ {
		record := Vector{
			Vector: []float32{1, 2, 3, float32(i)},
			Text:   fmt.Sprintf("1, 2, 3, %d", i),
		}
		_, err := aList.Insert(&record)
		if err != nil {
			t.Fatal(err)
		}

	}

	// Get some
	for i := 0; i < 10; i++ {
		record, err := aList.Get(int64(i))
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
		}

		expectData := fmt.Sprintf("1, 2, 3, %d", i)
		if string(record.Text) != expectData {
			t.Errorf("Unexpected record: got %v, want %v", string(record.Text), expectData)
		}
	}

	// Update some
	for i := 0; i < 5; i++ {
		record := Vector{
			Vector: []float32{1, 2, 3, float32(i * 2)},
			Text:   fmt.Sprintf("1, 2, 3, %d", i*2),
		}
		insertedIdx, err := aList.Update(int64(i), &record)
		if err != nil {
			t.Fatal(err)
		}
		if insertedIdx != int64(i) {
			t.Errorf("Unexpected record: got %v, want %v", insertedIdx, i)
		}
		// validate
		getrecord, err := aList.Get(int64(i))
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
		}

		expectData := fmt.Sprintf("1, 2, 3, %d", i*2)
		if string(record.Text) != expectData {
			t.Errorf("Unexpected record: got %v, want %v", string(getrecord.Text), expectData)
		}

	}

}

// TestSearchSimilarVectors tests the search similar vectors
func TestSearchSimilarVectors(t *testing.T) {
	// create a vector list
	var vectorsList = VectorList{
		Items:  make(map[int64]Vector),
		NextID: 0,
	}

	for i := 0; i < 10; i++ {
		record := Vector{
			Vector: []float32{1.45, float32(i), 10.223, float32(i) * 0.5},
			Text:   fmt.Sprintf("1.45, %f, 10.223, %f", float32(i), float32(i)*0.5),
		}
		_, err := vectorsList.Insert(&record)
		if err != nil {
			t.Fatal(err)
		}
	}
	r := Vector{
		Vector: []float32{1.45, 4, 10.223, 1.99},
	}

	l2Cal := algo.L2DistanceCalculator{}
	retList, err := vectorsList.SearchSimilar(context.Background(), r.Vector, l2Cal, 3)
	if err != nil {
		t.Fatal(err)
	}
	for _, v := range retList {
		t.Log("candidate: ", v)
	}

	t.Log("cosin similarity========")
	cosinCal := algo.CosineSimilarityCalculator{}
	retList, err = vectorsList.SearchSimilar(context.Background(), r.Vector, cosinCal, 3)
	if err != nil {
		t.Fatal(err)
	}
	for _, v := range retList {
		t.Log("candidate: ", v)
	}
}

// TestVectorMarshal tests the vector marshal
func TestVectorMarshal(t *testing.T) {
	vectorList := VectorList{
		Items:  make(map[int64]Vector),
		NextID: 0,
	}
	for i := 0; i < 10; i++ {
		record := Vector{
			Vector: []float32{1, 2, 3, float32(i)},
			Text:   fmt.Sprintf("1, 2, 3, %d", i),
		}

		_, err := vectorList.Insert(&record)
		if err != nil {
			t.Fatal(err)
		}
	}
	jsonByte, err := json.Marshal(&vectorList)
	if err != nil {
		t.Fatal(err)
	}

	var anotherVectorList VectorList
	err = json.Unmarshal(jsonByte, &anotherVectorList)
	if err != nil {
		t.Fatal(err)
	}
	for k, v := range anotherVectorList.Items {
		oriVal := vectorList.Items[k]
		for i, val := range v.Vector {
			if val != oriVal.Vector[i] {
				t.Errorf("Unexpected value: got %v, want %v", val, oriVal.Vector[i])
			}
			if v.Text != oriVal.Text {
				t.Errorf("Unexpected value: got %v, want %v", v.Text, oriVal.Text)
			}
			if v.Reason != oriVal.Reason {
				t.Errorf("Unexpected value: got %v, want %v", v.Reason, oriVal.Reason)
			}
			if v.Normal != oriVal.Normal {
				t.Errorf("Unexpected value: got %v, want %v", v.Normal, oriVal.Normal)
			}
		}
	}

}

// TestReadRAGs tests the RAGToReader
func TestReadRAGs(t *testing.T) {
	vecs := &VectorList{
		Items:  make(map[int64]Vector),
		NextID: 0,
	}
	for i := 0; i < 10; i++ {
		normal := i%2 == 0
		record := Vector{
			Vector: []float32{1, 2, 3, float32(i)},
			Text:   fmt.Sprintf("1, 2, 3, %d", i),
			Normal: normal,
		}
		_, err := vecs.Insert(&record)
		if err != nil {
			t.Fatal(err)
		}
	}
	ragData, err := SerializeToCSV(vecs, ".*")
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	// WriteCSVToVectorList
	newList, err := DeserializeFromCSV(ragData)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	t.Log("newList: ", newList)
	for _, v := range newList.Items {
		t.Log("vector: ", v)

	}
}
