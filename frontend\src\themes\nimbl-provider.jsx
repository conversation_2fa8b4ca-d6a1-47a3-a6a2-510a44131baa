import React from "react";
import enUS from "antd/locale/en_US";
import { App, ConfigProvider, theme } from "antd";
import { ThemeProvider } from "antd-style";
import { useSettingStore } from "../store/setting-store";
import GlobalStyle from "./GlobalStyle";
import { customDarkAlgorithm, customLightAlgorithm } from "./algorithms";
import { componentTokens } from "./component-tokens";

/**
 * @typedef {Object} NimblProviderProps
 * @property {React.ReactNode} children - Child components
 */

/**
 * Component wrapper for custom theme configuration
 * @param {NimblProviderProps} props - Component props
 */
const ComponentCustomThemeWrapper = React.memo(({ children }) => {
  const { token } = theme.useToken();

  return (
    <ConfigProvider
      locale={enUS}
      theme={{
        inherit: true,
        components: {
          ...componentTokens,
          Table: {
            ...componentTokens.Table,
            colorFillAlter: token.colorPrimaryBg,
          },
        },
      }}
    >
      <GlobalStyle />
      <App>{children}</App>
    </ConfigProvider>
  );
});

ComponentCustomThemeWrapper.displayName = "ComponentCustomThemeWrapper";

/**
 * Main theme provider for the Network Management System
 * @param {NimblProviderProps} props - Component props
 */
const NimblProvider = ({ children }) => {
  const { mode, colorPrimary } = useSettingStore();
  const systemTheme = window.matchMedia("(prefers-color-scheme: dark)").matches
    ? "dark"
    : "light";

  const themeConfig = React.useMemo(
    () => ({
      cssVar: true,
      hashed: false,
      token: {
        colorPrimary,
        borderRadius: 4,
        fontFamily: '"Poppins", sans-serif',
      },
      algorithm: mode === "dark" ? customDarkAlgorithm : customLightAlgorithm,
    }),
    [mode, colorPrimary]
  );

  return (
    <ThemeProvider
      defaultAppearance={mode === "auto" ? systemTheme : mode}
      themeMode={mode}
      theme={themeConfig}
    >
      <ComponentCustomThemeWrapper>{children}</ComponentCustomThemeWrapper>
    </ThemeProvider>
  );
};

export default NimblProvider;
