package anomstore

import (
	"context"
	"fmt"
	"mnms/anomaly_detection/statistic"
	"mnms/llm"
	"strings"

	"github.com/qeof/q"
)

/*
The file vectorrecord.go contains functions to manage and manipulate vector records.
*/

// abbreviateVector abbreviate vector to string
func abbreviateVector(vector []float32, maxElements int) string {
	var sb strings.Builder
	sb.WriteString("[")
	for i, v := range vector {
		if i > 0 {
			sb.WriteString(", ")
		}
		sb.WriteString(fmt.Sprintf("%v", v))
		if i == maxElements-1 && len(vector) > maxElements {
			sb.WriteString(", ...")
			break
		}
	}
	sb.WriteString("]")
	return sb.String()
}

// Vector represents a PostgreSQL schema for storing vector data.
type Vector struct {
	Vector []float32 `json:"vector" mapstructure:"vector"` // A slice of floating-point numbers representing the vector.
	Reason string    `json:"reason" mapstructure:"reason"` // Reason for the vector
	Normal bool      `json:"normal" mapstructure:"normal"` // Normal or not
	Text   string    `json:"text" mapstructure:"text"`     // Text for the vector
}

// ReCalEmbeddings re-calculate embeddings
func (v *Vector) ReCalEmbeddings() error {
	llmSettings, err := statistic.GetLargeLanguageModelFromAnomalyStatistics()
	if err != nil {
		q.Q("get llm settings error: ", err)
		return err
	}
	llm, err := llm.NewLLMClient(llmSettings)
	if err != nil {
		q.Q("connect to LLM error: ", err)
		return err
	}

	embeddings, err := llm.Embedding(context.Background(), v.Text)
	if err != nil {
		q.Q("get embedding error: ", err)
		return err
	}

	v.Vector = embeddings

	return nil
}

// CompareVectorRecordVector compare two  vector, return true if they are equal
func CompareVectorRecordVector(v1 []float32, v2 []float32) bool {
	if len(v1) != len(v2) {
		return false
	}
	for i, v := range v1 {
		if v != v2[i] {
			return false
		}
	}
	return true
}
