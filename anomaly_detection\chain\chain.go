package chain

import (
	"context"
	"encoding/json"
	"mnms/anomaly_detection/chain/prompt"
	"mnms/anomaly_detection/def"
	"mnms/anomaly_detection/retriever"
	"mnms/anomaly_detection/store"
	"mnms/llm"
	"strings"

	"github.com/qeof/q"
	"github.com/vmihailenco/msgpack/v5"
)

type ChainRunner interface {
	// Run(ctx, prompts, options) runs the chain with the given prompts and options.
	Run(context.Context, string, ...OptionAssigner) (string, error)
}

// BasicChain is a simple chain that runs a single function.
type BasicChain struct {
	Retriever   def.Retriever
	Store       def.Store
	m           llm.LLMClient
	systemPrmpt string
	threasold   float32
	candidates  int
}

// NewBasicChain creates a new instance of BasicChain.
func NewBasicChain(m llm.LLMClient, systemPrmpt string, threasold float32, candidates int) *BasicChain {
	s := store.NewStandardStore()
	r := retriever.NewRetriever(s, m)
	return &BasicChain{
		Retriever:   r,
		Store:       s,
		m:           m,
		systemPrmpt: systemPrmpt,
		threasold:   threasold,
		candidates:  candidates,
	}
}

// ImportStore imports the store from the given byte slice.
func (c *BasicChain) ImportStore(data []byte) error {
	c.Store.Mutex().Lock()
	defer c.Store.Mutex().Unlock()
	err := msgpack.Unmarshal(data, c.Store)
	if err != nil {
		return err
	}
	return nil
}

func (c *BasicChain) ExportStore() ([]byte, error) {
	c.Store.Mutex().Lock()
	defer c.Store.Mutex().Unlock()
	data, err := msgpack.Marshal(c.Store)
	if err != nil {
		return nil, err
	}
	return data, nil
}

// Run runs the chain with the given prompts and options.
func (c *BasicChain) Run(ctx context.Context, input string, opts ...OptionAssigner) (string, error) {

	q.Q(input)

	// get relevant docs
	docs, err := c.Retriever.GetReleventDocuments(ctx, input, c.candidates)
	if err != nil {
		return "Error: can't get relevent documents", err
	}
	var docUnderThreshold []def.Document
	q.Q("Get relevent documents", c.threasold, c.candidates)
	for _, doc := range docs {
		if doc.Score < c.threasold {
			docUnderThreshold = append(docUnderThreshold, doc)
		}
	}
	q.Q("len(relevant docs): ", len(docUnderThreshold))

	reference := ""

	for _, doc := range docUnderThreshold {
		jsonBytes, err := json.Marshal(doc.Metadata)
		if err != nil {
			// can't marshal meta to json instead use content
			reference = reference + "\n" + doc.Content + "\n"
		} else {
			reference = reference + "\n" + string(jsonBytes) + "\n"
		}
	}

	q.Q("Reference: ", reference)

	modifiedPrompt := input + "\n\n" + "Rferences: \n" + reference
	prom := prompt.Prompt{
		SystemPrompt: c.systemPrmpt,
		UserPrompt:   modifiedPrompt,
	}
	ret, err := c.m.Complete(ctx, prom.Messages())
	if err != nil {
		return "LLM complete fail: ", err
	}
	return ret, nil
}

/*
RunPipe allowing ChainRunner to be chained together seamlessly.
For complex tasks, multiple Chains are required to handle different stages of the process.
*/
func RunPipe(ctx context.Context, prompt string, chains []ChainRunner, opts ...OptionAssigner) (string, error) {
	var input = prompt
	var output string
	var err error
	for _, chain := range chains {
		output, err = chain.Run(ctx, input, opts...)
		if err != nil {
			return "", err
		}
		input = output
	}
	return output, nil
}

// RunZip runs multipe chains and combines the output into a string array.
func RunZip(ctx context.Context, prompt string, chains []ChainRunner, opts ...OptionAssigner) (string, error) {
	var input = prompt
	var outputs []string
	var answer string
	var err error
	for _, chain := range chains {
		answer, err = chain.Run(ctx, input, opts...)
		if err != nil {
			return strings.Join(outputs, "\n"), err
		}
		outputs = append(outputs, answer)
	}
	return strings.Join(outputs, "\n"), nil
}
