// This file defines tasks that Nimbl will support
package actionchain

var devConfigTasks = []string{
	`
Task: Change device's IP address
Actions:
1. command: config ip-addr {MAC} {NEW IP}
  - Replace {MAC} with the MAC address of the device
	- Replace {NEW IP} with the new IP address
	- This action can be validate with following actions: 
	  1. command: read ip-address {MAC} 
		2. Verify the result of the command, the 
		
Inputs:  
MAC: 00-12-33-65-58-0a 
NEW IP: *********
`,
}
