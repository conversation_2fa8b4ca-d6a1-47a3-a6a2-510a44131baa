import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen } from "@testing-library/react";
import { App } from "antd";
import IdpsRulesTable from "./IdpsRulesTable";

// Mock dayjs
vi.mock("dayjs", () => {
  const mockDayjs = vi.fn(() => ({
    format: vi.fn(() => "2024-01-01 12:00:00"),
  }));
  return { default: mockDayjs };
});

describe("IdpsRulesTable", () => {
  const mockData = [
    {
      name: "test-rule-1",
      created_time: "2024-01-01T12:00:00Z",
      contents: [
        { sid: "1001", value: "test content 1" },
        { sid: "1002", value: "test content 2" },
      ],
    },
    {
      name: "test-rule-2",
      created_time: "2024-01-01T13:00:00Z",
      contents: [],
    },
  ];

  const mockProps = {
    data: mockData,
    loading: false,
    onRefresh: vi.fn(),
    selectedService: "test-service",
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders rules table with data", () => {
    render(
      <App>
        <IdpsRulesTable {...mockProps} />
      </App>
    );

    expect(screen.getByText("Rules")).toBeInTheDocument();
    expect(screen.getByText("test-service")).toBeInTheDocument();
    expect(screen.getByText("test-rule-1")).toBeInTheDocument();
    expect(screen.getByText("test-rule-2")).toBeInTheDocument();
  });

  it("shows loading state", () => {
    render(
      <App>
        <IdpsRulesTable {...mockProps} loading={true} />
      </App>
    );

    expect(screen.getByText("Rules")).toBeInTheDocument();
  });

  it("handles empty data", () => {
    render(
      <App>
        <IdpsRulesTable {...mockProps} data={[]} />
      </App>
    );

    expect(screen.getByText("Rules")).toBeInTheDocument();
  });
});
