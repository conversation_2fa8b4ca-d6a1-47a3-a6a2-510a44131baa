import React from "react";
import { RouterProvider, createRouter } from "@tanstack/react-router";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { routeTree } from "./routeTree.gen";
import { useAuthStore } from "./store/auth-store";
import NimblProvider from "./themes/nimbl-provider";
import PageNotFound from "./components/PageNotFound";
import { Spin } from "antd";

const queryClient = new QueryClient();

// Set up a Router instance
const router = createRouter({
  routeTree,
  defaultPendingComponent: () => (
    <Spin fullscreen tip="Loading..." size="large" />
  ),
  defaultNotFoundComponent: () => <PageNotFound />,
  context: {
    queryClient,
    auth: undefined,
  },
  defaultPreload: "intent",
  defaultPreloadStaleTime: 0,
});

const App = () => {
  const auth = useAuthStore();
  return (
    <QueryClientProvider client={queryClient}>
      <NimblProvider>
        <RouterProvider router={router} context={{ auth }} />
      </NimblProvider>
      <ReactQueryDevtools initialIsOpen={false} buttonPosition="bottom-left" />
    </QueryClientProvider>
  );
};

export default App;
