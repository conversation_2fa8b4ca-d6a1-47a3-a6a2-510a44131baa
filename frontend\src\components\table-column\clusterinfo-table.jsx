import React from "react";
import dayjs from "dayjs";
import { Flex, Badge } from "antd";

export const useClusterInfoColumns = () => {
  return React.useMemo(
    () => [
      {
        title: "Service Name",
        dataIndex: "name",
        key: "name",
        exportable: true,
        width: 250,
        render: (data, record) => {
          return (
            <Flex gap={10} align="center" style={{ height: "100%" }}>
              {record && record.status === "inactive" ? (
                <Badge status="error" />
              ) : (
                <Badge color="green" status="processing" />
              )}
              {data}
            </Flex>
          );
        },
        sorter: (a, b) => (a.name > b.name ? 1 : -1),
      },
      {
        title: "Devices",
        dataIndex: "num_devices",
        key: "num_devices",
        exportable: true,
        width: 150,
        sorter: (a, b) => (a.num_devices > b.num_devices ? 1 : -1),
      },
      {
        title: "Cmds",
        width: 100,
        dataIndex: "num_cmds",
        key: "num_cmds",
        exportable: true,
        sorter: (a, b) => (a.num_cmds > b.num_cmds ? 1 : -1),
      },
      {
        title: "Logs Received",
        dataIndex: "num_logs_received",
        key: "num_logs_received",
        width: 200,
        exportable: true,
        sorter: (a, b) => (a.num_logs_received > b.num_logs_received ? 1 : -1),
      },
      {
        title: "Logs Sent",
        dataIndex: "num_logs_sent",
        key: "num_logs_sent",
        width: 200,
        exportable: true,
        sorter: (a, b) => (a.num_logs_sent > b.num_logs_sent ? 1 : -1),
      },
      {
        title: "Start",
        dataIndex: "start",
        key: "start",
        width: 300,
        exportable: true,
        sorter: (a, b) => (a.start > b.start ? 1 : -1),
        render: (data) => {
          return dayjs(data * 1000).format("YYYY/MM/DD HH:mm:ss");
        },
      },
      {
        title: "Now",
        dataIndex: "now",
        key: "now",
        width: 300,
        exportable: true,
        render: (data) => {
          return dayjs(data * 1000).format("YYYY/MM/DD HH:mm:ss");
        },
        sorter: (a, b) => (a.Now > b.Now ? 1 : -1),
      },
      {
        title: "Go Routines",
        dataIndex: "num_goroutines",
        key: "num_goroutines",
        width: 250,
        exportable: true,
        sorter: (a, b) => (a.NumGoroutines > b.NumGoroutines ? 1 : -1),
      },
      {
        title: "IP Addresses",
        dataIndex: "ip_addresses",
        key: "ip_addresses",
        width: 350,
        exportable: true,
        render: (data) => {
          if (Array.isArray(data)) {
            return data?.join();
          }
        },
        sorter: (a, b) => (a.IPAddresses > b.IPAddresses ? 1 : -1),
      },
    ],
    []
  );
};
