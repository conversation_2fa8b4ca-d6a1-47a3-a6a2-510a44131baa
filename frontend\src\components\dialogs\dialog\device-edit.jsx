import { Alert, App, Form, Input, Modal } from "antd";
import React, { useCallback, useEffect } from "react";
import { useSendCommand } from "../../../services/mutations";
import { generateCommand } from "../../../utils/generate-commands";
import { handleCommandExecution } from "../../../utils/command-execution";
import { FORM_FIELDS, VALIDATION_RULES } from "../../../constants/device-edit";

const DeviceEdit = ({ data, onClose }) => {
  const { notification } = App.useApp();
  const sendCommand = useSendCommand();
  const [form] = Form.useForm();

  const handleSubmit = useCallback(
    (values) => {
      const command = generateCommand(data.mac, "", values, "device-edit");
      handleCommandExecution(command, sendCommand, notification);
      onClose();
    },
    [data, data?.mac, sendCommand, notification, onClose]
  );

  useEffect(() => {
    const { modelname, netmask, hostname } = data;
    form.setFieldsValue({
      [FORM_FIELDS.MODEL_NAME]: modelname,
      [FORM_FIELDS.HOST_NAME]: hostname,
      [FORM_FIELDS.NETMASK]: netmask,
    });
  }, [form, data]);

  return (
    <Modal
      title="Edit manual device"
      maskClosable={false}
      open
      destroyOnClose
      onCancel={onClose}
      okButtonProps={{ autoFocus: true, htmlType: "submit" }}
      style={{ top: 20 }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            handleSubmit(values);
            form.resetFields();
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form
        form={form}
        layout="vertical"
        name="edit_device_form"
        clearOnDestroy
      >
        <Form.Item
          name={FORM_FIELDS.MODEL_NAME}
          label="Model name"
          rules={VALIDATION_RULES[FORM_FIELDS.MODEL_NAME]}
        >
          <Input placeholder="model name" />
        </Form.Item>
        <Form.Item
          name={FORM_FIELDS.NETMASK}
          label="Netmask"
          rules={VALIDATION_RULES[FORM_FIELDS.NETMASK]}
        >
          <Input placeholder="netmask" />
        </Form.Item>
        <Form.Item
          name={FORM_FIELDS.HOST_NAME}
          label="Hostname"
          rules={VALIDATION_RULES[FORM_FIELDS.HOST_NAME]}
        >
          <Input placeholder="hostname" />
        </Form.Item>
        <Alert message="please do not use white-space" banner />
      </Form>
    </Modal>
  );
};

export default React.memo(DeviceEdit);
