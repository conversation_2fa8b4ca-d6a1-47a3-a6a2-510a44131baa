import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { TanStackRouterVite } from "@tanstack/router-plugin/vite";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    TanStackRouterVite({
      target: "react",
      autoCodeSplitting: true,
      // Exclude test files from routing
      routeFileIgnorePattern: "\\.test\\.",
    }),
    react(),
  ],
  server: {
    port: 3001,
    host: true,
    strictPort: true,
    open: true,
  },
  build: {
    outDir: "../dist",
    emptyOutDir: true, // also necessary
    sourcemap: false,
    chunkSizeWarningLimit: 7000,
  },
});
