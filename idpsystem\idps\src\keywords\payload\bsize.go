package payload

import (
	"fmt"

	"github.com/google/gonids"
)

type bsize struct {
	min    int
	max    int
	num    int
	verify func(int) bool
}

func newBsize(l *list, v *gonids.LenMatch) error {
	b := &bsize{}
	err := b.setupLenMatch(v)
	if err != nil {
		return err
	}
	d := &Detect{
		id:         l.id,
		postition:  v.DataPosition,
		detectedID: detectBsize,
	}
	d.data = b
	err = l.appendList(d)
	if err != nil {
		return err
	}
	return nil
}
func (b *bsize) setupLenMatch(v *gonids.LenMatch) error {
	if v.DataPosition == gonids.PayloadData {
		return fmt.Errorf("bsize only support header buffer")
	}
	ck, err := b.creatFunc(v)
	if err != nil {
		return err
	}
	b.verify = ck
	return nil
}

func (b *bsize) inspect(p *Packet) bool {
	if b.verify != nil {
		return b.verify(int(p.bufferLen))
	}
	return true
}

func (b *bsize) creatFunc(v *gonids.LenMatch) (func(int) bool, error) {
	switch v.Operator {
	case "":
		b.num = v.Num
		return func(p int) bool { return p == b.num }, nil
	case "<=":
		b.num = v.Num
		return func(p int) bool { return p <= b.num }, nil
	case "<":
		b.num = v.Num
		return func(p int) bool { return p < b.num }, nil
	case ">":
		b.num = v.Num
		return func(p int) bool { return p > b.num }, nil
	case ">=":
		b.num = v.Num
		return func(p int) bool { return p >= b.num }, nil
	case "<>":
		b.max = v.Max
		b.min = v.Min
		return func(p int) bool { return p > b.min && p < b.max }, nil
	default:
		return nil, fmt.Errorf("invalid operator: %s", v.Operator)
	}

}

func mapSizeHandler(l *list, lm *gonids.LenMatch) error {
	m := map[string]func(l *list, lm *gonids.LenMatch) error{
		"bsize": func(l *list, lm *gonids.LenMatch) error {
			return newBsize(l, lm)
		},
		"dsize": func(l *list, lm *gonids.LenMatch) error {
			return newDsize(l, lm)
		},
	}
	if f, ok := m[lm.Kind.String()]; ok {
		return f(l, lm)
	}
	return nil
}
