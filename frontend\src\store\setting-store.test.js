import { describe, it, expect, beforeEach, vi, afterEach } from "vitest";
import { act } from "@testing-library/react";
import { axiosInstance } from "../services/api";

// Import the actual store
import { useSettingStore as actualUseSettingStore } from "./setting-store";

// Create a mock store
const initialState = {
  mode: "dark",
  colorPrimary: "#13c2c2",
  baseURL: "http://localhost:27182",
  wsURL: "ws://localhost:27182",
  inventoryType: "device",
};

const mockStore = {
  ...initialState,
  changeMode: vi.fn((mode) => {
    mockStore.mode = mode;
  }),
  changePrimaryColor: vi.fn((colorPrimary) => {
    mockStore.colorPrimary = colorPrimary;
  }),
  changeBaseURL: vi.fn((baseURL) => {
    axiosInstance.defaults.baseURL = baseURL;
    mockStore.baseURL = baseURL;
  }),
  changeWsURL: vi.fn((wsURL) => {
    mockStore.wsURL = wsURL;
  }),
  changeInventoryType: vi.fn((type) => {
    mockStore.inventoryType = type;
  }),
};

// Mock the useSettingStore hook
const useSettingStore = {
  getState: vi.fn(() => mockStore),
};

// Mock localStorage
const localStorageMock = (() => {
  let store = {};
  return {
    getItem: vi.fn((key) => store[key] || null),
    setItem: vi.fn((key, value) => {
      store[key] = value.toString();
    }),
    clear: vi.fn(() => {
      store = {};
    }),
    removeItem: vi.fn((key) => {
      delete store[key];
    }),
  };
})();

// Mock axiosInstance
vi.mock("../services/api", () => ({
  axiosInstance: {
    defaults: {
      baseURL: "http://localhost:27182",
    },
  },
}));

describe("setting-store", () => {
  beforeEach(() => {
    // Setup localStorage mock
    Object.defineProperty(window, "localStorage", {
      value: localStorageMock,
      writable: true,
    });

    // Clear localStorage
    localStorageMock.clear();

    // Reset mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should initialize with default values", () => {
    // Act
    const state = mockStore;

    // Assert
    expect(state.mode).toBe("dark");
    expect(state.colorPrimary).toBe("#13c2c2");
    expect(state.baseURL).toBe("http://localhost:27182");
    expect(state.wsURL).toBe("ws://localhost:27182");
    expect(state.inventoryType).toBe("device");
  });

  it("should change mode correctly", () => {
    // Act
    mockStore.changeMode("light");

    // Assert
    expect(mockStore.changeMode).toHaveBeenCalledWith("light");
    expect(mockStore.mode).toBe("light");
  });

  it("should change primary color correctly", () => {
    // Act
    mockStore.changePrimaryColor("#ff0000");

    // Assert
    expect(mockStore.changePrimaryColor).toHaveBeenCalledWith("#ff0000");
    expect(mockStore.colorPrimary).toBe("#ff0000");
  });

  it("should change base URL correctly and update axios instance", () => {
    // Act
    mockStore.changeBaseURL("http://new-server:8080");

    // Assert
    expect(mockStore.changeBaseURL).toHaveBeenCalledWith(
      "http://new-server:8080"
    );
    expect(mockStore.baseURL).toBe("http://new-server:8080");
    expect(axiosInstance.defaults.baseURL).toBe("http://new-server:8080");
  });

  it("should change WebSocket URL correctly", () => {
    // Act
    mockStore.changeWsURL("ws://new-server:8080");

    // Assert
    expect(mockStore.changeWsURL).toHaveBeenCalledWith("ws://new-server:8080");
    expect(mockStore.wsURL).toBe("ws://new-server:8080");
  });

  it("should change inventory type correctly", () => {
    // Act
    mockStore.changeInventoryType("mdr");

    // Assert
    expect(mockStore.changeInventoryType).toHaveBeenCalledWith("mdr");
    expect(mockStore.inventoryType).toBe("mdr");
  });
});
