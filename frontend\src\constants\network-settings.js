export const IP_REGEX =
  /^((?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){1}$/;

export const FORM_FIELDS = {
  DHCP: "isdhcp",
  IP_ADDRESS: "new_ip_address",
  NETMASK: "netmask",
  GATEWAY: "gateway",
  HOSTNAME: "hostname",
};

export const VALIDATION_RULES = {
  [FORM_FIELDS.IP_ADDRESS]: [
    { required: true, message: "Please input the IP address!" },
    { pattern: IP_REGEX, message: "Invalid IP address format" },
  ],
  [FORM_FIELDS.NETMASK]: [
    { required: true, message: "Please input the subnet mask!" },
    { pattern: IP_REGEX, message: "Invalid subnet mask format" },
  ],
  [FORM_FIELDS.GATEWAY]: [
    { required: true, message: "Please input the gateway!" },
    { pattern: IP_REGEX, message: "Invalid gateway format" },
  ],
  [FORM_FIELDS.HOSTNAME]: [
    { required: true, message: "Please input the hostname!" },
  ],
};
