package flow

import (
	"fmt"

	"mnms/aiagent/node"
)

// Edge is an abstract object used to describe a link between two nodes in an AI agent project. A link can:
type Edge struct {
	ID     string `json:"id"`
	Source string `json:"source"`
	Target string `json:"target"`
}

// Flow is an abstract object used to describe the flow of an AI agent project.
type Flow struct {
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Nodes       []node.Node `json:"nodes"`
	Edges       []Edge      `json:"edges"`
}

// NewFlow creates a new flow
func NewFlow(name, description string) *Flow {
	return &Flow{
		Name:        name,
		Description: description,
		Nodes:       []node.Node{},
		Edges:       []Edge{},
	}
}

// HeadNodes returns a list of head nodes in the flow
func (f *Flow) HeadNodes() []node.NodeProvider {
	// Create a set to track node IDs that are targets in any edge.
	incoming := make(map[string]bool)
	for _, edge := range f.Edges {
		incoming[edge.Target] = true
	}

	// A start node is one whose ID never appears as a target.
	var startNodes []node.NodeProvider
	for _, node := range f.Nodes {
		if !incoming[node.ID] {
			startNodes = append(startNodes, &node)
		}
	}
	return startNodes
}

// NextNodes returns a list of next nodes for a given node
func (f *Flow) NextNodes(nodeID string) []node.NodeProvider {
	// Create a map for quick lookup of nodes by ID.
	nodeMap := make(map[string]node.NodeProvider)
	for _, node := range f.Nodes {
		nodeMap[node.ID] = &node
	}

	var nextNodes []node.NodeProvider
	// Iterate over edges and find those with source equal to nodeID.
	for _, edge := range f.Edges {
		if edge.Source == nodeID {
			if next, exists := nodeMap[edge.Target]; exists {
				nextNodes = append(nextNodes, next)
			}
		}
	}
	return nextNodes
}

// GetReactFlowEdges returns a list of ReactFlow edges for the flow
func (f *Flow) GetReactFlowEdges() []Edge {
	return f.Edges
}

// AddNode adds a node to the project
func (p *Flow) AddNode(node node.Node) {
	p.Nodes = append(p.Nodes, node)
}

// NodeExists checks if a node exists in the project
func (p *Flow) NodeExists(nodeName string) bool {
	for _, n := range p.Nodes {
		info := n.Info()
		if info.Name == nodeName {
			return true
		}
	}
	return false
}

// GetNodeWithName returns a node from the Flow
func (p *Flow) GetNodeWithName(nodeName string) (*node.Node, error) {
	for _, node := range p.Nodes {
		info := node.Info()
		if info.Name == nodeName {
			return &node, nil
		}
	}
	return nil, fmt.Errorf("node %s not found", nodeName)
}

// GetNodeWithID returns a node from the Flow
func (p *Flow) GetNodeWithID(nodeID string) (node.NodeProvider, error) {
	for _, n := range p.Nodes {
		if n.ID == nodeID {

			return &n, nil
		}
	}
	return nil, fmt.Errorf("node %s not found", nodeID)
}

// AddEdge adds a link to the Flow
func (p *Flow) AddEdge(source, target string) error {
	// check node exists
	if !p.NodeExists(source) {
		return fmt.Errorf("source node %s does not exist", source)
	}
	if !p.NodeExists(target) {
		return fmt.Errorf("target node %s does not exist", target)
	}
	// id should be unique
	id := source + ">>" + target

	// check if edge already exists
	for _, edge := range p.Edges {
		if edge.ID == id {
			return fmt.Errorf("edge %s already exists", id)
		}
	}

	p.Edges = append(p.Edges, Edge{
		ID:     id,
		Source: source,
		Target: target,
	})
	return nil
}
