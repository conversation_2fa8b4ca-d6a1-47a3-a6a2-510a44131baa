import { useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "./api";

/**
 * Custom hook for handling user authentication
 * @returns {UseMutationResult} Mutation result for login operation
 */
export const useLogin = () => {
  return useMutation({
    mutationKey: ["authentication"],
    mutationFn: async (credentials) => {
      const response = await api.login(credentials);
      if (!response) {
        throw new Error("Invalid authentication response");
      }
      return response;
    },
    retry: false,
  });
};

/**
 * Custom hook for handling send command
 * @returns {UseMutationResult} Mutation result for send command operation
 */
export const useSendCommand = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["command"],
    mutationFn: async (command) => {
      if (!command || !Array.isArray(command)) {
        throw new Error("Invalid command format");
      }
      const response = await api.sendCommand(command);
      if (!response) {
        throw new Error("Failed to send command");
      }
      return response;
    },
    onSettled: () => {
      queryClient.invalidateQueries(["command"]);
    },
    retry: false,
  });
};

/**
 * Custom hook for handling user creation
 * @returns {UseMutationResult} Mutation result for user operation
 */
export const useCreateUserMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["users"],
    mutationFn: async (user) => {
      if (!user || !user.name) {
        throw new Error("Invalid user data");
      }
      const response = await api.createNewUser(user);
      if (!response) {
        throw new Error("Failed to create user");
      }
      return response;
    },
    onSettled: () => {
      queryClient.invalidateQueries(["users"]);
    },
    retry: false,
  });
};

/**
 * Custom hook for handling user edit
 * @returns {UseMutationResult} Mutation result for user operation
 */
export const useEditUserMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["users"],
    mutationFn: async (user) => {
      if (!user || !user.name) {
        throw new Error("Invalid user data");
      }
      const response = await api.editUser(user);
      if (!response) {
        throw new Error("Failed to edit user");
      }
      return response;
    },
    onSettled: () => {
      queryClient.invalidateQueries(["users"]);
    },
    retry: false,
  });
};

export const useDeleteUserMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["users"],
    mutationFn: async (user) => {
      if (!user || !user.name) {
        throw new Error("Invalid user data");
      }
      const response = await api.deleteUser(user);
      if (!response) {
        throw new Error("Failed to edit user");
      }
      return response;
    },
    onSettled: () => {
      queryClient.invalidateQueries(["users"]);
    },
    retry: false,
  });
};

export const useGenerateSecret = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["users"],
    mutationFn: async (user) => {
      if (!user) {
        throw new Error("Invalid user data");
      }
      const response = await api.generateSecret(user);
      if (!response) {
        throw new Error("Failed to create secret");
      }
      return response;
    },
    onSettled: () => {
      queryClient.invalidateQueries(["users"]);
    },
    retry: false,
  });
};

export const useDeleteSecret = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["users"],
    mutationFn: async (user) => {
      if (!user) {
        throw new Error("Invalid user data");
      }
      const response = await api.deleteSecret(user);
      if (!response) {
        throw new Error("Failed to delete secret");
      }
      return response;
    },
    onSettled: () => {
      queryClient.invalidateQueries(["users"]);
    },
    retry: false,
  });
};

export const useValidateSecret = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["users"],
    mutationFn: async (data2fa) => {
      if (!data2fa) {
        throw new Error("Invalid 2fa code data");
      }
      const response = await api.validate2fa(data2fa);
      if (!response) {
        throw new Error("Failed to delete secret");
      }
      return response;
    },
    onSettled: () => {
      queryClient.invalidateQueries(["users"]);
    },
    retry: false,
  });
};

/**
 * Custom hook for importing KV Store data
 * @returns {UseMutationResult} Mutation result for importing KV Store
 */
export const useImportKVStore = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["kv-store"],
    mutationFn: async (data) => {
      if (!data) {
        throw new Error("Invalid kv store data");
      }
      const response = await api.importKvStore(data);
      if (!response) {
        throw new Error("Failed to import kv store");
      }
      return response;
    },
    onSettled: () => {
      queryClient.invalidateQueries(["kv-store"]);
    },
    retry: false,
  });
};

export const useAddTopology = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["topology"],
    mutationFn: async (data) => {
      if (!data) {
        throw new Error("Invalid topology data");
      }
      const response = await api.addTopology(data);
      if (!response) {
        throw new Error("Failed to add topology");
      }
      return response;
    },
    onSettled: () => {
      queryClient.invalidateQueries(["topology"]);
    },
    retry: false,
  });
};

export const useSaveRestoreTopology = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["topology"],
    mutationFn: async (data) => {
      if (!data) {
        throw new Error("Invalid topology action data");
      }
      const response = await api.saveRestoreTopology(data);
      if (!response) {
        throw new Error("Failed to save/restore topology");
      }
      return response;
    },
    onSettled: () => {
      queryClient.invalidateQueries(["topology"]);
    },
    retry: false,
  });
};
/**
 * Custom hook for sending chat messages
 * @returns {Object} Mutation object for sending chat messages
 */
export const useSendChatMessage = () => {
  return useMutation({
    mutationFn: (chatData) => api.sendChatMessage(chatData),
    onError: (error) => {
      console.error("Chat message failed:", error);
    },
  });
};
