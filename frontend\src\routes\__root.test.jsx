import { describe, it, expect, vi } from "vitest";
import { render, screen } from "@testing-library/react";
import { RootComponent } from "./__root";
import { Outlet } from "@tanstack/react-router";

// Mock dependencies
vi.mock("@tanstack/react-router", () => ({
  Outlet: vi.fn(() => <div data-testid="outlet">Outlet Content</div>),
  createRootRoute: vi.fn(() => () => ({})),
}));

vi.mock("../components/dialogs/dialogs", () => ({
  default: vi.fn(() => <div data-testid="dialogs">Dialogs Component</div>),
}));

vi.mock("@tanstack/react-router-devtools", () => ({
  TanStackRouterDevtools: vi.fn(() => (
    <div data-testid="router-devtools">Router Devtools</div>
  )),
}));

describe("RootComponent", () => {
  it("renders the Outlet, Dialogs, and RouterDevtools components", () => {
    render(<RootComponent />);

    // Check if all components are rendered
    expect(screen.getByTestId("outlet")).toBeInTheDocument();
    expect(screen.getByTestId("dialogs")).toBeInTheDocument();
    expect(screen.getByTestId("router-devtools")).toBeInTheDocument();
  });
});
