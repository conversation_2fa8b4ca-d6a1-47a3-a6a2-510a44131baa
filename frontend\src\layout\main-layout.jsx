import { PageContainer, ProLayout } from "@ant-design/pro-layout";
import { useEffect, useState, memo } from "react";
import { useTheme, useThemeMode } from "antd-style";
import { Link, useLocation, useNavigate } from "@tanstack/react-router";
import { Dropdown, FloatButton } from "antd";
import { LogoutOutlined, MacCommandOutlined } from "@ant-design/icons";
import PropTypes from "prop-types";

import _DefaultProps from "./_defaultProps";
import { ErrorBoundries } from "./FallbackErrorBoundry";
import { useAuthStore } from "../store/auth-store";
import ServerStatus from "../components/servers-status";
import SettingsComp from "../components/SettingsComp";
import ThemeController from "../components/theme-controller";

// Import assets
import atopLogo from "../assets/images/NIMBL_Logo.svg";
import atopDarklogo from "../assets/images/darkmode-logo.svg";
import defaultAvatar from "../assets/images/defaultAvatar.webp";
import { CommandFloatButton } from "../features/command-results";
import { ChatFloatButton, ResizableChatPanel } from "../features/chat";
import { useSocketStore } from "../store/socket-store";

// Separate components for better organization
const HeaderLogo = memo(({ appearance }) => (
  <img
    src={appearance === "dark" ? atopDarklogo : atopLogo}
    alt="BlackBear TechHive"
    style={{ height: "50px" }}
  />
));

const UserAvatar = memo(({ handleMenuClick, dom }) => (
  <Dropdown
    trigger={["click"]}
    placement="bottom"
    arrow
    menu={{
      items: [
        {
          key: "logout",
          icon: <LogoutOutlined />,
          label: "Logout",
        },
      ],
      onClick: handleMenuClick,
    }}
  >
    {dom}
  </Dropdown>
));

const MainLayout = ({ children }) => {
  const token = useTheme();
  const auth = useAuthStore();
  const { appearance } = useThemeMode();
  const location = useLocation();
  const navigate = useNavigate();
  const [pathname, setPathname] = useState(location.pathname);
  const { enabledFeatures, licenseError } = useSocketStore();

  useEffect(() => {
    setPathname(location.pathname || "/");
  }, [location]);

  const handleMenuClick = async (e) => {
    if (e.key === "logout") {
      try {
        await auth.clearAuthData();
        await navigate({ to: "/login" });
      } catch (error) {
        console.error("Logout failed:", error);
      }
    }
  };

  const layoutToken = {
    bgLayout: token.colorBgLayout,
    sider: {
      colorMenuBackground: token.colorBgContainer,
      colorBgMenuItemSelected:
        appearance === "dark" ? token.colorPrimary : token.colorPrimaryBg,
      colorTextMenuSelected:
        appearance === "dark" ? token.colorText : token.colorPrimary,
    },
    pageContainer: {
      paddingBlockPageContainerContent: 0,
      paddingInlinePageContainerContent: 16,
    },
  };

  const headerActions = [
    <ServerStatus key="server-status" />,
    <SettingsComp key="settings" />,
    <ThemeController key="theme" />,
  ];

  return (
    <>
      <ProLayout
        {..._DefaultProps({
          anomalies: enabledFeatures.includes("anomalies"),
          idps: enabledFeatures.includes("idps"),
        })}
        siderWidth={220}
        layout="mix"
        fixSiderbar
        fixedHeader
        hasSiderMenu={true}
        siderMenuType="sub"
        ErrorBoundary={ErrorBoundries}
        menu={{ collapsedShowGroupTitle: false }}
        location={{ pathname }}
        logo={<HeaderLogo appearance={appearance} />}
        title="BlackBear TechHive"
        headerTitleRender={(logo) => (
          <a
            href="https://blackbeartechhive.com"
            target="_blank"
            rel="noreferrer"
          >
            {logo}
          </a>
        )}
        avatarProps={{
          src: defaultAvatar,
          size: "default",
          title: "admin",
          alt: "admin",
          render: (_, dom) => (
            <UserAvatar handleMenuClick={handleMenuClick} dom={dom} />
          ),
        }}
        actionsRender={() => headerActions}
        menuItemRender={(item, dom) => <Link to={item.path || "/"}>{dom}</Link>}
        token={layoutToken}
      >
        <PageContainer header={{ title: "", breadcrumb: [] }}>
          <div style={{ paddingBlock: "16px" }}>{children}</div>
          <CommandFloatButton />
          <ChatFloatButton />
        </PageContainer>
      </ProLayout>
      <ResizableChatPanel />
    </>
  );
};

MainLayout.propTypes = {
  children: PropTypes.node.isRequired,
};

HeaderLogo.propTypes = {
  appearance: PropTypes.oneOf(["dark", "light"]).isRequired,
};

UserAvatar.propTypes = {
  handleMenuClick: PropTypes.func.isRequired,
};

export default memo(MainLayout);
