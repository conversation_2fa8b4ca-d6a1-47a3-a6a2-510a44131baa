import { Button, Result, Typography } from "antd";
import React, { useEffect, useRef, memo } from "react";
import { CatchBoundary, useLocation, useRouter } from "@tanstack/react-router";
import PropTypes from "prop-types";

const { Paragraph, Text } = Typography;

const ErrorDetails = memo(({ error }) => (
  <details className="error-details" open>
    <summary>Error Details:</summary>
    <Paragraph>
      <Text
        strong
        style={{
          fontSize: 16,
          color: "var(--ant-color-error)",
        }}
      >
        {error.message}
      </Text>
    </Paragraph>
    <div style={{ whiteSpace: "pre-wrap" }}>
      {error?.stack?.toString() || "No stack trace available"}
    </div>
  </details>
));

const FallbackErrorBoundry = ({ error, reset }) => {
  const { pathname } = useLocation();
  const router = useRouter();
  const originalPathname = useRef(pathname);

  useEffect(() => {
    if (pathname !== originalPathname.current) {
      reset();
    }
  }, [pathname, reset]);

  const handleRetry = () => {
    reset();
  };

  const handleGoHome = () => {
    router.navigate({ to: "/" });
  };

  return (
    <Result
      status="error"
      title="Something went wrong!"
      subTitle="A client error occurred and your request couldn't be completed."
      extra={[
        <Button key="retry" type="primary" onClick={handleRetry}>
          Try Again
        </Button>,
        <Button key="home" onClick={handleGoHome}>
          Go to Home
        </Button>,
      ]}
    >
      {process.env.NODE_ENV !== "production" && <ErrorDetails error={error} />}
    </Result>
  );
};

const ErrorBoundaries = ({ children }) => (
  <CatchBoundary
    getResetKey={() => "reset"}
    errorComponent={FallbackErrorBoundry}
  >
    {children}
  </CatchBoundary>
);

// PropTypes definitions
ErrorDetails.propTypes = {
  error: PropTypes.shape({
    message: PropTypes.string,
    stack: PropTypes.string,
  }).isRequired,
};

FallbackErrorBoundry.propTypes = {
  error: PropTypes.shape({
    message: PropTypes.string,
    stack: PropTypes.string,
  }).isRequired,
  reset: PropTypes.func.isRequired,
};

ErrorBoundaries.propTypes = {
  children: PropTypes.node.isRequired,
};

// Export memoized components
export const ErrorBoundries = memo(ErrorBoundaries);
export const FallbackComponent = memo(FallbackErrorBoundry);
