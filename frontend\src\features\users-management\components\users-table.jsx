import React, { useCallback } from "react";
import { But<PERSON>, theme } from "antd";
import ProTable from "@ant-design/pro-table";
import { PlusOutlined } from "@ant-design/icons";
import ExportData from "../../../components/export-data/export-data";
import { useAuthStore } from "../../../store/auth-store";
import { useAppStore } from "../../../store/store";
import { TABLE_CONFIG } from "../constants";
import { useUsersFilters } from "../hooks/useUsersFilter";

const UsersTable = () => {
  const { token } = theme.useToken();
  const { role: userRole } = useAuthStore();
  const { openDialogs } = useAppStore();
  const { filteredData, setInputSearch, columns, refetch, isFetching } =
    useUsersFilters();

  const handleAddUser = useCallback(() => {
    openDialogs({
      id: "userManagement",
      data: null,
    });
  }, [openDialogs]);

  const tableProps = {
    cardProps: { style: { boxShadow: token?.Card?.boxShadow } },
    headerTitle: "User List",
    columns,
    dataSource: filteredData,
    rowKey: "name",
    size: "small",
    options: { reload: refetch, fullScreen: false },
    loading: isFetching,
    pagination: {
      ...TABLE_CONFIG.pagination,
      total: filteredData.length,
    },
    scroll: TABLE_CONFIG.scroll,
    toolbar: {
      search: {
        allowClear: true,
        onSearch: setInputSearch,
        onClear: () => setInputSearch(""),
      },
      actions: [
        userRole === "admin" && (
          <Button
            key="add-user"
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddUser}
            data-testid="add-user-button"
          >
            Add New
          </Button>
        ),
        <ExportData
          key="export"
          Columns={columns}
          DataSource={filteredData}
          title="User_List"
        />,
      ].filter(Boolean),
    },
    search: false,
    dateFormatter: "string",
    columnsState: TABLE_CONFIG.columnsState,
  };

  return <ProTable {...tableProps} />;
};

export default React.memo(UsersTable);
