import { describe, it, expect } from 'vitest';
import { 
  generateCommand, 
  generateNetworkCommand, 
  generateSyslogCommand 
} from './generate-commands';

describe('generate-commands', () => {
  const MAC_ADDRESS = '00:11:22:33:44:55';
  const CURRENT_IP = '*************';
  
  describe('generateNetworkCommand', () => {
    it('should generate correct command with static IP', () => {
      // Arrange
      const values = {
        isdhcp: false,
        new_ip_address: '*************',
        netmask: '*************',
        gateway: '***********',
        hostname: 'device1',
      };
      
      // Act
      const result = generateNetworkCommand(MAC_ADDRESS, CURRENT_IP, values);
      
      // Assert
      expect(result).toEqual([
        {
          command: `config network set ${MAC_ADDRESS} ${CURRENT_IP} ${values.new_ip_address} ${values.netmask} ${values.gateway} ${values.hostname} 0`,
        },
      ]);
    });
    
    it('should generate correct command with DHCP', () => {
      // Arrange
      const values = {
        isdhcp: true,
        new_ip_address: '*************', // Should be ignored
        netmask: '*************',
        gateway: '***********',
        hostname: 'device1',
      };
      
      // Act
      const result = generateNetworkCommand(MAC_ADDRESS, CURRENT_IP, values);
      
      // Assert
      expect(result).toEqual([
        {
          command: `config network set ${MAC_ADDRESS} ${CURRENT_IP} 0.0.0.0 ${values.netmask} ${values.gateway} ${values.hostname} 1`,
        },
      ]);
    });
  });
  
  describe('generateSyslogCommand', () => {
    it('should generate correct command with both log options enabled', () => {
      // Arrange
      const values = {
        logToFlash: true,
        logToServer: true,
        serverIP: '***********',
        serverPort: '514',
        logLevel: '7',
      };
      
      // Act
      const result = generateSyslogCommand(MAC_ADDRESS, values);
      
      // Assert
      expect(result).toEqual([
        {
          command: `config syslog set ${MAC_ADDRESS} 1 ${values.serverIP} ${values.serverPort} ${values.logLevel} 1`,
        },
      ]);
    });
    
    it('should generate correct command with only log to flash enabled', () => {
      // Arrange
      const values = {
        logToFlash: true,
        logToServer: false,
        serverIP: '***********',
        serverPort: '514',
        logLevel: '7',
      };
      
      // Act
      const result = generateSyslogCommand(MAC_ADDRESS, values);
      
      // Assert
      expect(result).toEqual([
        {
          command: `config syslog set ${MAC_ADDRESS} 0 ${values.serverIP} ${values.serverPort} ${values.logLevel} 1`,
        },
      ]);
    });
    
    it('should generate correct command with only log to server enabled', () => {
      // Arrange
      const values = {
        logToFlash: false,
        logToServer: true,
        serverIP: '***********',
        serverPort: '514',
        logLevel: '7',
      };
      
      // Act
      const result = generateSyslogCommand(MAC_ADDRESS, values);
      
      // Assert
      expect(result).toEqual([
        {
          command: `config syslog set ${MAC_ADDRESS} 1 ${values.serverIP} ${values.serverPort} ${values.logLevel} 0`,
        },
      ]);
    });
  });
  
  describe('generateCommand', () => {
    it('should generate network command when type is "network"', () => {
      // Arrange
      const values = {
        isdhcp: false,
        new_ip_address: '*************',
        netmask: '*************',
        gateway: '***********',
        hostname: 'device1',
      };
      
      // Act
      const result = generateCommand(MAC_ADDRESS, CURRENT_IP, values, 'network');
      
      // Assert
      expect(result).toEqual([
        {
          command: `config network set ${MAC_ADDRESS} ${CURRENT_IP} ${values.new_ip_address} ${values.netmask} ${values.gateway} ${values.hostname} 0`,
        },
      ]);
    });
    
    it('should generate syslog command when type is "syslog"', () => {
      // Arrange
      const values = {
        logToFlash: true,
        logToServer: true,
        serverIP: '***********',
        serverPort: '514',
        logLevel: '7',
      };
      
      // Act
      const result = generateCommand(MAC_ADDRESS, CURRENT_IP, values, 'syslog');
      
      // Assert
      expect(result).toEqual([
        {
          command: `config syslog set ${MAC_ADDRESS} 1 ${values.serverIP} ${values.serverPort} ${values.logLevel} 1`,
        },
      ]);
    });
    
    it('should generate trap command when type is "trap"', () => {
      // Arrange
      const values = {
        serverIP: '***********',
        serverPort: '162',
        comString: 'public',
      };
      
      // Act
      const result = generateCommand(MAC_ADDRESS, CURRENT_IP, values, 'trap');
      
      // Assert
      expect(result).toEqual([
        {
          command: `snmp trap add ${MAC_ADDRESS} ${values.serverIP} ${values.serverPort} ${values.comString}`,
        },
      ]);
    });
    
    it('should generate firmware update command when type is "firmware"', () => {
      // Arrange
      const values = {
        fwUrl: 'http://server/firmware.bin',
      };
      
      // Act
      const result = generateCommand(MAC_ADDRESS, CURRENT_IP, values, 'firmware');
      
      // Assert
      expect(result).toEqual([
        {
          command: `firmware update ${MAC_ADDRESS} ${values.fwUrl}`,
        },
      ]);
    });
    
    it('should generate device edit command when type is "device-edit"', () => {
      // Arrange
      const values = {
        modelname: 'Model-X',
        hostname: 'device1',
        netmask: '*************',
      };
      
      // Act
      const result = generateCommand(MAC_ADDRESS, CURRENT_IP, values, 'device-edit');
      
      // Assert
      expect(result).toEqual([
        {
          command: `device edit ${MAC_ADDRESS} ${values.modelname} ${values.netmask} ${values.hostname}`,
        },
      ]);
    });
    
    it('should throw error for invalid command type', () => {
      // Arrange
      const values = {};
      
      // Act & Assert
      expect(() => generateCommand(MAC_ADDRESS, CURRENT_IP, values, 'invalid-type'))
        .toThrow('Invalid command type');
    });
  });
});
