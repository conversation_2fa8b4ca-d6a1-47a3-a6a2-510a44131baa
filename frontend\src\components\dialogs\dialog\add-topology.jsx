import { App, Button, Form, Input, Modal, Space, Typography } from "antd";
import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import React, { useCallback } from "react";
import { useAddTopology } from "../../../services/mutations";
import { createManualTpologyData } from "../../../utils/topologyUtils";

const AddTopoloy = ({ onClose }) => {
  const [form] = Form.useForm();
  const { notification } = App.useApp();
  const addTopology = useAddTopology();

  const handleSubmit = useCallback(
    async (values) => {
      try {
        const topoData = createManualTpologyData(values);
        await addTopology.mutateAsync(topoData);
        notification.success({
          message: "Success",
          description: "Topology added successfully",
        });
        form.resetFields();
        onClose();
      } catch (error) {
        console.error("Failed to add topology:", error);
        notification.error({
          message: "Error adding topology",
          description: error.message || "Something went wrong",
        });
      }
    },
    [addTopology, notification, form, onClose]
  );

  return (
    <Modal
      title="Add Topology"
      open
      destroyOnClose
      onCancel={onClose}
      style={{ top: 20 }}
      maskClosable={false}
      okButtonProps={{ autoFocus: true, htmlType: "submit" }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            handleSubmit(values);
            // Form will be reset only on successful submission
            // to preserve values in case of error
          })
          .catch((info) => {
            console.error("Form validation failed:", info);
            notification.error({
              message: "Validation Error",
              description: "Please check the form fields and try again",
            });
          });
      }}
    >
      <Form
        name="add_topology_form"
        form={form}
        autoComplete="off"
        layout="vertical"
        clearOnDestroy
      >
        <Form.Item
          name="mac"
          label="Device macaddress"
          rules={[
            {
              required: true,
              message: "Missing device mac",
            },
          ]}
        >
          <Input placeholder="Device macaddress" />
        </Form.Item>
        <Form.List
          name="linkData"
          rules={[
            {
              validator: async (_, linkData) => {
                if (!linkData || linkData.length < 1) {
                  return Promise.reject(
                    new Error("At least 1 link data required")
                  );
                }
              },
            },
          ]}
        >
          {(fields, { add, remove }, { errors }) => (
            <>
              {fields.map(({ key, name, ...restField }, i) => (
                <div key={key}>
                  <Typography.Text>Link data {i + 1}:</Typography.Text>
                  <Space
                    style={{
                      display: "flex",
                      marginBottom: 8,
                    }}
                    align="baseline"
                  >
                    <Form.Item
                      {...restField}
                      name={[name, "target"]}
                      rules={[
                        {
                          required: true,
                          message: "Missing target mac",
                        },
                      ]}
                    >
                      <Input placeholder="Target mac" />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, "sourcePort"]}
                      rules={[
                        {
                          required: true,
                          message: "Missing source port",
                        },
                      ]}
                    >
                      <Input placeholder="Source port" />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, "targetPort"]}
                      rules={[
                        {
                          required: true,
                          message: "Missing target port",
                        },
                      ]}
                    >
                      <Input placeholder="Target port" />
                    </Form.Item>
                    <MinusCircleOutlined onClick={() => remove(name)} />
                  </Space>
                </div>
              ))}
              <Form.Item>
                <Button
                  type="dashed"
                  onClick={() => add()}
                  block
                  icon={<PlusOutlined />}
                >
                  Add field
                </Button>
                <Form.ErrorList errors={errors} />
              </Form.Item>
            </>
          )}
        </Form.List>
      </Form>
    </Modal>
  );
};

export default AddTopoloy;
