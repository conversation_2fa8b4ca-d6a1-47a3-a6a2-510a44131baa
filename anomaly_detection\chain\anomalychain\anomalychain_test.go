package anomalychain

import (
	"context"
	"mnms/anomaly_detection/chain"
	"mnms/llm"

	"mnms/anomaly_detection/retriever"
	"mnms/anomaly_detection/store/anomstore"
	"testing"
	"time"
)

// TestAnomalyChain tests the AnomalyChain implementation.
func TestAnomalyChain(t *testing.T) {
	promtps := []string{
		"Please introduce yourself",
		`<5>May 15 15:22:55 root alert: root offline`,
		`<5>May 15 15:22:55 root alert: root shutdown`,
		`<5>May 15 15:22:55 bbanomsvc: command insert not found!`,
		`<11> Jul 17 06:14:36 unknown: the unrecognized message`,
		`<11> Jul 17 06:14:36 unknown: a wierd message, it was sent <NAME_EMAIL>`,
		`<11> Jul 17 06:14:36 unknown: a strange message, every night!!!`,
		`<11> Jul 17 06:14:36 unknown: an alien message from nova5 system`,
		`<188> Jul 17 06:14:36 CRITICAL: an alien attack root server!!!!!`,
		`<99> Jul 17 06:14:36 root: everything is fine`,
	}
	s := &anomstore.LocalVectors
	// llmModule := m.NewDemoTransformer3()
	llmModule := llm.NewOllama("http://localhost", "llama3", 11434)

	retr := retriever.NewRetriever(s, llmModule)

	retr.Add(context.Background(), "<5>May 15 15:22:55 bbanomsvc: command insert not found!", map[string]any{
		"reason": "",
		"normal": true,
	})

	retr.Add(context.Background(), "<11> Jul 17 06:14:36 unknown: the unrecognized message", map[string]any{
		"reason": "we don't care about if message is unrecognized",
		"normal": true,
	})

	retr.Add(context.Background(), "<188> Jul 17 06:14:36 CRITICAL: an alien attack root server!!!!", map[string]any{
		"reason": "NO WAY! alien is our friend! this is not an attack!",
		"normal": true,
	})

	retr.Add(context.Background(), "<99> Jul 17 06:14:36 root: everything is fine", map[string]any{
		"reason": "I don't believe root.",
		"normal": false})

	c := NewChain(llmModule)
	start := time.Now()
	for _, prompt := range promtps {
		t.Log("Prompt: ", prompt)
		answer, err := c.Run(context.Background(), prompt,
			chain.WithDebugOutput("debug.txt"),
			chain.WithReportFile("report.txt"))
		if err != nil {
			t.Fatal(err)
		}
		t.Log("Answer: ", answer)

	}

	otherPrompts := []string{
		`<5>May 15 15:22:55 bbanomsvc: command append not found!`,
		`<188> Jul 17 06:14:36 CRITICAL: root server be attacked by someone from space!!!!!`,
	}

	for _, prompt := range otherPrompts {
		t.Log("Prompt: ", prompt)
		answer, err := c.Run(context.Background(), prompt,
			chain.WithDebugOutput("debug.txt"),
			chain.WithReportFile("report.txt"))
		if err != nil {
			t.Fatal(err)
		}
		t.Log("Answer: ", answer)
	}

	t.Log("Time: ", time.Since(start))

}
