## Set up LLM 
Since the AI agent requires the assistance of LLM, setting up LLM is necessary. The system searches for the config file in the following order of priority:
Linux
1. working directory/llmconfig.yaml
2. /etc/nimbl/llmconfig.yaml 
3. ~/.nimbl/llmconfig.yaml
Windows
1. working directory\llmconfig.yaml
2. %APPDATA%\Roaming\nimbl\llmconfig.yaml
3. %APPDATA%\Local\nimbl\llmconfig.yaml

## Sample llmconfig.yaml
A open-ai llmconfig.yaml sample:
```yaml
	type: "open-ai"
	model: "gpt-3"
	api_key: "your-openai-api-key"
```

A ollama llmconfig.yaml sample:
```yaml
  type: "ollama"
  model: "llama3.1:default"
  host: "http://localhost"
  port: 5000
```

If not found, the system will use the default settings. The default settings are as follows:
```yaml
  type: "open-ai"
  model: "gpt-3"
  api_key: ""
```

## Set up API agent
The AI agent requires the assistance of the API agent to communicate with the LLM. API agent reads the environment variable `BB_TOKEN` and `BBROOT_HOST` to connect to `bbrootsvc`.

## Sample .env file
```env
BB_TOKEN=your-bb-token
BBROOT_HOST=http://localhost:27182
```