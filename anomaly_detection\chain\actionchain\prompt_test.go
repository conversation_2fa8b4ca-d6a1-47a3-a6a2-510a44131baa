package actionchain

import (
	"mnms/llm"

	"testing"
)

// TestGetDeviceCurrentSettings
func TestGetDeviceCurrentSettings(t *testing.T) {
	inputs := []string{
		"Please set device 00-12-44-12-34 IP address to ***********",
		"Assign IP address ************ to the device with MAC 00-1A-2B-3C-4D-5E.",
		"Change the IP of device A0-B1-C2-D3-E4-F5 to **********.",
		"Set subnet mask ************* for device 00-12-44-12-34.",
		"Device 11-22-33-44-55-66 needs a subnet mask of *************.",
	}
	gptSetting := llm.GPTSettings()
	mod, err := llm.NewLLMClient(gptSetting)
	if err != nil {
		t.<PERSON><PERSON>rf("error creating LLM client: %v", err)
	}
	for _, input := range inputs {
		ret, err := GetDeviceCurrentSettings(mod, input)
		if err != nil {
			t.<PERSON><PERSON>rf("error getting device settings: %v", err)
		}
		t.Logf("Result: %s", ret)

	}
	// Test mutiple device settings
	input := "Please set device 00-12-44-12-34 IP address to ***********, and device 00-12-44-12-35 IP address to ***********."
	ret, err := GetDeviceCurrentSettings(mod, input)
	if err != nil {
		t.Errorf("error getting device settings: %v", err)
	}
	t.Logf("Result: %s", ret)
}
