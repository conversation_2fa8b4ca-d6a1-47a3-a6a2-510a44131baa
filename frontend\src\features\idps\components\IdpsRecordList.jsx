import { memo, useMemo, useState } from "react";
import {
  Card,
  DatePicker,
  Space,
  Typography,
  theme,
  Row,
  Col,
  Button,
  Modal,
  Select,
  Input,
} from "antd";
import ReactApexChart from "react-apexcharts";
import { useTheme } from "antd-style";
import { DeleteOutlined } from "@ant-design/icons";
import dayjs from "dayjs";

const { Text } = Typography;

/**
 * IDPS Record List Component
 * Displays record list with bar chart and date filtering
 * @param {Object} props Component props
 * @param {Array} props.data Record list data
 * @param {boolean} props.loading Loading state
 * @param {string} props.selectedService Selected service name
 * @param {Function} props.onFileSearch Callback for file search
 * @param {Function} props.onDeleteRecords Callback for delete records
 * @returns {JSX.Element} Record list component
 */
const IdpsRecordList = ({
  data = [],
  loading = false,
  selectedService,
  onFileSearch,
  onDeleteRecords,
}) => {
  const { appearance } = useTheme();
  const { token } = theme.useToken();
  const [selectedDate, setSelectedDate] = useState(dayjs()); // Default to today
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteType, setDeleteType] = useState("all");
  const [deleteFileName, setDeleteFileName] = useState("");
  const [deleteSelectedDate, setDeleteSelectedDate] = useState(dayjs());

  // Filter data based on selected date
  const filteredData = useMemo(() => {
    if (!selectedDate) {
      return data;
    }

    const targetDate = selectedDate.format("YYYY-MM-DD");
    return data.filter((record) => {
      if (!record.date) return false;
      const recordDate = dayjs(record.date).format("YYYY-MM-DD");
      return recordDate === targetDate;
    });
  }, [data, selectedDate]);

  // Process data for bar chart - individual files with name vs size
  const chartOptions = useMemo(() => {
    if (!filteredData || filteredData.length === 0) {
      return null;
    }

    // Collect all files from filtered records
    const allFiles = [];
    filteredData.forEach((record) => {
      if (record.files && Array.isArray(record.files)) {
        record.files.forEach((file) => {
          if (file.name && file.size) {
            allFiles.push({
              name: file.name,
              size: Math.round(file.size / 1024), // Convert to KB
              date: record.date,
            });
          }
        });
      }
    });

    if (allFiles.length === 0) {
      return null;
    }

    // Sort files by size (largest first) and limit to top 20 for readability
    const sortedFiles = allFiles.sort((a, b) => b.size - a.size).slice(0, 20);

    const fileNames = sortedFiles.map((file) => file.name);
    const fileSizes = sortedFiles.map((file) => file.size);

    return {
      series: [
        {
          name: "File Size (KB)",
          data: fileSizes,
        },
      ],
      options: {
        chart: {
          type: "bar",
          height: 400,
          toolbar: {
            show: false,
          },
          background: "transparent",
          events: {
            dataPointSelection: function (_, __, config) {
              const selectedFile = sortedFiles[config.dataPointIndex];

              if (selectedFile && onFileSearch && selectedDate) {
                const dateStr = selectedDate.format("YYYY-MM-DD");
                onFileSearch(selectedFile.name, dateStr, selectedService);
              }
            },
          },
        },
        theme: {
          mode: appearance === "dark" ? "dark" : "light",
        },
        plotOptions: {
          bar: {
            horizontal: true,
            columnWidth: "55%",
            borderRadius: 4,
          },
        },
        dataLabels: {
          enabled: true,
          formatter: function (val) {
            return val + " KB";
          },
        },
        stroke: {
          show: true,
          width: 2,
          colors: ["transparent"],
        },
        colors: [token.colorPrimary],
        xaxis: {
          title: {
            text: "File Size (KB)",
          },
        },
        yaxis: {
          categories: fileNames,
          title: {
            text: "File Name",
          },
          labels: {
            style: {
              fontSize: "10px",
            },
            maxWidth: 150,
          },
        },
        fill: {
          opacity: 1,
        },
        tooltip: {
          y: {
            formatter: function (val) {
              return val + " KB";
            },
          },
          x: {
            formatter: function (val) {
              return "File: " + val;
            },
          },
        },
        title: {
          text: selectedDate
            ? `Files for ${selectedDate.format("YYYY-MM-DD")} (Click to search)`
            : "Record Files by Size",
          align: "left",
          style: {
            fontSize: "16px",
            fontWeight: "bold",
          },
        },
      },
    };
  }, [
    filteredData,
    appearance,
    token,
    selectedDate,
    onFileSearch,
    selectedService,
  ]);

  const totalFiles = filteredData.reduce((sum, record) => {
    return sum + (Array.isArray(record.files) ? record.files.length : 0);
  }, 0);

  const totalSize = filteredData.reduce((sum, record) => {
    if (record.files && Array.isArray(record.files)) {
      return (
        sum +
        record.files.reduce(
          (fileSum, file) => fileSum + (file.size || 0), // Assuming size is already in KB
          0
        )
      );
    }
    return sum;
  }, 0);

  const handleDateChange = (date) => {
    setSelectedDate(date);
  };

  const handleDeleteRecords = () => {
    if (onDeleteRecords) {
      const dateStr =
        deleteType !== "all" ? deleteSelectedDate.format("YYYY-MM-DD") : null;
      onDeleteRecords(deleteType, dateStr, deleteFileName, selectedService);
      setDeleteModalVisible(false);
    }
  };

  const openDeleteModal = () => {
    setDeleteModalVisible(true);
  };

  return (
    <Card
      title={
        <div>
          <Text strong>Record List</Text>
          {selectedService && (
            <div style={{ fontSize: "12px", color: token.colorTextSecondary }}>
              Service: {selectedService}
            </div>
          )}
        </div>
      }
      extra={
        <Space>
          <Text type="secondary">Select date:</Text>
          <DatePicker
            value={selectedDate}
            onChange={handleDateChange}
            format="YYYY-MM-DD"
            allowClear
            size="small"
            placeholder="Select date"
          />
          <Button
            icon={<DeleteOutlined />}
            onClick={openDeleteModal}
            size="small"
            danger
          >
            Delete Records
          </Button>
        </Space>
      }
      variant="borderless"
      loading={loading}
    >
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          {chartOptions ? (
            <div>
              <div style={{ marginBottom: "16px", textAlign: "center" }}>
                <Space>
                  <Text type="secondary">
                    Total Records: <Text strong>{filteredData.length}</Text>
                  </Text>
                  <Text type="secondary">
                    Total Files: <Text strong>{totalFiles}</Text>
                  </Text>
                  <Text type="secondary">
                    Total Size: <Text strong>{totalSize.toFixed(2)} KB</Text>
                  </Text>
                  {selectedDate && (
                    <Text type="secondary">
                      Selected Date:{" "}
                      <Text strong>{selectedDate.format("YYYY-MM-DD")}</Text>
                    </Text>
                  )}
                </Space>
              </div>
              <ReactApexChart
                options={chartOptions.options}
                series={chartOptions.series}
                type="bar"
                height={400}
              />
            </div>
          ) : (
            <div
              style={{
                height: 350,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                flexDirection: "column",
              }}
            >
              <Text type="secondary">No record data available</Text>
              {selectedService && (
                <Text
                  type="secondary"
                  style={{ fontSize: "12px", marginTop: "8px" }}
                >
                  for service: {selectedService}
                </Text>
              )}
              {selectedDate && (
                <Text
                  type="secondary"
                  style={{ fontSize: "12px", marginTop: "8px" }}
                >
                  for date: {selectedDate.format("YYYY-MM-DD")}
                </Text>
              )}
            </div>
          )}
        </Col>

        <Col xs={24} lg={8}>
          <Card size="small" title="Record Summary">
            <Space direction="vertical" style={{ width: "100%" }}>
              <div>
                <Text type="secondary">Total Records:</Text>
                <div
                  style={{
                    fontSize: "24px",
                    fontWeight: "bold",
                    color: token.colorPrimary,
                  }}
                >
                  {filteredData.length}
                </div>
              </div>
              <div>
                <Text type="secondary">Total Files:</Text>
                <div
                  style={{
                    fontSize: "20px",
                    fontWeight: "bold",
                    color: token.colorSuccess,
                  }}
                >
                  {totalFiles}
                </div>
              </div>
              <div>
                <Text type="secondary">Total Size:</Text>
                <div
                  style={{
                    fontSize: "18px",
                    fontWeight: "bold",
                    color: token.colorWarning,
                  }}
                >
                  {totalSize.toFixed(2)} KB
                </div>
              </div>
              {selectedDate && (
                <div>
                  <Text type="secondary">Selected Date:</Text>
                  <div style={{ fontSize: "12px", marginTop: "4px" }}>
                    {selectedDate.format("MMM DD, YYYY")}
                  </div>
                </div>
              )}
            </Space>
          </Card>
        </Col>
      </Row>

      <Modal
        title="Delete Records"
        open={deleteModalVisible}
        onOk={handleDeleteRecords}
        onCancel={() => setDeleteModalVisible(false)}
        okText="Delete"
        okButtonProps={{ danger: true }}
      >
        <Space direction="vertical" style={{ width: "100%" }}>
          <div>
            <Text strong>Delete Type:</Text>
            <Select
              value={deleteType}
              onChange={setDeleteType}
              style={{ width: "100%", marginTop: "8px" }}
              options={[
                { value: "all", label: "Delete All Records" },
                { value: "date", label: "Delete by Date" },
                { value: "file", label: "Delete by File & Date" },
              ]}
            />
          </div>

          {(deleteType === "date" || deleteType === "file") && (
            <div>
              <Text strong>Date:</Text>
              <DatePicker
                value={deleteSelectedDate}
                onChange={setDeleteSelectedDate}
                format="YYYY-MM-DD"
                style={{ width: "100%", marginTop: "8px" }}
              />
            </div>
          )}

          {deleteType === "file" && (
            <div>
              <Text strong>File Name:</Text>
              <Input
                value={deleteFileName}
                onChange={(e) => setDeleteFileName(e.target.value)}
                placeholder="Enter file name"
                style={{ marginTop: "8px" }}
              />
            </div>
          )}

          <div
            style={{
              marginTop: "16px",
              padding: "8px",
              backgroundColor: "#f5f5f5",
              borderRadius: "4px",
            }}
          >
            <Text type="secondary" style={{ fontSize: "12px" }}>
              Command:{" "}
              {deleteType === "all"
                ? "idps records delete"
                : deleteType === "date"
                  ? `idps records delete -d ${deleteSelectedDate.format("YYYY-MM-DD")}`
                  : `idps records delete -f ${deleteFileName || "[filename]"} -d ${deleteSelectedDate.format("YYYY-MM-DD")}`}
            </Text>
          </div>
        </Space>
      </Modal>
    </Card>
  );
};

export default memo(IdpsRecordList);
