import { memo, useMemo, useState } from "react";
import { Card, DatePicker, Space, Typography, theme, Row, Col } from "antd";
import React<PERSON>pex<PERSON><PERSON> from "react-apexcharts";
import { useTheme } from "antd-style";
import dayjs from "dayjs";

const { Text } = Typography;
const { RangePicker } = DatePicker;

/**
 * IDPS Record List Component
 * Displays record list with bar chart and date filtering
 * @param {Object} props Component props
 * @param {Array} props.data Record list data
 * @param {boolean} props.loading Loading state
 * @param {string} props.selectedService Selected service name
 * @returns {JSX.Element} Record list component
 */
const IdpsRecordList = ({
  data = [],
  loading = false,
  selectedService,
}) => {
  const { appearance } = useTheme();
  const { token } = theme.useToken();
  const [dateRange, setDateRange] = useState(null);

  // Filter data based on selected date range
  const filteredData = useMemo(() => {
    if (!dateRange || !dateRange[0] || !dateRange[1]) {
      return data;
    }

    const [startDate, endDate] = dateRange;
    return data.filter(record => {
      if (!record.date) return false;
      const recordDate = dayjs(record.date);
      return recordDate.isAfter(startDate.startOf('day')) && 
             recordDate.isBefore(endDate.endOf('day'));
    });
  }, [data, dateRange]);

  // Process data for bar chart
  const chartOptions = useMemo(() => {
    if (!filteredData || filteredData.length === 0) {
      return null;
    }

    // Group data by date and count files
    const dateGroups = {};
    filteredData.forEach(record => {
      if (record.date && record.files) {
        const date = dayjs(record.date).format('YYYY-MM-DD');
        if (!dateGroups[date]) {
          dateGroups[date] = 0;
        }
        dateGroups[date] += Array.isArray(record.files) ? record.files.length : 1;
      }
    });

    const dates = Object.keys(dateGroups).sort();
    const fileCounts = dates.map(date => dateGroups[date]);

    if (dates.length === 0) {
      return null;
    }

    return {
      series: [
        {
          name: "Files Count",
          data: fileCounts,
        },
      ],
      options: {
        chart: {
          type: "bar",
          height: 350,
          toolbar: {
            show: false,
          },
          background: "transparent",
        },
        theme: {
          mode: appearance === "dark" ? "dark" : "light",
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: "55%",
            borderRadius: 4,
          },
        },
        dataLabels: {
          enabled: false,
        },
        stroke: {
          show: true,
          width: 2,
          colors: ["transparent"],
        },
        colors: [token.colorPrimary],
        xaxis: {
          categories: dates,
          title: {
            text: "Date",
          },
        },
        yaxis: {
          title: {
            text: "Files Count",
          },
        },
        fill: {
          opacity: 1,
        },
        tooltip: {
          y: {
            formatter: function (val) {
              return val + " files";
            },
          },
        },
        title: {
          text: "Record Files by Date",
          align: "left",
          style: {
            fontSize: "16px",
            fontWeight: "bold",
          },
        },
      },
    };
  }, [filteredData, appearance, token]);

  const totalFiles = filteredData.reduce((sum, record) => {
    return sum + (Array.isArray(record.files) ? record.files.length : 1);
  }, 0);

  const handleDateChange = (dates) => {
    setDateRange(dates);
  };

  return (
    <Card
      title={
        <div>
          <Text strong>Record List</Text>
          {selectedService && (
            <div style={{ fontSize: "12px", color: token.colorTextSecondary }}>
              Service: {selectedService}
            </div>
          )}
        </div>
      }
      extra={
        <Space>
          <Text type="secondary">Filter by date:</Text>
          <RangePicker
            value={dateRange}
            onChange={handleDateChange}
            format="YYYY-MM-DD"
            allowClear
            size="small"
          />
        </Space>
      }
      variant="borderless"
      loading={loading}
    >
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          {chartOptions ? (
            <div>
              <div style={{ marginBottom: "16px", textAlign: "center" }}>
                <Space>
                  <Text type="secondary">
                    Total Records: <Text strong>{filteredData.length}</Text>
                  </Text>
                  <Text type="secondary">
                    Total Files: <Text strong>{totalFiles}</Text>
                  </Text>
                  {dateRange && (
                    <Text type="secondary">
                      Date Range: <Text strong>
                        {dateRange[0].format('YYYY-MM-DD')} to {dateRange[1].format('YYYY-MM-DD')}
                      </Text>
                    </Text>
                  )}
                </Space>
              </div>
              <ReactApexChart
                options={chartOptions.options}
                series={chartOptions.series}
                type="bar"
                height={350}
              />
            </div>
          ) : (
            <div
              style={{
                height: 350,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                flexDirection: "column",
              }}
            >
              <Text type="secondary">No record data available</Text>
              {selectedService && (
                <Text type="secondary" style={{ fontSize: "12px", marginTop: "8px" }}>
                  for service: {selectedService}
                </Text>
              )}
              {dateRange && (
                <Text type="secondary" style={{ fontSize: "12px", marginTop: "8px" }}>
                  in selected date range
                </Text>
              )}
            </div>
          )}
        </Col>
        
        <Col xs={24} lg={8}>
          <Card size="small" title="Record Summary">
            <Space direction="vertical" style={{ width: "100%" }}>
              <div>
                <Text type="secondary">Total Records:</Text>
                <div style={{ fontSize: "24px", fontWeight: "bold", color: token.colorPrimary }}>
                  {filteredData.length}
                </div>
              </div>
              <div>
                <Text type="secondary">Total Files:</Text>
                <div style={{ fontSize: "20px", fontWeight: "bold", color: token.colorSuccess }}>
                  {totalFiles}
                </div>
              </div>
              {dateRange && (
                <div>
                  <Text type="secondary">Date Filter:</Text>
                  <div style={{ fontSize: "12px", marginTop: "4px" }}>
                    {dateRange[0].format('MMM DD, YYYY')} - {dateRange[1].format('MMM DD, YYYY')}
                  </div>
                </div>
              )}
            </Space>
          </Card>
        </Col>
      </Row>
    </Card>
  );
};

export default memo(IdpsRecordList);
