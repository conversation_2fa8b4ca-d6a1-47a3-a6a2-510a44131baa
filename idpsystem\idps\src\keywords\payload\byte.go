package payload

import "github.com/google/gonids"

func mapByteHandler(l *list, bm *gonids.ByteMatch, context any) error {
	m := map[string]func(*list, *gonids.ByteMatch, any) error{
		"isdataat": func(l *list, bm *gonids.ByteMatch, _ any) error {
			return newIsDataAt(l, bm)
		},
		"byte_test": func(l *list, bm *gonids.ByteMatch, _ any) error {
			return newByteTest(l, bm)
		},
		"byte_math": func(l *list, bm *gonids.ByteMatch, _ any) error {
			return newByteMath(l, bm)
		},
		"byte_jump": func(l *list, bm *gonids.ByteMatch, _ any) error {
			return newByteJump(l, bm)
		},
		"byte_extract": func(l *list, bm *gonids.ByteMatch, context any) error {
			return newByteExtract(l, bm, context)
		},
	}
	if f, ok := m[bm.Kind.String()]; ok {
		return f(l, bm, context)
	}
	return nil
}

func byteRetrieveSMVar(name string, list *list, index *uint8) bool {
	det := byteExtractRetrieveSMVar(name, list)
	if det != nil {
		bed, _ := det.data.(*byteExtract)
		*index = bed.localID
		return true
	}
	det = byteMathRetrieveSMVar(name, list)
	if det != nil {
		bed, _ := det.data.(*byteMath)
		*index = bed.localID
		return true
	}

	return false
}

func byteExtractRetrieveSMVar(name string, list *list) *Detect {
	if list == nil {
		return nil
	}
	head := list.head
	for head != nil {
		if head.detectedID == detectByteExtract {
			bed, _ := head.data.(*byteExtract)
			if bed.name == name {
				return head
			}
		}
		head = head.next
	}

	return nil
}
func byteMathRetrieveSMVar(name string, list *list) *Detect {
	if list == nil {
		return nil
	}
	head := list.head
	for head != nil {
		if head.detectedID == detectByteMath {
			bed, _ := head.data.(*byteMath)
			if bed.result == name {
				return head
			}
		}
		head = head.next
	}

	return nil
}

func byteExtractStringUint64(data []byte, b int, len int32, res *uint64) int {
	if len > 23 {
		return -1
	}
	if b == 0 {
		b = 10
	}
	num := 0
	for _, v := range data[:len] {
		num = num*int(b) + int(v-'0')
	}
	*res = uint64(num)
	return int(len)
}

func byteExtractUint64(data []byte, e int, len uint16, res *uint64) int {
	var i64 uint64
	var ret int
	if len > 8 {
		return -1
	}
	ret = byteExtractValue(data, e, len, &i64)
	if ret <= 0 {
		return ret
	}
	*res = uint64(i64)
	return int(len)
}

func byteExtractValue(data []byte, e int, len uint16, res *uint64) int {
	if e != byteLittleEndiad && e != byteBigEndiad {
		return -1
	}
	for i := uint16(0); i < len; i++ {
		var b uint64
		if e == byteLittleEndiad {
			b = uint64(data[i])
		} else {
			b = uint64(data[len-i-1])
		}
		*res |= (b << ((i & 7) << 3))

	}
	return int(len)

}
