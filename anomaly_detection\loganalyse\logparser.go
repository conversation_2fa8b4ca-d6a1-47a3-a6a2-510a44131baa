package loganalyse

import (
	"bufio"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mnms/anomaly_detection/chain"
	"mnms/anomaly_detection/chain/anomalychain"
	"mnms/anomaly_detection/def"
	"mnms/anomaly_detection/statistic"
	"mnms/llm"

	"mnms/anomaly_detection/report"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/qeof/q"
)

var logPatterns = []*regexp.Regexp{
	regexp.MustCompile(`^<(\d+)>(\w+\s+\d+\s+\d+:\d+:\d+)\s+(\S+)\s+([^:]+):\s+(.*)$`),
	regexp.MustCompile(`^<(\d+)>(\w+\s+\d+\s+\d+:\d+:\d+)\s+(\S+)\s+(.*)$`),
	regexp.MustCompile(`^<(\d+)>(\w+\s+\d+\s+\d+:\d+:\d+)\s+([\d.]+:\d+)\s+\d{4}.\d{2}.\d{2}\s+\d{2}:\d{2}:\d{2}\s+\d{2}d\d{2}h\d{2}m\d{2}s\s+syslog:\s+(.*)$`),
}

// LogStruct defines BlackBear TechHive devices log struct
type LogStruct struct {
	Priority  int64  `json:"priority"`
	Timestamp string `json:"timestamp"`
	Hostname  string `json:"hostname"`
	Tag       string `json:"tag"`
	Message   string `json:"message"`
}

// ParseLog convert raw text log to a LogStruct
func ParseLog(log string) (*LogStruct, error) {
	for _, pattern := range logPatterns {
		match := pattern.FindStringSubmatch(log)
		if len(match) == 0 {
			continue
		}
		facility, err := strconv.ParseInt(match[1], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("convert facility %s to int64 error: %v", match[1], err)
		}
		switch pattern {
		case logPatterns[0]:
			// have tag

			return &LogStruct{
				// convert match[1] to int
				Priority:  facility,
				Timestamp: strings.TrimSpace(match[2]),
				Hostname:  strings.TrimSpace(match[3]),
				Tag:       strings.TrimSpace(match[4]),
				Message:   strings.TrimSpace(match[5]),
			}, nil
		case logPatterns[1]:
			// no tag
			return &LogStruct{
				Priority:  facility,
				Timestamp: strings.TrimSpace(match[2]),
				Hostname:  strings.TrimSpace(match[3]),
				Message:   strings.TrimSpace(match[4]),
			}, nil
		case logPatterns[2]:
			return &LogStruct{
				Priority:  facility,
				Timestamp: strings.TrimSpace(match[2]),
				Hostname:  strings.TrimSpace(match[6]),
				Message:   strings.TrimSpace(match[7]),
			}, nil
		default:
			return nil, fmt.Errorf("unknown pattern: %v", pattern)
		}

	}
	return nil, fmt.Errorf("log %s format error", log)
}

// checkJSONAnomaly check json anomaly, if input string is not a valid json, return error
func checkJSONAnomaly(s string) (*def.LogAnomalyResult, error) {
	var js map[string]interface{}
	err := json.Unmarshal([]byte(s), &js)
	if err != nil {
		return &def.LogAnomalyResult{
			Text:     s,
			Reporter: LocalReporter,
			Reason:   "not a JSON message",
			TakeOver: false,
		}, nil
	}
	// check map["success"] == false or map["success"] == "false"
	success, ok := js["success"]
	if ok {
		switch success.(type) {
		case bool:
			if !success.(bool) {
				return &def.LogAnomalyResult{
					Text:     s,
					Reporter: LocalReporter,
					Normal:   false,
					Reason:   "message field success is false",
					TakeOver: true,
				}, nil
			}
		case string:
			if success.(string) == "false" {
				return &def.LogAnomalyResult{
					Text:     s,
					Reporter: LocalReporter,
					Normal:   false,
					Reason:   "message field success is false",
					TakeOver: true,
				}, nil
			}
		}
	}

	// check map["status"] != ok
	status, ok := js["status"]
	if ok {
		if status.(string) != "ok" {
			return &def.LogAnomalyResult{
				Reporter: LocalReporter,
				Text:     s,
				Normal:   false,
				Reason:   "message field status is not ok",
				TakeOver: true,
			}, nil
		}
	}

	return &def.LogAnomalyResult{
		Text:     s,
		Normal:   true,
		Reporter: LocalReporter,
		Reason:   "message JSON object has no anomaly",
		TakeOver: true,
	}, nil

}

// extractFirstJSON extracts the first JSON object found in the input string.
func extractFirstJSON(input string) (string, error) {
	start := -1       // Start index of the JSON object
	depth := 0        // Braces nesting depth
	inString := false // Flag to indicate if we're inside a string
	escaped := false  // Flag to handle escaped characters

	for i := 0; i < len(input); i++ {
		c := input[i]

		if escaped {
			// Skip the next character if it's escaped
			escaped = false
			continue
		}

		switch c {
		case '\\':
			// Handle escape character
			if inString {
				escaped = true
			}
		case '"':
			// Toggle inString flag when a quote is encountered
			inString = !inString
		case '{':
			if !inString {
				// Increase depth when a '{' is found outside a string
				if depth == 0 {
					start = i
				}
				depth++
			}
		case '}':
			if !inString {
				// Decrease depth when a '}' is found outside a string
				depth--
				if depth == 0 && start != -1 {
					// Return the JSON substring when depth returns to 0
					return input[start : i+1], nil
				} else if depth < 0 {
					// Error if braces are unmatched
					return "", errors.New("JSON not found")
				}
			}
		}
	}

	if depth != 0 {
		// Error if braces are unmatched at the end of the string
		return "", errors.New("JSON not found")
	}

	return "", errors.New("JSON not found")
}

// LogAnomalyDetect detect anomaly
func LogAnomalyDetect(m llm.LLMClient, log string, score float32) (*def.LogAnomalyResult, error) {

	// step1. use local log parser to detect anomaly
	result, err := LogParserDetectAnomaly(log)
	if err != nil {
		return nil, fmt.Errorf("log parser detect anomaly error: %v", err)
	}

	if !result.TakeOver {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		// not take over go to next step
		anomChain := anomalychain.NewChain(m)
		// callback function

		replacedLog := NERClassify(log)

		ret, err := anomChain.Run(ctx, replacedLog,
			// chain.WithDebugOutput("debug.log"), disable the debug output
			chain.WithScoreThreshold(score))

		if err != nil {
			return &def.LogAnomalyResult{
				Text:     err.Error(),
				Reporter: AIReporter,
				Reason:   "Call API fail",
				Normal:   false,
			}, fmt.Errorf("log openAI detect anomaly error: %v", err)
		}
		jsonText, err := extractFirstJSON(ret)
		if err != nil {
			q.Q("extract first JSON object error, ", err)
			q.Q("input: ", ret)
			return nil, fmt.Errorf("extract first JSON object error: %v", err)
		}
		var jsonResult map[string]interface{}
		err = json.Unmarshal([]byte(jsonText), &jsonResult)
		if err != nil {
			return nil, fmt.Errorf("unmarshal openAI result error: %v", err)
		}

		var normal = false
		var reason = ""
		_, ok := jsonResult["normal"]
		if ok {
			normal, ok = jsonResult["normal"].(bool)
			if !ok {
				normal = false
			}
		}
		_, ok = jsonResult["reason"]
		if ok {
			// saft convert to string
			reasonVal, ok := jsonResult["reason"].(string)
			if !ok {
				reason = ""
			} else {
				reason = reasonVal
			}
		}

		return &def.LogAnomalyResult{
			Text:     result.Text,
			Normal:   normal,
			Reason:   reason,
			Reporter: AIReporter,
		}, nil
	}
	return result, nil
}

// CheckPatterns check log message with predefined patterns
func CheckPatterns(log string) (*def.LogAnomalyResult, error) {

	// check message is kind of InsertDev: new device: 00-60-E9-1A-3B-89
	// create pattern
	newDevicePattern := regexp.MustCompile(`^new\s+device:\s+([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$`)
	// check message is kind of InsertTopo: new topology from: jilllocal
	newTopoPattern := regexp.MustCompile(`^new\s+topology\s+from:\s+(\w+)$`)
	// add all patterns to a slice
	patterns := map[string]*regexp.Regexp{
		"newDevice": newDevicePattern,
		"newTopo":   newTopoPattern,
	}
	// check log message with all patterns
	for name, pattern := range patterns {
		match := pattern.FindStringSubmatch(log)
		if len(match) == 0 {
			continue
		}

		return &def.LogAnomalyResult{
			Text:     log,
			Normal:   true,
			Reporter: LocalReporter,
			Reason:   fmt.Sprintf("log message is %s", name),
			TakeOver: true,
		}, nil
	}
	return &def.LogAnomalyResult{
		Text:     log,
		Reporter: LocalReporter,
		Reason:   "log message is not predefined pattern",
		TakeOver: false,
	}, nil
}

// LogParserDetectAnomaly detect anomaly
func LogParserDetectAnomaly(log string) (*def.LogAnomalyResult, error) {

	logStruct, err := ParseLog(log)
	if err != nil {
		// logParser didn't recognize this log
		return &def.LogAnomalyResult{
			Text:     log,
			Reporter: LocalReporter,
			Reason:   err.Error(),
			TakeOver: false,
		}, nil
	}
	// Check predefined patterns
	patternResult, err := CheckPatterns(logStruct.Message)
	if err != nil {
		return nil, fmt.Errorf("check patterns error: %v", err)
	}
	if patternResult.TakeOver {
		return patternResult, nil
	}

	message := logStruct.Message

	return checkJSONAnomaly(message)

}

// Predefined NER patterns
var (
	// MAC address
	macPattern = regexp.MustCompile(`([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})`)
	// IPV4 address
	ipv4Pattern = regexp.MustCompile(`\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b`)
	// IPV6 address
	ipv6Pattern = regexp.MustCompile(`\b[0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){7}\b`)
	// IPV4 address with port number
	ipv4PortPattern = regexp.MustCompile(`\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d{1,5}\b`)
	// IPV6 address with port number
	ipv6PortPattern = regexp.MustCompile(`\b[0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){7}:\d{1,5}\b`)
	// Atop device's time format
	atopDevLogTime = regexp.MustCompile(`\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) \d{1,2} \d{2}:\d{2}:\d{2}\b`)
)

// NERClassify detect named entity recognition in log message, replace to predefined category
// MAC address => {MAC}
// IPV4 address => {IPV4}
// IPV6 address => {IPV6}
func NERClassify(log string) string {
	// replace mac address
	log = macPattern.ReplaceAllString(log, "[MASKED MAC]")
	// replace ipv4 address
	log = ipv4Pattern.ReplaceAllString(log, "[MASKED IPV4]")
	// replace ipv6 address
	log = ipv6Pattern.ReplaceAllString(log, "[MASKED IPV6]")
	// replace atop device's time format
	log = atopDevLogTime.ReplaceAllString(log, "[MASKED TIME]")
	// replace ipv4 address with port number
	log = ipv4PortPattern.ReplaceAllString(log, "[MASKED IPV4 PORT]")
	// replace ipv6 address with port number
	log = ipv6PortPattern.ReplaceAllString(log, "[MASKED IPV6 PORT]")
	return log
}

// dumpDebug append debug info to file debug.log
func dumpDebug(format string, a ...any) error {
	if q.P != "" {
		log := fmt.Sprintf(format, a...)
		f, err := os.OpenFile("debug.log", os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
		if err != nil {
			return fmt.Errorf("open debug.log error: %v", err)
		}
		defer f.Close()
		if _, err := f.WriteString(log + "\n"); err != nil {
			return fmt.Errorf("write debug.log error: %v", err)
		}
	}
	return nil
}

var defaultYear = 1983

// SyslogFileAnalyseOpt defines syslog file analyse options
type SyslogFileAnalyseOpt struct {
	LastLines int       `json:"last_lines"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Score     float32   `json:"score"`
}

// convertSyslogTime convert syslog time to time.Time
func convertSyslogTime(t string) (time.Time, error) {

	// check input t if it is whole syslog, extract time part
	// whole syslog should like this :
	// <1>May 15 15:31:23 .....
	strs := regexp.MustCompile(`^<\d+>(\w+\s+\d+\s+\d+:\d+:\d+)\s+`).FindStringSubmatch(t)
	if len(strs) > 0 {
		t = strs[1]
	}

	// input t time format Jul 17 12:30:48
	// convert t to time, because syslog doesn't have year, so we need to add a default year 1983

	yearStr := strconv.Itoa(defaultYear)
	tt, err := time.Parse("2006 Jan 2 15:04:05", yearStr+" "+t)
	if err != nil {
		return time.Time{}, fmt.Errorf("parse time error: %v", err)
	}
	return tt, nil
}

// SetTime set start time and end time
func (o *SyslogFileAnalyseOpt) SetTime(start string, end string) error {
	// input start and end time format Jul 17 12:30:48
	if len(start) == 0 {
		o.StartTime = time.Time{}
	} else {
		// convert start to time
		startTime, err := convertSyslogTime(start)
		if err != nil {
			return fmt.Errorf("convert syslog time error: %v", err)
		}
		o.StartTime = startTime
	}
	if len(end) == 0 {
		o.EndTime = time.Time{}
	} else {
		// convert end to time

		endTime, err := convertSyslogTime(end)
		if err != nil {
			return fmt.Errorf("convert syslog time error: %v", err)
		}
		o.EndTime = endTime
	}

	return nil
}

// IsTimeBetween check t is between start time and end time
func (o *SyslogFileAnalyseOpt) IsTimeBetween(t string) (bool, error) {
	q.Q("IsTimeBetween: ", t, o.StartTime, o.EndTime)
	// if both time don't set it always return true
	if o.StartTime == (time.Time{}) && o.EndTime == (time.Time{}) {
		return true, nil
	}

	// input t time format Jul 17 12:30:48
	// convert t to time
	tt, err := convertSyslogTime(t)
	if err != nil {
		return false, fmt.Errorf("convert syslog time error: %v", err)
	}

	if tt.After(o.StartTime) && tt.Before(o.EndTime) {
		return true, nil
	}
	return false, nil
}

// readLastNLines read last n lines from file
func readLastNLines(f *os.File, n int) ([]string, error) {
	var lines []string
	buf := make([]byte, 1)

	// Start reading from the end of the file
	offset, err := f.Seek(0, io.SeekEnd)
	if err != nil {
		return nil, fmt.Errorf("seek error: %v", err)
	}
	lineCount := 0
	for offset > 0 && lineCount <= n {
		offset--
		_, err = f.Seek(offset, io.SeekStart)
		if err != nil {
			return nil, err
		}

		if _, err = f.Read(buf); err != nil {
			return nil, err
		}

		if buf[0] == '\n' {
			lineCount++
		}
	}

	scanner := bufio.NewScanner(f)
	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}

	if err = scanner.Err(); err != nil {
		return nil, err
	}

	return lines, nil

}

type SyslogFileAnalyseResult struct {
	Error     string `json:"error"`
	TotalLogs int    `json:"total_logs"`
}

// GenerateAnalyseReport generate analyse report
func GenerateAnalyseReport(inputLogs []string, source, client string, distance float32) (*report.Report, error) {

	if distance <= 0 {
		distance = DefaultScore
	}
	s := report.NewMessageArray()
	analyseReport := report.NewReport(client, source, distance, s)

	err := AddLogs(analyseReport, inputLogs)
	if err != nil {
		analyseReport.Status = fmt.Sprintf("error: %v", err)
	}
	analyseReport.Status = "ok"

	return analyseReport, err
}

// AddLogs append input logs analysed result to report
func AddLogs(r *report.Report, inputLogs []string) error {
	if len(inputLogs) == 0 {
		return fmt.Errorf("no logs to analyse")
	}

	score := r.Distance
	llmSettings, err := statistic.GetLargeLanguageModelFromAnomalyStatistics()
	if err != nil {
		return err
	}
	mod, err := llm.NewLLMClient(llmSettings)
	if err != nil {
		return err
	}

	for _, log := range inputLogs {

		result, err := LogAnomalyDetect(mod, log, score)
		if err != nil {
			r.AddMessage(log, "error", err.Error())
			continue
		}
		if !result.Normal {
			r.AddMessage(log, "anomaly", result.Reason)
		} else {
			r.AddMessage(log, "normal", "")
		}

	}
	r.Until = time.Now().Format(time.RFC3339)
	return nil
}
