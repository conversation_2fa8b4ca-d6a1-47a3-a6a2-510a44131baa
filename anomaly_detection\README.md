# Anomaly service

## Introduction
The `bbanomaly` is an advanced tool designed to analyse syslog. The feature integrated with OpenAI's API. Through its API and UI, users can effortlessly feed logs for analysis, detect anomalies. 


## OpenAI API key 
(Need to update)
To use anomaly detection feature, you need to have an OpenAI API key. You can get it from [OpenAI](https://beta.openai.com/). After you get the API key, set it to the `openai.key` file and put it to `bbanomaly` working directory or the home directory of the user who run `bbanomaly`.



## Glossary of Terms
- **Syslog message template**: A predefined structure used to store syslog messages. Users can define templates to categorize messages as anomalies or normal based on the specific patterns or criteria set within the template. This facilitates the process of log analysis by automating the classification of incoming syslog messages.

- **Unrecognized log**:  These are the syslog entries that do not match any existing pattern within the `Syslog message template`. When the system encounters a syslog message that it cannot correlate with a known template, it places that message in the "unrecognized logs" category. Users can later review these logs to determine whether they are normal or anomalous and subsequently incorporate them into the appropriate log templates for future reference.

## Start anomaly service
To start anomaly service there are two necessary flags: `-n` and `-r`. `-n` is the name of the service, `-r` is the root url of the service. The following is an example of how to start anomaly service. 

```
$ bbanomaly -n as -r http://localhost:27182
```


## Feature Overview
### 1. Intelligent log analysis with OpenAI
- Leveraging OpenAI's machine learning capabilities, our system interprets the semantic meaning behind each log entry rather than relying on simple pattern matching.
### 2. Vector Database Maintenance
- Upon analyzing a log, the system maps it to a multi-dimensional vector space, preserving its semantic meaning.
- Users have the ability to label specific logs as 'normal' or 'anomaly'.
- Over time, this labeled data aids in refining and enhancing the system's accuracy.
### 3. Recognized vs. Unrecognized Logs
- In cases where the system encounters unfamiliar logs, these entries are stored in an 'unrecognized' database.
- Users can later review and label unrecognized logs, ensuring continuous learning and system enhancement.
- The system will cover more and more logs as it learns from the user's feedback.
### 4. User-Centric Labeling interface
- An `nimbl` UI allows users to review both recognized and unrecognized logs.
- Users can provide feedback, label logs, and help the system adapt to ever-changing log patterns.

## How to use anomaly service

### Analyze syslog messages

To analyze syslog messages, refer to the following steps:  
1. **Message input**: There are several ways to input syslog messages, basically `bbanomaly` analysis all incoming syslog messages by default. You can also use command `anomaly analyse [url] [distance]` to analyse specify a URL of file which contains syslog message
2. 
3. 
4. s.

5. **Analyse message**: `nimbl` will analyse syslog messages automatically. In general, no additional processing is required. There are two results after analysed the syslog messages:  
    - **Recognized**: `nimbl` will try to match the syslog messages with existing syslog message templates. If matched, the syslog messages will be recognized and put into message histories, GET `/anomaly-detect/logs` to get history messages.
    - **Unrecognized**: If `nimbl` can't match the syslog messages with existing syslog message templates, the syslog messages will be unrecognized and put into unrecognized messages, GET `/anomaly-detect/unrecognized` to get unrecognized logs. Users can review and categorize these unrecognized messages later. Accumulate enough unrecognized messages will help `nimbl` to learn more patterns and improve the accuracy of anomaly detection.

6. **Review results**: Check `/anomaly-detect/logs` API response to see analysis results. 


## Indexing
The anomaly detection feature leverages a vector database to search similar logs. When users modify logs whithin the system, synchronization with the vector database is required to ensure accuracy and consistency.

### Synchronization check
To verify the necessity of synchronization, utilize the following API endpoint:
- GET `/sync`: This endpoint should be called to cehck if the data within the vector datavbase needs to be updated. It will assess the current state and return a response indecating whether whychronization is required.

### Exectuing synchronization
Upon confirmation that synchronization is needed, the following API endpoint should be called:
- POST `/async/vectors/sync`: This endpoint triigers the process of synchronizing the database logs with the vector database. It ensures that all recent changes are reflected in the vector database.


## Analyzing logs
`nimbl` checks all incomimg syslog messages by default, in the UI anomaly detection page you can see results of syslog messages analysis. `/logs` API also returns the analysis result. User can turn off syslog analysis with `/anomaly-detect/enable` API or in the UI. 



# API Documentation

## Overview
This document provides details on the usage of our two main types of APIs housed under the /api/v1/anomaly-detect endpoint: synchronous (sync) and asynchronous (async).

- Synchronous API(sync API): Provides an immediate response after the request is processed.
- Asynchronous API(async API): Returns task id. Users can later tract the task status using the provided task id.

## Synchronous APIs
### Endpoint
All sync APIs are located under the `/api/v1/anomaly-detect`` endpoint, with the exception of async APIs which have their separate endpoint.

### Usage
Make a request to the desired sync API endpoint to get an immediate response.

Example endpoints:

- `/api/v1/anomaly-detect/logs`
- `/api/v1/anomaly-detect/unrecognized`

Response format:  
```json
{
    "data": { ... },
    "error": "",
    "success": true
}
```

## Asynchronous APIs
### Endpoint
All async APIs are located under the `/api/v1/anomaly-detect/async` endpoint.
### Usage
Make a request to the desired async API endpoint to initiate a background task. The API will return task information.

Example endpoints:  
- `/api/v1/anomaly-detect/async/import`
- `/api/v1/anomaly-detect/async/logs/detect`

Response format:  Async API will returns a response which contain task id in the data.id
```json
{
    "data": {
        "id": "705ec208-49de-4ebe-89e0-38e401219641",
        "status": "running",
        "message": "",
        "description": "get data.text embedding"
    },
    "error": "",
    "success": true
}
```
Fields: 
- **id**: Unique identifier for the task.
- **status**: Current status of the task. Possible values: running, completed, failed, etc.
- **message**: Additional messages related to the task, especially for errors.
- **description**: Brief description of the task.

> All anomaly detection APIs need superuser permission. Here is the example of how to get token for superuser: 
```bash
$ TOKEN=$(curl -s -X POST -H 'Accept: application/json' -H 'Content-Type: application/json' --data '{"user":"admin","password":"testpw"}' http://localhost:27182/api/v1/login | jq -r '.token')
 
$ echo $TOKEN
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2OTc2ODI5MTcsInRpbWVzdGFtcCI6IjIwMjMtMDktMTlUMTA6MzU6MTcrMDg6MDAiLCJ1c2VyIjoiYWRtaW4ifQ.PVes-1ZhdEGR1xttlBEsyBu-BfWg_TjKei55u32COvE
```
> Remember to change host and port to your own server. and user/password to your own superuser.

## Sync status
Get current sync status, the sync status is used to indicate our vectors database is ready for search or not. If the sync status is not ready, client can send a POST request to `/api/v1/anomaly-detect/async/vectors/sync` to start a sync task.  
- `GET /sync`

Request sample:  
```bash
$ curl -H 'Accept: application/json' -H "Authorization: Bearer ${TOKEN}" http://localhost:27182/api/v1/anomaly-detect/sync 
```

Response:  
```json
{
  "data": {
    "need_sync": false
  },
  "error": "",
  "success": true
}
```

## Unrecognized logs
### GET unrecognized logs
Get unrecognized logs, the unrecognized logs are the logs that can't be recognized by our anomaly detection system. User can label these logs and send them to `TODO` to improve anomaly detect system.

- `GET /unrecognized`  
Query parameters:
- count: number of vectors to return
- sortColumn: sort column name
- sortDirection: sort direction, asc or desc
- page: page number
- pageSize: page size
- startTime: start time
- endTime: end time

Start time and End time format sample:  
```json
{
    "startTime": "2006-01-02 15:04:05",
    "endTime": "2006-01-02 15:22:05"
}
```


> Time shuold be in UTC format.

Request sample:  
```bash
 curl -H 'Accept: application/json' -H "Authorization: Bearer ${TOKEN}" http://localhost:27182/api/v1/anomaly-detect/unrecognized
 ```
Response:  
```json
{
  "data": [
    {
      "candidate_id": 20,
      "id": 10,
      "score": 0.43335503,
      "text": "Tony quit",
      "timestamp": "2023-09-05T11:44:37.60151Z",
      "vector": "[-0.011216911, -0.024975711, 0.023333259, ...]"
    },
    {
      "candidate_id": 20,
      "id": 9,
      "score": 0.40537024,
      "text": "Sam quit",
      "timestamp": "2023-09-05T11:44:36.853797Z",
      "vector": "[-0.0047319764, -0.012245521, 0.014993975, ...]"
    },
    {
      "candidate_id": 21,
      "id": 8,
      "score": 0.4811857,
      "text": "daneil quit",
      "timestamp": "2023-09-05T11:44:36.195704Z",
      "vector": "[-0.02164053, 0.0037576212, -0.005434692, ...]"
    },
    {
      "candidate_id": 21,
      "id": 7,
      "score": 0.44697797,
      "text": "alan quit",
      "timestamp": "2023-09-05T11:44:35.651059Z",
      "vector": "[-0.024489278, 0.00040615062, 0.0119987335, ...]"
    }
  ],
  "error": "",
  "success": true
}
```
- candidate_id: the id of the candidate log template
- id: the id of the unrecognized log
- score: the similarity score between the unrecognized log and the candidate log template
- text: the unrecognized log text
- timestamp: the unrecognized log timestamp
- vector: the vector of the unrecognized log

### Export unrecognized logs
Export unrecognized logs to a csv file

- GET `/unrecognized/export`

Request sample:  
```bash
curl -H 'Accept: application/json' -H "Authorization: Bearer ${TOKEN}" http://localhost:27182/api/v1/anomaly-detect/unrecognized/export --output unrecog.csv
```
Response is a csv file.  

### Delete unrecognized logs
Delete unrecognized logs by id

- DELETE `/unrecognized/{id}`


### Update, Add Vector
POST /api/v1/vectors, PUT /api/v1/vectors
If `id` doesn't present, system will add new vector with auto-increment id. If `id` is present and it is not exist, system will add new vector also. If `id` is present and it is exist, system will update the vector with specified id.  
```bash
Table is not necessary, if table is not specified, it will be created automatically with the default table.
```bash
curl --location 'http://localhost:27188/api/v1/vectors' \
--header 'Content-Type: application/json' \
--data '{
    "table":"test22",
    "vector": [1.0,2.0,3.0],
    "data":{
        "rawlog":"hello testing",
        "meta": "skip"
    }
}'
```
Response sample:
```json
{"data":{"id":2},"error":"","success":true}
```

### Query Vector
GET /api/v1/vectors
Accept query parameters:
- table: table name
- count: number of vectors to return
- sortColumn: sort column name
- sortDirection: sort direction, asc or desc
- page: page number
- pageSize: page size
- other query parameters will be used as filter conditions


```bash
curl --location 'http://localhost:27188/api/v1/vectors?count=1&sortColumn=vector&sortDirection=asc'
```

*More query*
POST /api/v1/vectors/query
```bash

```

### Delete Vector
Delete vector by ids, please refer following request sample
```bash
curl --location --request DELETE 'http://localhost:27188/api/v1/vectors' \
--header 'Content-Type: application/json' \
--data '{
    "table":"test22",
    "ids":[1,2]
}'
```



## Embedding


This API supposed to be used by system. Client rarely use this API directly.


POST /api/v1/embedding  
Make embedding for specified text, this is a long running task, the request is a kind of SSE request. Server will return the progress of the task. Please refer following response sample.

```bash
curl --location 'http://localhost:27188/api/v1/embedding' \
--header 'Accept: text/event-stream' \
-N \
--data '{
    "data":[
        {
            "text":"hello1"
        },
        {
            "text":"hello2"
        }
    ]
}'
```
Response  
```
{"total":2,"finished":1,"insert_table":"vectors"}
{"total":2,"finished":2,"insert_table":"vectors"}
{"data":{"insert_table":"vectors","total_vectors":2},"error":"","success":true}
```

# Asynchronous APIs
Asynchronous APIs are used to handle long running tasks, such as embedding, training, etc. APIs return a task id, client can use this id to query the task status.

Asynchronous APIs always return a response with a task, following is the response sample:
```json
{
    "data": {
        "id": "705ec208-49de-4ebe-89e0-38e401219641",
        "status": "running",
        "message": "",
        "description": "get data.text embedding "
    },
    "error": "",
    "success": true
}
```
Client can get task status by GET `/api/v1/tasks/{id}` 

## Embedding
POST /async/v1/embedding  
Make embedding for specified text. The field `table` in the request is optional. If it is not specified, system will use default table instead. 

This API will get `data.text` value's vector and store it to `table` table. 

Field `data` is a list of object, each object must has a field `text` which is the text to be embedded.

**Request sample**  
```json
{
    "table":"table-name-which-will-store-data-and-embedding",
    "data":[
        {
            "text":"hello1"
        },
        {
            "text":"hello2"
        }
    ]
}
```
**Response sample**  
```json
{
    "data": {
        "id": "705ec208-49de-4ebe-89e0-38e401219641",
        "status": "running",
        "message": "",
        "description": "get data.text embedding "
    },
    "error": "",
    "success": true
}
```

## Anomaly Detection
POST /logs/detection  
Detect anomaly logs, the field `log` is a input log, and the field `score` is a float value that will use to search vector database. Use `/async/v1/embeddings` to add new log template to vector database.

**Request sample**  
```bash
curl -H 'Accept: application/json' -H "Authorization: Bearer ${TOKEN}" http://localhost:27182/api/v1/anomaly-detect/async/logs/detect --data '{"logs":["curl1"]}'
```

**Response sample**  
```json
{
    "data":{
        "id":"3eb41b31-040b-42d3-bfff-7b5c1530713e",
        "status":"running",
        "message":"",
        "description":"detect anomaly logs",
        "percent":0
    },
    "error":"",
    "success":true
}
```

## Detect syslog file
POST /async/analyse-file

request sample: analyse file's last 30 lines
```json
{
    "file_url": "http://localhost:27188/api/v1/file/1",
    "last_line": 30,
}
```

request sample: analyse log between 3/10/11:00 to 3/10/12:00
```json
{
    "file_url": "http://localhost:27188/api/v1/file/1",
    "start_time": "May 15 15:00:00",
    "end_time": "May 15 16:23:05"
}
```
Response doen't contain analyse result, it only contains task id. Client can use this task id to query task status.
Response sample: 
```json
{
    "data": {
        "id": "4aa5b318-020d-4166-800e-216fa7c9b407",
        "status": "running",
        "message": "",
        "description": "analyse logs from http://localhost:8080/rawlog.log",
        "percent": 0
    },
    "error": "",
    "success": true
}
```
To check task status, use GET /tasks/{id} API. The response should like this:
```json
{
    "data": {
        "id": "4aa5b318-020d-4166-800e-216fa7c9b407",
        "status": "ok",
        "message": "detect 52/52 finished, 3 anomaly, 0 unrecognized",
        "description": "analyse logs from http://localhost:8080/rawlog.log",
        "percent": 1
    },
    "error": "",
    "success": true
}
```

GET /file-results to get analyse result.

Accept query parameters:
- id: the result id, same as task id
- anomaly-logs: display anomaly logs in the response or not
- unknown-logs: display unknown logs in the response or not
- error-logs: display error logs in the response or not
> anomaly-logs, unknown-logs, error-logs can be true or false, these logs could be very large, so it is better to set them to false.

This is a sample of requst:
```
http://localhost:27182/api/v1/anomaly-detect/file-results?id=4aa5b318-020d-4166-800e-216fa7c9b407&anomaly-logs=true&error-logs=true&unknown-logs=true
```

```json
{
    "data": {
        "id": 6,
        "task_id": "4aa5b318-020d-4166-800e-216fa7c9b407",
        "error": "",
        "total_logs": 52,
        "count_unknown_logs": 0,
        "count_anomaly_logs": 3,
        "anomaly_logs": [
            "<134>Jul 17 10:45:07 combo sshd(pam_unix)[24031]: authentication failure; logname= uid=0 euid=0 tty=NODEVssh ruser= rhost=61-220-159-99.hinet-ip.hinet.net  user=root",
            "<134>Jul 17 10:45:07 combo sshd(pam_unix)[24033]: authentication failure; logname= uid=0 euid=0 tty=NODEVssh ruser= rhost=61-220-159-99.hinet-ip.hinet.net  user=root",
            "<134>Jul 17 10:45:07 combo sshd(pam_unix)[24030]: authentication failure; logname= uid=0 euid=0 tty=NODEVssh ruser= rhost=61-220-159-99.hinet-ip.hinet.net  user=root"
        ],
        "unknown_logs": null,
        "error_logs": null,
        "timestamp": "2023-10-31T19:15:08.693637Z"
    },
    "error": "",
    "success": true
}
```

## Input syslog messages
Query input syslog messages 
GET `/logs`

Accept query parameters:
- count: number of vectors to return
- sortColumn: sort column name
- sortDirection: sort direction, asc or desc
- page: page number
- pageSize: page size
- startTime: start time
- endTime: end time
- other query parameters will be used as filter conditions

```bash
$ curl -H 'Accept: application/json' \
-H "Authorization: Bearer ${TOKEN}" \
--location `http://localhost:27182/async/v1/logs` \

```

