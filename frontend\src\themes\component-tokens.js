export const componentTokens = {
  Table: {
    fontSize: 14,
    algorithm: true,
  },
  Statistic: {
    contentFontSize: 16,
    algorithm: true,
  },
  Button: {
    fontSize: 14,
    algorithm: true,
  },
  Card: {
    boxShadow:
      "rgb(0 0 0 / 20%) 0px 2px 1px -1px, rgb(0 0 0 / 14%) 0px 1px 1px 0px, rgb(0 0 0 / 12%) 0px 1px 3px 0px",
    algorithm: true,
  },
  Descriptions: {
    titleMarginBottom: 0,
    algorithm: true,
  },
  Menu: {
    colorActiveBarWidth: 0,
    itemBg: "transparent",
    subMenuItemBg: "transparent",
    colorSplit: "transparent",
    algorithm: true,
  },
  Badge: {
    statusSize: 14,
    algorithm: true,
  },
};
