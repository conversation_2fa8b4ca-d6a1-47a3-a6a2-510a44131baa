import { createFileRoute, Outlet, redirect } from "@tanstack/react-router";
import { isExpired } from "react-jwt";
import MainLayout from "../layout/main-layout";
import { useSettingStore } from "../store/setting-store";
import useWebSocket from "react-use-websocket";
import { useEffect, useCallback, useMemo } from "react";
import { useGetRootInfo, useGetSyslogAlert } from "../services/queries";
import { useSocketStore } from "../store/socket-store";

/**
 * Custom hook to handle WebSocket connections and message processing
 *
 * @param {string} url - WebSocket URL to connect to
 * @returns {Object} WebSocket connection status and last message
 */
function useAppWebSocket(url) {
  const {
    setEnabledFeatures,
    setLicenseError,
    setClearLicenseError,
    addMessage,
  } = useSocketStore();

  // WebSocket connection handler
  const handleWebSocketOpen = useCallback(() => {
    console.log("Socket connection established.");
  }, []);

  // WebSocket configuration
  const webSocketConfig = useMemo(
    () => ({
      onOpen: handleWebSocketOpen,
      shouldReconnect: () => true,
      share: true,
    }),
    [handleWebSocketOpen]
  );

  // Initialize WebSocket connection
  const { lastMessage, readyState } = useWebSocket(url, webSocketConfig);

  // Process WebSocket messages
  useEffect(() => {
    if (!lastMessage) return;

    try {
      const res = JSON.parse(lastMessage.data);
      if (!res) return;

      switch (res.kind) {
        case "license_alert_features":
          setEnabledFeatures(res.message.split(","));
          break;
        case "license_alert_error":
          setLicenseError(res.message);
          break;
        case "license_alert_clear":
          setClearLicenseError();
          break;
        default:
          addMessage({
            title: res.kind,
            message: res.message,
            time_stamp: Date.now(),
          });
          break;
      }
    } catch (error) {
      console.error("Failed to parse WebSocket message:", error);
    }
  }, [
    lastMessage,
    setEnabledFeatures,
    setLicenseError,
    setClearLicenseError,
    addMessage,
  ]);

  return { lastMessage, readyState };
}

/**
 * Custom hook to process syslog alerts
 *
 * @param {Array} syslogAlert - Raw syslog alert data
 * @returns {void}
 */
function useSyslogAlertProcessor(syslogAlert) {
  const { setMessages, messages } = useSocketStore();

  // Format and process syslog alerts
  const formattedMessages = useMemo(() => {
    if (!syslogAlert) return [];

    return syslogAlert
      .map((item) => ({
        title: item.kind,
        message: item.message,
        time_stamp: new Date(item.timestamp).toUTCString().replace("GMT", ""),
      }))
      .sort((a, b) => new Date(b.time_stamp) - new Date(a.time_stamp));
  }, [syslogAlert]);

  // Update messages in store when formatted messages change
  useEffect(() => {
    if (formattedMessages.length > 0 && messages.length === 0) {
      setMessages(formattedMessages);
    }
  }, [formattedMessages, messages.length, setMessages]);
}

/**
 * Custom hook to process root info data
 *
 * @param {Object} rootInfo - Root info data containing license information
 * @returns {void}
 */
function useRootInfoProcessor(rootInfo) {
  const { setEnabledFeatures } = useSocketStore();

  // Process root info and extract enabled features
  useEffect(() => {
    if (rootInfo?.license?.enabledFeatures) {
      const features = rootInfo.license.enabledFeatures.split(",");
      setEnabledFeatures(features);
    }
  }, [rootInfo, setEnabledFeatures]);
}

/**
 * Authentication route that checks if the user is authenticated
 * and redirects to login if not.
 */
export const Route = createFileRoute("/_auth")({
  beforeLoad: ({ context: { auth } }) => {
    // Check if token exists and is not expired
    const isTokenExpired = !auth?.token || isExpired(auth.token);

    if (isTokenExpired) {
      throw redirect({
        to: "/login",
      });
    }
  },
  component: AuthenticatedRouteComponent,
});

/**
 * Main component for authenticated routes that handles WebSocket connections
 * and provides the main layout.
 */
export function AuthenticatedRouteComponent() {
  // Get WebSocket URL from settings
  const { wsURL } = useSettingStore();

  // Data fetching hooks
  const { data: syslogAlert } = useGetSyslogAlert();
  const { data: rootInfo } = useGetRootInfo();

  // Initialize WebSocket connection
  useAppWebSocket(`${wsURL}/api/v1/ws`);

  // Process data from API
  useSyslogAlertProcessor(syslogAlert);
  useRootInfoProcessor(rootInfo);

  return (
    <MainLayout>
      <Outlet />
    </MainLayout>
  );
}
