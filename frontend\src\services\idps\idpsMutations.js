import { useMutation } from "@tanstack/react-query";
import { App } from "antd";
import { useSendCommand } from "../mutations";
import { handleCommandExecution } from "../../utils/command-execution";

/**
 * Custom hook for importing IDPS rules
 * @param {Function} onSuccess - Success callback
 * @returns {Object} Mutation object
 */
export const useImportRules = (onSuccess) => {
  const { notification } = App.useApp();
  const sendCommand = useSendCommand();

  return useMutation({
    mutationFn: async ({ url, serviceName }) => {
      const command = [{
        command: `idps rules import ${url}`,
        client: serviceName,
      }];
      return handleCommandExecution(command, sendCommand, notification);
    },
    onSuccess: (data) => {
      if (onSuccess) onSuccess(data);
    },
  });
};

/**
 * Custom hook for deleting IDPS rules
 * @param {Function} onSuccess - Success callback
 * @returns {Object} Mutation object
 */
export const useDeleteRule = (onSuccess) => {
  const { notification } = App.useApp();
  const sendCommand = useSendCommand();

  return useMutation({
    mutationFn: async ({ ruleName, serviceName }) => {
      const command = [{
        command: `idps rules delete ${ruleName}`,
        client: serviceName,
      }];
      return handleCommandExecution(command, sendCommand, notification);
    },
    onSuccess: (data) => {
      if (onSuccess) onSuccess(data);
    },
  });
};

/**
 * Custom hook for searching files in IDPS records
 * @param {Function} onSuccess - Success callback
 * @returns {Object} Mutation object
 */
export const useSearchFile = (onSuccess) => {
  const { notification } = App.useApp();
  const sendCommand = useSendCommand();

  return useMutation({
    mutationFn: async ({ filename, selectedRecordDate, serviceName }) => {
      const command = [{
        command: `idps records search -f ${filename} -st ${selectedRecordDate}-00:00 -et ${selectedRecordDate}-23:59`,
        client: serviceName,
      }];
      return handleCommandExecution(command, sendCommand, notification);
    },
    onSuccess: (data) => {
      if (onSuccess) onSuccess(data);
    },
  });
};

/**
 * Custom hook for deleting IDPS records
 * @param {Function} onSuccess - Success callback
 * @returns {Object} Mutation object
 */
export const useDeleteRecords = (onSuccess) => {
  const { notification } = App.useApp();
  const sendCommand = useSendCommand();

  return useMutation({
    mutationFn: async ({ deleteType, deleteSelectedDate, deleteFileName, serviceName }) => {
      let commandStr;
      if (deleteType === "all") {
        commandStr = "idps records delete";
      } else if (deleteType === "date" && deleteSelectedDate) {
        commandStr = `idps records delete -d ${deleteSelectedDate}`;
      } else if (deleteType === "file" && deleteFileName && deleteSelectedDate) {
        commandStr = `idps records delete -f ${deleteFileName} -d ${deleteSelectedDate}`;
      } else {
        throw new Error("Invalid delete parameters");
      }

      const command = [{
        command: commandStr,
        client: serviceName,
      }];
      return handleCommandExecution(command, sendCommand, notification);
    },
    onSuccess: (data) => {
      if (onSuccess) onSuccess(data);
    },
  });
};

/**
 * Custom hook for filtering IDPS events
 * @param {Function} onSuccess - Success callback
 * @returns {Object} Mutation object
 */
export const useFilterEvents = (onSuccess) => {
  const { notification } = App.useApp();
  const sendCommand = useSendCommand();

  return useMutation({
    mutationFn: async ({ eventFilterFileName, eventFilterStartDate, eventFilterEndDate, serviceName }) => {
      const command = [{
        command: `idps records search -f ${eventFilterFileName} -st ${eventFilterStartDate}-00:00 -et ${eventFilterEndDate}-23:59`,
        client: serviceName,
      }];
      return handleCommandExecution(command, sendCommand, notification);
    },
    onSuccess: (data) => {
      if (onSuccess) onSuccess(data);
    },
  });
};
