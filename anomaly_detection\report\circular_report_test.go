package report

import (
	"reflect"
	"testing"
)

func TestCircularMessages(t *testing.T) {
	// Create a new CircularMessages with size 3
	cb := NewCircularMessages(3)

	// Add messages to the buffer
	cb.Add(ReportMessage{Message: "Message 1"})
	cb.Add(ReportMessage{Message: "Message 2"})
	cb.Add(ReportMessage{Message: "Message 3"})

	// Test Get method
	msg, err := cb.Get(0)
	if err != nil {
		t.<PERSON>rf("Unexpected error: %v", err)
	}
	expectedMsg := ReportMessage{Message: "Message 1"}
	if !reflect.DeepEqual(msg, expectedMsg) {
		t.<PERSON><PERSON><PERSON>("Expected message %v, but got %v", expectedMsg, msg)
	}

	// Test GetAll method
	allMsgs, err := cb.GetAll()
	if err != nil {
		t.<PERSON><PERSON>rf("Unexpected error: %v", err)
	}
	expectedAllMsgs := []ReportMessage{
		{Message: "Message 1"},
		{Message: "Message 2"},
		{Message: "Message 3"},
	}
	if !reflect.DeepEqual(allMsgs, expectedAllMsgs) {
		t.<PERSON>("Expected all messages %v, but got %v", expectedAllMsgs, allMsgs)
	}

	// Test IsEmpty method
	if cb.IsEmpty() {
		t.Errorf("Expected buffer to not be empty")
	}

	// Add message 4 it shuold overwrite message 1
	pre, err := cb.Add(ReportMessage{Message: "Message 4"})
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	expectedMsg = ReportMessage{Message: "Message 1"}
	if !reflect.DeepEqual(pre, &expectedMsg) {
		t.Errorf("Expected message %v, but got %v", expectedMsg, msg)
	}
	messages, err := cb.GetAll()
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
	t.Log("Add message 4 and it should overwrite message 1")
	t.Log(messages)

	// Test Clear method
	cb.Clear()
	if cb.Len() != 0 {
		t.Errorf("Expected buffer length to be 0 after clearing, but got %d", cb.Len())
	}
}
