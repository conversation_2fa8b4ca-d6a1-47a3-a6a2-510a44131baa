package report

import (
	"fmt"
	"testing"
	"time"
)

// TestReport
func TestReport(t *testing.T) {
	store := NewMessageArray()
	rep := NewReport("test", "test", 0.5, store)
	circularStore := NewCircularMessages(25)
	circularReport := NewReport("test", "test", 0.5, circularStore)
	for i := 1; i <= 20; i++ {
		msg := fmt.Sprintf("message:%d", i)
		t.Log("Add message:", msg)
		if i%2 == 0 {
			circularReport.AddMessage(msg, "anomaly", "test")
			rep.AddMessage(msg, "anomaly", "test")
		} else {
			circularReport.AddMessage(msg, "normal", "test")
			rep.AddMessage(msg, "normal", "test")
		}
		// sleep 0.5 second
		time.Sleep(time.Millisecond * 500)
	}

	// test summary
	summary := rep.GetSummary()
	if summary.TotalCount != 20 {
		t.<PERSON><PERSON><PERSON>("GetSummary error: %v", summary)
	}
	if summary.TotalAnomaly != 10 {
		t.<PERSON><PERSON><PERSON>("GetSummary error: %v", summary)
	}
	summary = circularReport.GetSummary()
	if summary.TotalCount != 20 {
		t.Errorf("GetSummary error: %v", summary)
	}
	if summary.TotalAnomaly != 10 {
		t.Errorf("GetSummary error: %v", summary)
	}

	// page 1 should be fake message 20 - fake message 16
	page1, err := rep.GetMessages(5, 1)
	if err != nil {
		t.Errorf("GetMessages error: %v", err)
	}
	if len(page1) != 5 {
		t.Errorf("GetMessages error: %v", page1)
	}
	// dump page1
	t.Log("Page 1 ( each page 5 messages ):")
	for _, v := range page1 {
		t.Logf("%s - %s", v.Message, v.TimeStamp)
	}

	// check first message should be fake message 20
	if page1[0].Message != "message:20" {
		t.Errorf("The first message of page 1 expected to be message:20, but got %s", page1[0].Message)
	}

	// Circular report shouldn't full
	if circularReport.Messages.IsFull() {
		t.Errorf("Circular report shouldn't full")
	}

	// Add other 5 messages
	for i := 21; i <= 25; i++ {
		msg := fmt.Sprintf("message:%d", i)
		t.Log("Add message:", msg)
		circularReport.AddMessage(msg, "anomaly", "test")
	}

	// Circular report should full
	if !circularReport.Messages.IsFull() {
		t.Errorf("Circular report should full")
	}

	// Add more message should overwrite the oldest message
	for i := 26; i <= 30; i++ {
		msg := fmt.Sprintf("message:%d", i)
		circularReport.AddMessage(msg, "anomaly", "test")
	}
	// The first message should be message:26
	msgs, err := circularReport.GetMessages(30, 1)
	if err != nil {
		t.Errorf("GetMessages error: %v", err)
	}
	t.Log("dump messages:", len(msgs))
	//dump page1
	for idx, v := range msgs {
		t.Logf("idx:%d %s - %s", idx, v.Message, v.TimeStamp)
	}

}
