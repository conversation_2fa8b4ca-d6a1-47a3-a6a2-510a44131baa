import { axiosInstance } from "../api";

/**
 * IDPS Service - Handles all IDPS-related API calls
 */
export class IdpsService {
  /**
   * Fetch IDPS report data
   * @returns {Promise<Object>} IDPS report data
   */
  static async getReport() {
    const response = await axiosInstance.get("/api/v1/idps/report");
    return response.data;
  }

  /**
   * Execute IDPS command
   * @param {string} command - The command to execute
   * @param {string} client - The service/client name
   * @returns {Promise<Object>} Command execution response
   */
  static async executeCommand(command, client) {
    const commandData = [{
      command,
      client,
    }];
    const response = await axiosInstance.post("/api/v1/commands", commandData);
    return response.data;
  }

  /**
   * Import rules for a service
   * @param {string} url - URL to import rules from
   * @param {string} serviceName - Service name
   * @returns {Promise<Object>} Command execution response
   */
  static async importRules(url, serviceName) {
    const command = `idps rules import ${url}`;
    return this.executeCommand(command, serviceName);
  }

  /**
   * Delete a rule from a service
   * @param {string} ruleName - Name of the rule to delete
   * @param {string} serviceName - Service name
   * @returns {Promise<Object>} Command execution response
   */
  static async deleteRule(ruleName, serviceName) {
    const command = `idps rules delete ${ruleName}`;
    return this.executeCommand(command, serviceName);
  }

  /**
   * Search files in records
   * @param {string} filename - File name to search
   * @param {string} selectedRecordDate - Date to search
   * @param {string} serviceName - Service name
   * @returns {Promise<Object>} Command execution response
   */
  static async searchFile(filename, selectedRecordDate, serviceName) {
    const command = `idps records search -f ${filename} -st ${selectedRecordDate}-00:00 -et ${selectedRecordDate}-23:59`;
    return this.executeCommand(command, serviceName);
  }

  /**
   * Delete records with various options
   * @param {string} deleteType - Type of deletion (all, date, file)
   * @param {string} deleteSelectedDate - Date for deletion (optional)
   * @param {string} deleteFileName - File name for deletion (optional)
   * @param {string} serviceName - Service name
   * @returns {Promise<Object>} Command execution response
   */
  static async deleteRecords(deleteType, deleteSelectedDate, deleteFileName, serviceName) {
    let command;
    if (deleteType === "all") {
      command = "idps records delete";
    } else if (deleteType === "date" && deleteSelectedDate) {
      command = `idps records delete -d ${deleteSelectedDate}`;
    } else if (deleteType === "file" && deleteFileName && deleteSelectedDate) {
      command = `idps records delete -f ${deleteFileName} -d ${deleteSelectedDate}`;
    } else {
      throw new Error("Invalid delete parameters");
    }
    return this.executeCommand(command, serviceName);
  }

  /**
   * Filter events by filename and date range
   * @param {string} eventFilterFileName - File name to filter
   * @param {string} eventFilterStartDate - Start date
   * @param {string} eventFilterEndDate - End date
   * @param {string} serviceName - Service name
   * @returns {Promise<Object>} Command execution response
   */
  static async filterEvents(eventFilterFileName, eventFilterStartDate, eventFilterEndDate, serviceName) {
    const command = `idps records search -f ${eventFilterFileName} -st ${eventFilterStartDate}-00:00 -et ${eventFilterEndDate}-23:59`;
    return this.executeCommand(command, serviceName);
  }
}
