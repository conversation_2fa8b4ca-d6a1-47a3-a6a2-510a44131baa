import { Card, Flex, Typography } from "antd";
import Icon from "@ant-design/icons";
import { MdFlashOff, MdFlashOn } from "react-icons/md";
import PropTypes from "prop-types";
import { PORT_INFO_CONFIG } from "../config/port-info.config";

export const DeviceInformation = ({ data, portInfoData, token }) => {
  return (
    <Card
      variant="borderless"
      title="Device Information"
      styles={{ body: { height: 400 } }}
    >
      {PORT_INFO_CONFIG.INFO_ITEMS.map((item) => (
        <Flex vertical key={item.id}>
          <Typography.Title
            align="center"
            level={5}
            style={{ backgroundColor: token.colorBgLayout }}
          >
            {item.label}
          </Typography.Title>
          {item.id === "power" ? (
            <div>
              {portInfoData[data.mac]?.powerStatus.map((pitem) => (
                <Flex justify="center" align="center" key={pitem.powerId}>
                  <Typography.Title
                    align="center"
                    key={pitem.powerId}
                    level={5}
                    style={{ margin: 0 }}
                  >
                    {`Power ${pitem.powerId}  `}
                  </Typography.Title>
                  {pitem.status ? (
                    <Icon component={MdFlashOn} />
                  ) : (
                    <Icon component={MdFlashOff} />
                  )}
                </Flex>
              ))}
            </div>
          ) : (
            <Typography.Title style={{ marginTop: 0 }} align="center" level={5}>
              {`${data[item.id]}`}
            </Typography.Title>
          )}
        </Flex>
      ))}
    </Card>
  );
};

DeviceInformation.propTypes = {
  data: PropTypes.shape({
    mac: PropTypes.string.isRequired,
    modelname: PropTypes.string.isRequired,
    ipaddress: PropTypes.string.isRequired,
    kernel: PropTypes.string.isRequired,
  }).isRequired,
  portInfoData: PropTypes.shape({
    powerStatus: PropTypes.arrayOf(
      PropTypes.shape({
        powerId: PropTypes.string.isRequired,
        status: PropTypes.bool.isRequired,
      })
    ).isRequired,
  }).isRequired,
  token: PropTypes.object.isRequired,
};
