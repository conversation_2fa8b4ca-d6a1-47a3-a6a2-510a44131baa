package store

import (
	"context"
	"encoding/json"
	"fmt"
	"mnms/anomaly_detection/def"
	"mnms/anomaly_detection/store/algo"
	"mnms/anomaly_detection/store/anomstore"
	"sort"
	"sync"
)

// Item

type Item struct {
	Vector []float32      `json:"vector" mapstructure:"vector"` // A slice of floating-point numbers representing the vector.
	Meta   map[string]any `json:"meta" mapstructure:"meta"`     // Reason for the vector
	Text   string         `json:"text" mapstructure:"text"`     // Text for the vector
}

type Candidate struct {
	ID    int64
	Item  Item
	Score float32
}

// StandardStore is a simple in-memory store that implements the Store interface.
type StandardStore struct {
	Items  map[int64]Item `json:"items" mapstructure:"items"`
	NextID int64          `json:"nextID" mapstructure:"nextID"`
	mu     sync.Mutex     `json:"-" mapstructure:"-"` // Mutex to synchronize access to the store
}

var localmemStore = StandardStore{
	Items:  map[int64]Item{},
	NextID: 0,
	mu:     sync.Mutex{},
}

// NewStandardStore creates a new instance of StandardStore.
func NewStandardStore() *StandardStore {
	return &StandardStore{
		mu:     sync.Mutex{},
		Items:  map[int64]Item{},
		NextID: 0,
	}
}

func (s *StandardStore) Mutex() *sync.Mutex {
	return &s.mu
}

// GetDefaultStore returns the default store.
func GetDefaultStore() *StandardStore {
	return &localmemStore
}

// Len returns the number of items in the store.
func (s *StandardStore) Len() int {
	return len(s.Items)
}

// Impl. of def.Store
// GetContent
func (s *StandardStore) GetContent(id int64) (string, error) {

	if item, ok := s.Items[id]; ok {
		return item.Text, nil
	}
	return "", fmt.Errorf("item with id %d not found", id)
}

// GetMeta
func (s *StandardStore) GetMeta(id int64) (map[string]any, error) {

	if item, ok := s.Items[id]; ok {
		return item.Meta, nil
	}
	return nil, fmt.Errorf("item with id %d not found", id)
}

// Clear
func (s *StandardStore) Clear() error {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.Items = map[int64]Item{}
	s.NextID = 0
	return nil
}

// Add
func (s *StandardStore) Add(content string, meta map[string]any, vec []float32) (int64, error) {
	s.mu.Lock()
	defer s.mu.Unlock()
	id := s.NextID
	s.Items[id] = Item{
		Vector: vec,
		Meta:   meta,
		Text:   content,
	}
	s.NextID++
	return id, nil
}

// Update
func (s *StandardStore) Update(id int64, content string, meta map[string]any, vec []float32) error {

	if _, ok := s.Items[id]; !ok {
		return fmt.Errorf("item with id %d not found", id)
	}
	s.mu.Lock()
	s.Items[id] = Item{
		Vector: vec,
		Meta:   meta,
		Text:   content,
	}
	s.mu.Unlock()
	return nil
}

// copyMap creates a new copy of the given map
func copyMap(original map[string]any) map[string]any {
	if original == nil {
		return nil
	}
	newMap := make(map[string]any, len(original))
	for k, v := range original {
		newMap[k] = v
	}
	return newMap
}

// Delete
func (s *StandardStore) Delete(id int64) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	if _, ok := s.Items[id]; !ok {
		return fmt.Errorf("item with id %d not found", id)
	}
	delete(s.Items, id)
	return nil
}

// Upsert
func (s *StandardStore) Upsert(ctx context.Context, content string, metadata map[string]any, vec []float32, threshold float32) (int64, error) {
	metaCopy := copyMap(metadata)
	// search similar
	candidates, err := s.SearchSimilar(ctx, vec, algo.CosineSimilarityCalculator{}, 1)
	if err != nil {
		return -1, err
	}
	if len(candidates) > 0 && candidates[0].Score < threshold {
		// update
		err := s.Update(candidates[0].ID, content, metaCopy, vec)
		if err != nil {
			return -1, err
		}
		return candidates[0].ID, nil
	}
	// no similar, add
	return s.Add(content, metaCopy, vec)
}

// Search
func (s *StandardStore) Search(ctx context.Context, vec []float32, n int) ([]def.Document, error) {
	candidates, err := s.SearchSimilar(ctx, vec, algo.CosineSimilarityCalculator{}, n)
	if err != nil {
		return []def.Document{}, err
	}
	docs := make([]def.Document, 0, len(candidates))
	for _, candidate := range candidates {
		docs = append(docs, def.Document{
			Content:  candidate.Item.Text,
			Metadata: candidate.Item.Meta,
			Score:    candidate.Score,
		})
	}
	return docs, nil
}

// SearchSimilar
func (s *StandardStore) SearchSimilar(ctx context.Context, vec []float32, f algo.DistanceCalculator, count int) ([]*Candidate, error) {
	if count <= 0 {
		return nil, fmt.Errorf("count should be greater than 0")
	}
	if count > len(s.Items) {
		count = len(s.Items)
	}
	var candidates = make([]*Candidate, 0)

	// Create a channel to receive results from the goroutine
	resultChan := make(chan []*Item)
	errChan := make(chan error)

	// Launch the search in a separate goroutine
	go func() {
		defer close(resultChan)
		for idx, item := range s.Items {
			// Check if the context is canceled
			select {
			case <-ctx.Done():
				errChan <- ctx.Err()
				return // Exit the goroutine
			default:
				// Continue with the search
			}

			distance := f.Calculate(vec, item.Vector)
			candidate := &Candidate{
				ID:    int64(idx),
				Item:  item,
				Score: distance,
			}

			candidates = append(candidates, candidate)
		}
		errChan <- nil // Signal that the search completed successfully
	}()

	// Wait for either the search to complete or the context to be cancelled
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	case err := <-errChan:
		if err != nil {
			return nil, err
		}
	}

	// Sort the candidates by score and return the top 'count' candidates
	sort.Slice(candidates, func(i, j int) bool {
		return candidates[i].Score < candidates[j].Score
	})

	if len(candidates) > count {
		candidates = candidates[:count]
	}

	return candidates, nil
}

type wrapper struct {
	Type string         `json:"type"`
	Data *StandardStore `json:"data"`
}

// Export
func (s *StandardStore) Serialize() ([]byte, error) {

	// Create a wrapper that includes type information
	w := wrapper{
		Type: "StandardStore",
		Data: s,
	}

	return json.Marshal(w)

}

// DeserializeStandardStore deserializes the StandardStore from the given byte slice
func DeserializeStandardStore(data []byte) (*StandardStore, error) {
	// Unmarshal the wrapper
	w := wrapper{}
	err := json.Unmarshal(data, &w)
	if err != nil {
		return nil, err
	}

	return w.Data, nil
}

// DeserializeStore deserializes the Store from the given byte slice
func DeserializeStore(data []byte) (def.Store, error) {
	// Unmarshal the wrapper

	w := wrapper{}
	err := json.Unmarshal(data, &w)
	if err != nil {
		return nil, err
	}

	switch w.Type {
	case "StandardStore":
		return DeserializeStandardStore(data)
	case "VectorList":
		return anomstore.DeserializeVectorList(data)
	default:
		return nil, fmt.Errorf("unknown type %s", w.Type)
	}
}
