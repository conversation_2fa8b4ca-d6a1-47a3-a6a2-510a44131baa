import { memo, useMemo } from "react";
import { Card, Spin, Typography, theme } from "antd";
import ReactApex<PERSON>hart from "react-apexcharts";
import { useTheme } from "antd-style";

const { Text } = Typography;

/**
 * IDPS Packet Chart Component
 * Displays pie chart of packet counts by rules
 * @param {Object} props Component props
 * @param {Array} props.data Packet data for rules
 * @param {boolean} props.loading Loading state
 * @param {string} props.selectedService Selected service name
 * @returns {JSX.Element} Packet chart component
 */
const IdpsPacketChart = ({ data = [], loading = false, selectedService }) => {
  const { appearance } = useTheme();
  const { token } = theme.useToken();

  const chartOptions = useMemo(() => {
    if (!data || data.length === 0) {
      return null;
    }
    console.log(data);
    // Process data for chart
    const chartData = data
      .filter((item) => item.counts > 0)
      .map((item) => ({
        name: item.rule_name || item.name || "Unknown Rule",
        count: item.counts || 0,
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // Show top 10 rules

    if (chartData.length === 0) {
      return null;
    }

    const series = chartData.map((item) => item.count);
    const labels = chartData.map((item) => item.name);

    return {
      series,
      options: {
        chart: {
          type: "pie",
          height: 350,
          background: "transparent",
        },
        theme: {
          mode: appearance === "dark" ? "dark" : "light",
        },
        colors: [
          token.colorPrimary,
          token.colorSuccess,
          token.colorWarning,
          token.colorError,
          token.colorInfo,
          token.colorPrimaryActive,
          token.colorSuccessActive,
          token.colorWarningActive,
          token.colorErrorActive,
          token.colorInfoActive,
        ],
        labels,
        responsive: [
          {
            breakpoint: 480,
            options: {
              chart: {
                width: 200,
              },
              legend: {
                position: "bottom",
              },
            },
          },
        ],
        legend: {
          position: "bottom",
          horizontalAlign: "center",
        },
        tooltip: {
          y: {
            formatter: function (val) {
              return val + " packets";
            },
          },
        },
        plotOptions: {
          pie: {
            donut: {
              size: "45%",
              labels: {
                show: true,
                total: {
                  show: true,
                  label: "Total Packets",
                  formatter: function (w) {
                    return w.globals.seriesTotals.reduce((a, b) => a + b, 0);
                  },
                },
              },
            },
          },
        },
      },
    };
  }, [data, appearance, token]);

  return (
    <Card
      title={
        <div>
          <Text strong>Packet Count by Rules</Text>
          {selectedService && (
            <div style={{ fontSize: "12px", color: token.colorTextSecondary }}>
              Service: {selectedService}
            </div>
          )}
        </div>
      }
      variant="borderless"
    >
      <Spin spinning={loading}>
        {chartOptions ? (
          <div>
            <ReactApexChart
              options={chartOptions.options}
              series={chartOptions.series}
              type="donut"
              height={350}
            />
          </div>
        ) : (
          <div
            style={{
              height: 350,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              flexDirection: "column",
            }}
          >
            <Text type="secondary">No packet data available</Text>
            {selectedService && (
              <Text
                type="secondary"
                style={{ fontSize: "12px", marginTop: "8px" }}
              >
                for service: {selectedService}
              </Text>
            )}
          </div>
        )}
      </Spin>
    </Card>
  );
};

export default memo(IdpsPacketChart);
