package envconfig

import (
	"fmt"
	"os"
	"strings"

	"github.com/qeof/q"
)

// Var returns an environment variable stripped of leading and trailing quotes or spaces
func Var(key string) (string, error) {
	s := strings.Trim(strings.TrimSpace(os.Getenv(key)), "\"'")
	if s == "" {
		return "", fmt.<PERSON>rrorf("environment variable %s is not set", key)
	}
	return s, nil
}

// LLMAPIKey returns the API key for the LLM. API key can be configured via the NIMBL_LLM_API_KEY environment variable.
func LLMAPIKey() (string, error) {
	s, err := Var("NIMBL_LLM_API_KEY")
	if err != nil {

		// try to compatible with old environment variable OPENAI_API_KEY
		s, err = Var("OPENAI_API_KEY")
		if err == nil {
			return s, nil
		}
		q.Q("NIMBL_LLM_API_KEY is not set", err)
		return "", err
	}
	return s, nil
}

// LLMToken maybe someone want to use Ollama with token?
func LLMToken() (string, error) {
	s, err := Var("NIMBL_LLM_TOKEN")
	if err != nil {
		q.Q("NIMBL_LLM_TOKEN is not set", err)
		return "", err
	}
	return s, nil
}

// LLMHost returns the host for the LLM. Host can be configured via the NIMBL_LLM_HOST environment variable.
func LLMHost() (string, error) {
	s, err := Var("NIMBL_LLM_HOST")
	if err != nil {
		q.Q("NIMBL_LLM_HOST is not set", err)
		return "", err
	}
	return s, nil
}

// LLMModel returns the model for the LLM. Model can be configured via the NIMBL_LLM_MODEL environment variable.
func LLMModel() (string, error) {
	s, err := Var("NIMBL_LLM_MODEL")
	if err != nil {
		q.Q("NIMBL_LLM_MODEL is not set", err)
		return "", err
	}
	return s, nil
}
