import Icon, {
  ApartmentOutlined,
  GlobalOutlined,
  LineChartOutlined,
  PoweroffOutlined,
  SaveOutlined,
  SoundOutlined,
} from "@ant-design/icons";

import {
  MdAcUnit,
  MdAddTask,
  MdEvent,
  MdCloud,
  MdDelete,
} from "react-icons/md";

export const MENU_IDS = {
  DEVICE: "device-menu",
  MASS: "mass-menu",
  MDR: "mdr-menu",
  PORT_INFO: "port-info-menu",
};

export const getDeviceMenuItems = (userRole) =>
  [
    userRole !== "user" && {
      label: "Open in web",
      key: "openweb",
      icon: <GlobalOutlined />,
    },
    userRole !== "user" && {
      label: "Open in web via tunnel",
      key: "openwebtunnel",
      icon: <GlobalOutlined />,
    },
    {
      label: "Beep",
      key: "beep",
      icon: <SoundOutlined />,
    },
    {
      label: "Port Information",
      key: "portInfo",
      icon: <LineChartOutlined />,
    },
    {
      label: "Reboot",
      key: "reboot",
      icon: <PoweroffOutlined />,
    },
    {
      label: "Network Setting",
      key: "networkSetting",
      icon: <ApartmentOutlined />,
    },
    {
      label: "Syslog Setting",
      key: "syslogSetting",
      icon: <Icon component={MdEvent} />,
    },
    {
      label: "Trap Setting",
      key: "trapSetting",
      icon: <Icon component={MdAcUnit} />,
    },
    {
      label: "Enable SNMP",
      key: "enablesnmp",
      icon: <Icon component={MdAddTask} />,
    },
    {
      label: "Upload Firmware",
      key: "uploadFirmware",
      icon: <Icon component={MdCloud} />,
    },
    {
      label: "Save Running Config",
      key: "saveConfig",
      icon: <SaveOutlined />,
    },
  ].filter(Boolean);

export const massMenuItems = [
  {
    label: "Beep",
    key: "massBeep",
    icon: <SoundOutlined />,
  },
  {
    label: "Reboot",
    key: "massReboot",
    icon: <PoweroffOutlined />,
  },
  {
    label: "Syslog Setting",
    key: "massSyslogSetting",
    icon: <Icon component={MdEvent} />,
  },
  {
    label: "Trap Setting",
    key: "massTrapSetting",
    icon: <Icon component={MdAcUnit} />,
  },
  {
    label: "Enable SNMP",
    key: "massEnablesnmp",
    icon: <Icon component={MdAddTask} />,
  },
  {
    label: "Upload Firmware",
    key: "massUploadFirmware",
    icon: <Icon component={MdCloud} />,
  },
  {
    label: "Delete Device",
    key: "deleteDevice",
    icon: <Icon component={MdDelete} />,
  },
];

export const mdrMenuItems = [
  {
    label: "Network Setting",
    key: "setNetwork",
  },
  {
    label: "Set Led",
    key: "setLed",
  },
  {
    label: "Set MDR",
    key: "setMdr",
  },
  {
    label: "Set Profinet",
    key: "setProfinet",
  },
];

export const portInfoMenuItem = [
  {
    label: "Disable Port",
    key: "disable",
  },
  {
    label: "Enable Port",
    key: "enable",
  },
];
