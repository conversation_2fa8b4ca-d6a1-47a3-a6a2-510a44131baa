package llm

import (
	"context"
	"os"
	"testing"
)

// TestFormatOutput tests the FormatOutput function
func TestFormatOutput(t *testing.T) {
	llmsettings := GPTSettings()
	llmsettings.APIKey = os.Getenv("OPENAI_API_KEY")
	t.Log(os.Getenv("OPENAI_API_KEY"))
	llmsettings.Model = "gpt-4o"
	client := NewOpenAI(llmsettings.APIKey, llmsettings.Model)
	type Member struct {
		// Name of the struct
		Name   string   `json:"name" `
		Age    int      `json:"age"`
		Skills []string `json:"skills" description:"Skills of the member"`
	}
	var m Member
	ret, err := client.FormattedOutput(context.Background(),
		[]Message{
			{
				Role:    "system",
				Content: "You are an expert at structured data extraction. You will be given unstructured text from a research paper and should convert it into the given structure.",
			},
			{
				Role:    "user",
				Content: "<PERSON> is a 35 years old man, he is good for coding, singing and dancing.",
			},
		}, "member", &m)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(ret)

}

// TestFormatOutput tests the FormatOutput function
func TestFormatOutput2(t *testing.T) {
	llmsettings := OllamaSettings("llama3.2:3b")
	client, err := NewLLMClient(llmsettings)
	if err != nil {
		t.Fatal(err)
	}
	type Member struct {
		// Name of the struct
		Name   string   `json:"name" `
		Age    int      `json:"age"`
		Skills []string `json:"skills" description:"Skills of the member"`
	}
	var m Member
	ret, err := client.FormattedOutput(context.Background(),
		[]Message{
			{
				Role:    "system",
				Content: "You are an expert at structured data extraction. You will be given unstructured text from a research paper and should convert it into the given structure.",
			},
			{
				Role:    "user",
				Content: "Alan is a 35 years old man, he is good for coding, singing and dancing.",
			},
		}, "member", &m)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(ret)
}
