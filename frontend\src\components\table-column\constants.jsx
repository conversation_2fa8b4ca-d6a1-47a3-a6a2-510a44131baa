import { Col, Flex, Row, Tag } from "antd";

export const DEVICE_INFO_COLUMNS = [
  {
    title: "Model",
    key: "modelname",
    dataIndex: "modelname",
    span: 2,
  },
  {
    title: "Ip Address",
    key: "ipaddress",
    dataIndex: "ipaddress",
  },
  {
    title: "Netmask",
    key: "netmask",
    dataIndex: "netmask",
  },
  {
    title: "Gateway",
    key: "gateway",
    dataIndex: "gateway",
  },
  {
    title: "DHCP",
    key: "isdhcp",
    dataIndex: "isdhcp",
    render: (data) => {
      return data ? "YES" : "NO";
    },
  },
  {
    title: "F/W Version",
    key: "ap",
    dataIndex: "ap",
    span: 2,
  },
  {
    title: "Kernel",
    key: "kernel",
    dataIndex: "kernel",
  },
  {
    title: "Agent Version",
    key: "agentVersion",
    dataIndex: "agentVersion",
  },
  {
    title: "Agent",
    key: "capabilities.agent",
    dataIndex: "capabilities",
    render: (data) => {
      return data?.agent ? "YES" : "NO";
    },
  },
  {
    title: "GWD",
    key: "capabilities.gwd",
    dataIndex: "capabilities",
    render: (data) => {
      return data?.gwd ? "YES" : "NO";
    },
  },
  {
    title: "SNMP support",
    key: "snmpSupported",
    dataIndex: "snmpSupported",
    render: (data) => {
      return data === "1" ? "YES" : "NO";
    },
  },
  {
    title: "SNMP Enabled",
    key: "snmpEnabled",
    dataIndex: "snmpEnabled",
    render: (data) => {
      return data === "1" ? "YES" : "NO";
    },
  },
  {
    title: "Read Community",
    key: "readcommunity",
    dataIndex: "readcommunity",
  },
  {
    title: "Write Community",
    key: "writecommunity",
    dataIndex: "writecommunity",
  },
  {
    title: "Scan proto",
    key: "scanproto",
    dataIndex: "scanproto",
  },
  {
    title: "Topology proto",
    key: "topologyproto",
    dataIndex: "topologyproto",
  },
  {
    title: "Service Name",
    key: "scannedby",
    dataIndex: "scannedby",
  },
  {
    title: "Tunnel URL",
    key: "tunneled_url",
    dataIndex: "tunneled_url",
    ellipsis: true,
  },
  {
    title: "Timestamp",
    key: "timestamp",
    dataIndex: "timestamp",
    span: 2,
    render: (data) => new Date(data * 1000).toLocaleString(),
  },
  {
    title: undefined,
    key: "supported",
    dataIndex: "supported",
    span: 2,
    render: (data) => (
      <Flex vertical gap={10}>
        <span className="ant-descriptions-item-label">Supported</span>
        <Row gutter={[8, 8]}>
          {(data || []).map((item) => (
            <Col span={8} key={item}>
              <Tag color="gold">{item}</Tag>
            </Col>
          ))}
        </Row>
      </Flex>
    ),
  },
  // ...other columns configuration
];

export const TABLE_WIDTHS = {
  IP_ADDRESS: 120,
  MODEL: 200,
  SERVICE_NAME: 150,
  MAC_ADDRESS: 180,
  HOST_NAME: 150,
  NETMASK: 150,
  KERNEL: 100,
  FIRMWARE: 200,
  AGENT: 120,
  TIMESTAMP: 200,
};
