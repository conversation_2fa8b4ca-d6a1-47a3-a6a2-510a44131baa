import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";

/**
 * @typedef {Object} AppState
 * @property {string[]} selectedRowKeys - Selected row keys in tables
 * @property {string} selectedServices - Selected network services filter
 * @property {string[]} dialogs - Array of open dialogs
 * @property {boolean} isChatOpen - Whether chat panel is open
 * @property {number} chatWidth - Width of chat panel in pixels
 */

/**
 * @typedef {Object} AppActions
 * @property {(keys: string[]) => void} setSelectedRowKeys - Update selected row keys
 * @property {(service: string) => void} setSelectedServices - Update selected services
 * @property {(dialog: { id: string; data: any }) => void} openDialogs - Open a dialog
 * @property {(id: string) => void} closeDialogs - Close a dialog
 * @property {() => void} toggleChat - Toggle chat panel visibility
 * @property {(width: number) => void} setChatWidth - Set chat panel width
 */

/** @type {AppState} */
const initialState = {
  selectedRowKeys: [],
  selectedServices: "All Network Services",
  dialogs: [],
  isChatOpen: false,
  chatWidth: 450,
};

/**
 * Create store with initial state and actions
 * @param {Function} set - Zustand set function
 * @returns {AppState & AppActions} Store with state and actions
 */
const createSharedStore = (set) => ({
  ...initialState,

  setSelectedRowKeys: (keys = []) =>
    set((state) => {
      state.selectedRowKeys = keys;
    }),

  setSelectedServices: (service) =>
    set((state) => {
      state.selectedServices = service;
    }),
  openDialogs: (dialog) =>
    set((state) => {
      state.dialogs.push(dialog);
    }),
  closeDialogs: (id) =>
    set((state) => {
      state.dialogs = state.dialogs.filter((d) => d.id !== id);
    }),
  toggleChat: () =>
    set((state) => {
      state.isChatOpen = !state.isChatOpen;
    }),
  setChatWidth: (width) =>
    set((state) => {
      state.chatWidth = Math.max(300, Math.min(800, width)); // Min 300px, Max 800px
    }),
});

/**
 * Main application store for managing global state
 * @type {import("zustand").UseBoundStore<AppState & AppActions>}
 */
export const useAppStore = create(
  devtools(
    immer((...args) => ({
      ...createSharedStore(...args),
    })),
    {
      name: "AppStore",
      enabled: process.env.NODE_ENV === "development",
    }
  )
);
