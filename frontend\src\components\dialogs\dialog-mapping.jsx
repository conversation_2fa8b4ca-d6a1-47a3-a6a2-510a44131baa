import { DIALOG_TYPES } from "../../constants/dialog-types";
import CommandResultsDialog from "./dialog/command-results";
import DeviceEdit from "./dialog/device-edit";
import FirmwareUploadDialog from "./dialog/firmware-upload";
import NetworkSettingDialog from "./dialog/network-setting";
import PortInfoDialog from "./dialog/port-info";
import GenerateQRCode from "./dialog/qrcode-2fa";
import ScanCidr from "./dialog/scan-cidr";
import SyslogSettingDialog from "./dialog/syslog-setting";
import TrapSettingDialog from "./dialog/trap-setting";
import UserForm from "./dialog/user-form";
import Validate2FA from "./dialog/validate-2fa";
import AddTopoloy from "./dialog/add-topology";
import SaveRestoreTopology from "./dialog/save-restore-topology";

export const DIALOG_COMPONENTS = {
  [DIALOG_TYPES.NETWORK_SETTING]: NetworkSettingDialog,
  [DIALOG_TYPES.SYSLOG_SETTING]: SyslogSettingDialog,
  [DIALOG_TYPES.TRAP_SETTING]: TrapSettingDialog,
  [DIALOG_TYPES.UPLOAD_FIRMWARE]: FirmwareUploadDialog,
  [DIALOG_TYPES.PORT_INFO]: PortInfoDialog,
  [DIALOG_TYPES.MASS_SYSLOG_SETTING]: SyslogSettingDialog,
  [DIALOG_TYPES.MASS_TRAP_SETTING]: TrapSettingDialog,
  [DIALOG_TYPES.MASS_UPLOAD_FIRMWARE]: FirmwareUploadDialog,
  [DIALOG_TYPES.COMMAND_RESULTS]: CommandResultsDialog,
  [DIALOG_TYPES.SCAN_CIDR]: ScanCidr,
  [DIALOG_TYPES.DEVICE_EDIT]: DeviceEdit,
  [DIALOG_TYPES.USER_MANAGEMENT]: UserForm,
  [DIALOG_TYPES.GENERATE_2FA]: GenerateQRCode,
  [DIALOG_TYPES.VALIDATE_2FA]: Validate2FA,
  [DIALOG_TYPES.ADD_TOPOLOGY]: AddTopoloy,
  [DIALOG_TYPES.SAVE_RESTORE_TOPOLOGY]: SaveRestoreTopology,
};
