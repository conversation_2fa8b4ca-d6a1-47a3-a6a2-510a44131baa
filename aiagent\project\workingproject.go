package project

import (
	"errors"
	"fmt"
)

// Set Project as the working project to start accepting queries and returning results

var workingProject *Project

// GetWorkingProject returns the working project
func GetWorkingProject() (*Project, error) {
	if workingProject == nil {
		return nil, fmt.Errorf("No working project")
	}
	return workingProject, nil
}

// SetWorkingProject sets the working project
func SetWorkingProject(p *Project) error {
	if p == nil {
		return errors.New("Project is nil")
	}

	workingProject = p
	return nil
}
