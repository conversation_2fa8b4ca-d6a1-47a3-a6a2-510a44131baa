import { useCallback, useRef } from "react";
import { App } from "antd";
import { useGetTopologyData } from "../../services/queries";
import domtoimage from "dom-to-image";
import saveAs from "file-saver";
import { getFilename } from "../../utils/topologyUtils";
import { useTopologyStore } from "../../store/topology-store";

export const useTopology = () => {
  const { selectedServices, setSelectedServices } = useTopologyStore();
  const { data, isFetching, error, refetch } =
    useGetTopologyData(selectedServices);
  const graphRef = useRef(null);
  const containerRef = useRef(null);
  const { message } = App.useApp();

  const handleGraphRender = useCallback((g, c) => {
    if (!g || !c) return;
    graphRef.current = g;
    containerRef.current = c;
  }, []);

  const handleServiceChange = useCallback(
    (value) => {
      setSelectedServices(value);
    },
    [setSelectedServices]
  );

  const handleRefresh = useCallback(() => {
    refetch();
  }, [refetch]);

  const exportTopology = useCallback(async () => {
    if (!containerRef.current) {
      message.error({
        key: "export-error",
        content: "No topology graph available to export",
      });
      return;
    }

    const messageKey = "exporting-topology";
    try {
      message.loading({
        content: "Preparing topology for export...",
      });

      const exportOptions = {
        filter: (node) => node.tagName !== "i",
        quality: 1,
        style: {
          transform: "scale(1)",
          "transform-origin": "top left",
        },
      };

      const dataUrl = await domtoimage.toSvg(
        containerRef.current,
        exportOptions
      );
      const filename = getFilename("topology");

      saveAs(dataUrl, `${filename}.svg`);

      message.success({
        key: messageKey,
        content: "Topology exported successfully",
        duration: 3,
      });
    } catch (err) {
      console.error("[Topology Export]:", err);
      message.error({
        content: `Failed to export topology: ${err instanceof Error ? err.message : "Unknown error"}`,
        duration: 4,
      });
    }
  }, [message]);

  return {
    data,
    isFetching,
    error,
    selectedServices,
    handleGraphRender,
    handleServiceChange,
    handleRefresh,
    exportTopology,
  };
};
