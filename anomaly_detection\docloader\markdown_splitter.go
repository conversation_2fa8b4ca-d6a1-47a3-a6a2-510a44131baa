package docloader

import (
	"io"
	"os"

	"mnms/anomaly_detection/def"

	"github.com/tmc/langchaingo/schema"
	"github.com/tmc/langchaingo/textsplitter"
)

// LoadMarkdown loads a markdown file and splits it into Document objects.
func LoadMarkdown(path string) ([]*def.Document, error) {
	f, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	splitter := textsplitter.NewMarkdownTextSplitter(
		textsplitter.WithChunkSize(1000),
		textsplitter.WithChunkOverlap(500),
	)
	content, err := io.ReadAll(f)
	if err != nil {
		return nil, err
	}
	docs, err := textsplitter.CreateDocuments(splitter, []string{string(content)}, []map[string]any{})
	if err != nil {
		return nil, err
	}

	return docToDocument(docs), nil

}

// docToDocument converts a textsplitter.Document to a def.Document.
func docToDocument(docs []schema.Document) []*def.Document {
	var documents []*def.Document
	for _, doc := range docs {

		d := &def.Document{
			Content:  doc.PageContent,
			Metadata: doc.Metadata,
			Score:    doc.Score,
		}
		documents = append(documents, d)
	}
	return documents
}
