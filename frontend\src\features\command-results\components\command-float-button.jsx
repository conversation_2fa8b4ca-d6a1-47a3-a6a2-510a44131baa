import { MacCommandOutlined } from "@ant-design/icons";
import { FloatButton } from "antd";
import React from "react";
import { useAppStore } from "../../../store/store";

const CommandFloatButton = () => {
  const { openDialogs } = useAppStore();
  return (
    <FloatButton
      shape="circle"
      type="primary"
      tooltip="commands"
      style={{
        insetInlineEnd: 10,
        insetBlockEnd: 70,
      }}
      icon={<MacCommandOutlined />}
      onClick={() => openDialogs({ id: "commandResults", data: null })}
    />
  );
};

export default CommandFloatButton;
