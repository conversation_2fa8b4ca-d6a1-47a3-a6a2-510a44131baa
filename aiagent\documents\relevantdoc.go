package documents

import (
	"bytes"
	"context"
	"fmt"
	"mnms/aiagent/documents/def"
	"mnms/aiagent/documents/vectorstore"
	"mnms/llm"
	"os"
	"path/filepath"
	"slices"

	"encoding/json"

	"github.com/qeof/q"
	"go.uber.org/zap"
)

type RelevantDocumentsFinder interface {
	FindWithSimilarity(ctx context.Context, query, docCatgory string, nResult int, similarity float32) ([]string, error)
	Query(query, cate string) ([]string, error)
}

// RelevantDoc a basic struct for relevant document
type RelevantDoc struct {
	Category string `json:"category"` // the category of the document
	Content  string `json:"content"`  // the map key of the document
	Doc      string `json:"doc"`      // the document
}

// RelevantDocWithReference a struct for relevant document with reference
type RelevantDocWithReference struct {
	RelevantDoc
	References []string `json:"references"`
}

type RelevantDocumentAdder interface {
	AddDoc(ctx context.Context, doc *RelevantDoc) error
	AddDocWithReference(ctx context.Context, option *RelevantDocWithReference) error
}

// RelevantDocumentIndexer lavarage the LLM to generate references (indexes) for input doc.
type RelevantDocumentIndexer interface {
	// IndexingDocument indexes a document returns string list which similiar to the document
	IndexingDocument(ctx context.Context, doc string, systemPrompt string, count int) ([]string, error)
}

type RelevantDocumentCategory struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Count       int    `json:"count"`
}

type RelevantCategoriesLister interface {
	ListCategories() []RelevantDocumentCategory
}

type RelevantDocumentReferenceOperator interface {
	GetReference(ctx context.Context, collection, id string) (*ChromemDocumentWithoutEmbedding, error)
	FindReference(ctx context.Context, category, docID string) ([]ChromemDocumentWithoutEmbedding, error)
}

type RelevantDocumentLister interface {
	ListRelevantDocuments(category string, page, pagesize int) ([]def.DocumentItem, error)
	TotalCount(category string) int
}

type RelevantDocumentModifier interface {
	UpdateDocument(category string, content string, newDoc string) error
	DeleteDocument(category string, content string) error
}

// RelevantDocumentProvider is an interface for relevant document provider
type RelevantDocumentProvider interface {
	RelevantDocumentsFinder
	RelevantDocumentAdder
	RelevantCategoriesLister
	RelevantDocumentIndexer
	RelevantDocumentReferenceOperator
	RelevantDocumentLister
	RelevantDocumentModifier
	Serialize() ([]byte, error)
	GetRelevantDocumentStatistics() *RelevantDocumentStatistics
}

type LocalRelevantDocuments struct {
	extraDocs DocumentProviders
	chroma    *vectorstore.ChromemeStore
}

// Serialize
func (lrd *LocalRelevantDocuments) Serialize() ([]byte, error) {
	docData, err := lrd.extraDocs.Serialize()
	if err != nil {
		return nil, fmt.Errorf("Serialize extraDocs fail: %s", err.Error())
	}
	buffer := bytes.Buffer{}
	err = lrd.chroma.Serialize(&buffer)
	if err != nil {
		return nil, fmt.Errorf("Serialize chroma fail: %s", err.Error())
	}
	return json.Marshal(map[string]json.RawMessage{
		"extra_docs": docData,
		"chroma":     buffer.Bytes(),
	})
}

// DeserializeLocalRelevantDocuemts
func DeserializeLocalRelevantDocuemts(data []byte) (*LocalRelevantDocuments, error) {
	var dataMap map[string]json.RawMessage
	err := json.Unmarshal(data, &dataMap)
	if err != nil {
		return nil, fmt.Errorf("Unmarshal data fail: %s", err.Error())
	}
	var extraDocsData json.RawMessage
	var chromaData json.RawMessage
	if err := json.Unmarshal(dataMap["extra_docs"], &extraDocsData); err != nil {
		return nil, fmt.Errorf("Unmarshal extra_docs fail: %s", err.Error())
	}
	if err := json.Unmarshal(dataMap["chroma"], &chromaData); err != nil {
		return nil, fmt.Errorf("Unmarshal chroma fail: %s", err.Error())
	}
	lrd := &LocalRelevantDocuments{}
	docs, err := DeserializeDocumantProviders(extraDocsData)
	if err != nil {
		return nil, fmt.Errorf("Deserialize extraDocs fail: %s", err.Error())
	}
	lrd.extraDocs = docs
	buffer := bytes.NewReader(chromaData)
	lrd.chroma, err = vectorstore.DeserializeLocalChromemeStore(buffer, "")
	if err != nil {
		return nil, fmt.Errorf("Deserialize chroma fail: %s", err.Error())
	}
	return lrd, nil
}

var relevantDocumentFolder = "rag"

// LoadLocalRelevantDocument
func LoadLocalRelevantDocument(filename string) (RelevantDocumentProvider, error) {
	// add .json if not exist
	if filepath.Ext(filename) != ".json" {
		filename += ".json"
	}
	// read project from file
	filepath := filepath.Join(relevantDocumentFolder, filename)
	data, err := os.ReadFile(filepath)
	if err != nil {
		return nil, fmt.Errorf("failed to read RAG %s : %w", filename, err)
	}
	return DeserializeLocalRelevantDocuemts(data)
}

// SaveLocalRelevantDocument save the relevant document to /rag folder
func SaveLocalRelevantDocument(filename string, lrd RelevantDocumentProvider) error {
	// add .json if not exist
	if filepath.Ext(filename) != ".json" {
		filename += ".json"
	}
	// make sure the rag folder exists
	err := os.MkdirAll(relevantDocumentFolder, 0755)
	if err != nil {
		return fmt.Errorf("failed to create rag folder: %w", err)
	}

	// write to file
	filepath := filepath.Join(relevantDocumentFolder, filename)
	data, err := lrd.Serialize()
	if err != nil {
		return fmt.Errorf("failed to serialize RAG %s : %w", filename, err)
	}
	err = os.WriteFile(filepath, data, 0644)
	if err != nil {
		return fmt.Errorf("failed to write RAG %s : %w", filename, err)
	}
	return nil
}

// UpdateDocument update the document
func (lrd *LocalRelevantDocuments) UpdateDocument(category string, content string, newDoc string) error {
	for _, d := range lrd.extraDocs {
		if d.GetName() == category {
			return d.Update(content, newDoc)
		}
	}
	return fmt.Errorf("Didn't find the document category %s", category)
}

// DeleteDocument delete the document
func (lrd *LocalRelevantDocuments) DeleteDocument(category string, content string) error {
	for _, d := range lrd.extraDocs {
		if d.GetName() == category {
			// find all reference
			refs, err := lrd.FindReference(context.Background(), category, content)
			if err != nil {
				zap.L().Error("FindReference", zap.Error(err))
			}
			zap.L().Info("FindReference ", zap.Int("count", len(refs)))
			if len(refs) > 0 {
				ids := []string{}
				for _, ref := range refs {
					ids = append(ids, ref.ID)
				}
				col, err := lrd.chroma.GetReference(category)
				if err != nil {
					zap.L().Error("GetReference", zap.Error(err))
				}
				err = col.DeleteReferences(context.Background(), ids)
				if err != nil {
					zap.L().Error("DeleteReferences", zap.Error(err))
				}
				zap.L().Info("DeleteReferences ", zap.Int("count", len(ids)))
			}
			return d.Delete(content)
		}
	}
	return fmt.Errorf("Didn't find the document category %s", category)
}

// ListSavedLocalRelevantDocuments list all the saved relevant documents
func ListSavedLocalRelevantDocuments() ([]string, error) {
	files, err := os.ReadDir(relevantDocumentFolder)
	if err != nil {
		return nil, fmt.Errorf("failed to read RAG folder: %w", err)
	}
	var ret []string
	for _, file := range files {
		ret = append(ret, file.Name())
	}
	return ret, nil
}

// NewLocalRelevantDocuments returns a new LocolMemoryDocumentProvider
func NewLocalRelevantDocuments(llmsettings *llm.LargeLanguageModel) *LocalRelevantDocuments {
	return &LocalRelevantDocuments{
		extraDocs: *GetLocalDocumentProviders(),
		chroma:    vectorstore.NewChromeme(llmsettings),
	}
}

// AddDoc add a document to the relevant document
func (lrd *LocalRelevantDocuments) AddDoc(ctx context.Context, doc *RelevantDoc) error {
	jsonBytes, err := json.Marshal(doc.Doc)
	var docText = doc.Doc
	if err == nil {
		docText = string(jsonBytes)
	}

	// check category exist?
	for _, d := range lrd.extraDocs {
		if d.GetName() == doc.Category {
			return d.Update(doc.Content, docText)

		}
	}
	// category not exist, add new category
	newDocProvider := NewLocalMemoryDocumentProvider(doc.Category, "")
	lrd.extraDocs = append(lrd.extraDocs, newDocProvider)
	return newDocProvider.Update(doc.Content, docText)
}

// AddDocWithReference add a document with reference to the relevant document
func (lrd *LocalRelevantDocuments) AddDocWithReference(ctx context.Context, option *RelevantDocWithReference) error {
	err := lrd.AddDoc(ctx, &option.RelevantDoc)
	if err != nil {
		return fmt.Errorf("Add document fail: %s", err.Error())
	}

	for _, ref := range option.References {
		doc := vectorstore.DocumentRef{
			ItemID: option.Content,
			DocKey: option.Category,
			Query:  ref,
		}
		err := lrd.chroma.AddDocumentRef(ctx, &doc)
		if err != nil {
			return fmt.Errorf("Add document ref fail: %s", err.Error())
		}
	}
	return nil
}

// ListCategories list all the categories
func (lrd *LocalRelevantDocuments) ListCategories() []RelevantDocumentCategory {
	var ret []RelevantDocumentCategory
	for _, d := range lrd.extraDocs {
		ret = append(ret, RelevantDocumentCategory{
			Name:        d.GetName(),
			Description: d.GetDescription(),
			Count:       d.TotalCount(),
		})
	}
	return ret
}

// Query query the document
func (lrd *LocalRelevantDocuments) Query(query, category string) ([]string, error) {
	var ret []string
	for _, d := range lrd.extraDocs {
		if d.GetName() == category || category == "" {
			return d.Query(query)
		}
	}
	return ret, fmt.Errorf("Didn't find the document category %s", category)
}

// FindwithSimilarity find the document with similarity
func (lrd *LocalRelevantDocuments) FindWithSimilarity(ctx context.Context, query, docCatgory string, nResults int, similarity float32) ([]string, error) {
	logger, _ := zap.NewProduction()
	defer logger.Sync()
	logger.Debug("FindWithSimilarity", zap.String("query", query), zap.String("docCatgory", docCatgory), zap.Int("nResults", nResults), zap.Float32("similarity", similarity))
	if similarity < -1 || similarity > 1 {
		return nil, fmt.Errorf("Similarity must be between -1 and 1, 1 is the most similar")
	}

	resolver, err := lrd.chroma.GetReference(docCatgory)
	if err != nil {
		// no index, query to extraDocument instead
		provider, err := lrd.extraDocs.GetDocumentProvider(docCatgory)
		if err != nil {
			return nil, fmt.Errorf("Didn't find the document category %s", docCatgory)
		}
		return provider.Query(query)
	}
	results, err := resolver.QueryWithNResult(ctx, query, nResults)
	if err != nil {
		return nil, fmt.Errorf("Resolve key fail: %s", err.Error())
	}

	var docs []string
	var foundItemIDs []string
	for _, ret := range results {
		if ret.Similarity < similarity {
			continue
		}
		id, ok := ret.Metadata["item_id"]
		if ok {
			// if id already in the foundItemIDs, skip

			if slices.Contains(foundItemIDs, id) {
				continue
			}

			foundItemIDs = append(foundItemIDs, id)
		}

		s, err := GetDocumentItemFromChememResult(ctx, lrd.extraDocs, ret)
		if err != nil {
			q.Q(err)
			continue
		}
		docs = append(docs, s)
	}
	return docs, nil

}

type ChromemDocumentWithoutEmbedding struct {
	ID       string            `json:"id"`
	Metadata map[string]string `json:"metadata"`
	Content  string            `json:"content"`
}

// GetReference get the reference with id
func (lrd *LocalRelevantDocuments) GetReference(ctx context.Context, collection, id string) (*ChromemDocumentWithoutEmbedding, error) {
	col, err := lrd.chroma.GetCollection(collection)
	if err != nil {
		return nil, err
	}
	doc, err := col.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	return &ChromemDocumentWithoutEmbedding{
		ID:       doc.ID,
		Metadata: doc.Metadata,
		Content:  doc.Content,
	}, nil

}

// FindReference find the reference with category and docID
func (lrd *LocalRelevantDocuments) FindReference(ctx context.Context, category, docID string) ([]ChromemDocumentWithoutEmbedding, error) {
	resolver, err := lrd.chroma.GetReference(category)
	if err != nil {
		return nil, err
	}

	results := resolver.GetDocumentWithFilter(map[string]string{
		"item_id": docID,
		"doc_key": category,
	})

	var ret []ChromemDocumentWithoutEmbedding
	for _, result := range results {
		ret = append(ret, ChromemDocumentWithoutEmbedding{
			ID:       result.ID,
			Metadata: result.Metadata,
			Content:  result.Content,
		})
	}
	return ret, nil
}

// IndexingDocument indexes a document returns string list which similiar to the document
// The function has default prompt for normal case, but it can be overrided by systemPrompt
func (lrd *LocalRelevantDocuments) IndexingDocument(ctx context.Context, doc string, systemPrompt string, count int) ([]string, error) {

	schema := llm.JSONSchemaDefinition{
		Schema:      "https://json-schema.org/draft/2020-12/schema",
		Type:        "object",
		ID:          "https://example.com/person.schema.json",
		Title:       "document-reference-descriptions",
		Description: "Generated queries,summaries or descriptions related to the input document.",
		Properties: map[string]llm.JSONSchemaProperty{
			"texts": {
				Description: "Generated queries , summaries or descriptions related to the input document.",
				Type:        "array",
				Items: &llm.JSONSchemaProperty{
					Type: "string",
				},
			},
		},
		Required: []string{"texts"},
	}
	client, err := llm.NewLLMClient(lrd.chroma.LLMSettings)
	if err != nil {
		return nil, fmt.Errorf("Settings %v could not be initialized: %s", lrd.chroma.LLMSettings, err.Error())
	}
	if systemPrompt == "" {
		systemPrompt = `
I have an input text, and I need you to generate multiple pieces of text that are closely related to it. 
For each piece, ensure it aligns with one of the following categories:

- Question: Create a question that the input text can answer or address.
- Statement: Write a concise statement that describes or relates to the input text.
- Summary: Generate a brief summary of the input text.

Please generate your output using the same language as the input text.

Your responses should remain relevant to the input text and demonstrate a clear connection to its content.`
	}
	systemPrompt += fmt.Sprintf("Try to generate at least %d items.", count)

	result, err := client.FormattedOutputWithSchema(ctx, []llm.Message{
		{
			Role:    "system",
			Content: systemPrompt,
		},
		{
			Role:    "user",
			Content: doc,
		},
	}, "make index of document", "sementic-index", schema)
	if err != nil {
		return nil, err
	}

	queriesVal, ok := result["texts"]
	if !ok {
		return nil, fmt.Errorf("queries not found in the result:%v", result)
	}

	var queries []string
	for _, q := range queriesVal.([]interface{}) {
		queries = append(queries, q.(string))
	}

	return queries, nil

}

type VectorCollectionStatistics struct {
	Count int    `json:"count"`
	Name  string `json:"name"`
}

type DocumentStatistics struct {
	Name  string `json:"name"`
	Count int    `json:"count"`
}

// RelevantDocumentStatistics is a struct for relevant document statistics
type RelevantDocumentStatistics struct {
	References []VectorCollectionStatistics `json:"references"`
	Docuements []DocumentStatistics         `json:"documents"`
}

// GetRelevantDocumentStatistics returns the statistics of the relevant document
func (lrd *LocalRelevantDocuments) GetRelevantDocumentStatistics() *RelevantDocumentStatistics {
	var ret RelevantDocumentStatistics
	collections := lrd.chroma.ListCollections()
	for _, collectionName := range collections {
		col, err := lrd.chroma.GetCollection(collectionName)
		if err != nil {
			q.Q(err)
			continue
		}
		ret.References = append(ret.References, VectorCollectionStatistics{
			Count: col.Count(),
			Name:  collectionName,
		})
	}
	for _, doc := range lrd.extraDocs {
		ret.Docuements = append(ret.Docuements, DocumentStatistics{
			Name:  doc.GetName(),
			Count: doc.TotalCount(),
		})
	}

	return &ret
}

// ListRelevantDocuments list all the relevant documents
func (lrd *LocalRelevantDocuments) ListRelevantDocuments(category string, page, pagesize int) ([]def.DocumentItem, error) {
	for _, d := range lrd.extraDocs {
		if d.GetName() == category {
			return d.GetPage(page, pagesize)
		}
	}
	return nil, fmt.Errorf("Didn't find the document category %s", category)
}

// TotalCount
func (lrd *LocalRelevantDocuments) TotalCount(category string) int {
	for _, d := range lrd.extraDocs {
		if d.GetName() == category {
			return d.TotalCount()
		}
	}
	return 0
}
