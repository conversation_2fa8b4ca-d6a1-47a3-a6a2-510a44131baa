package llm

import (
	"reflect"

	"github.com/invopop/jsonschema"
)

// func GenOpenAIJSONSchema[T any](v T) (string, error) {
// 	schemaPram:=openai.ResponseFormatJSONSchema
// }

type JSONSchemaDefinition struct {
	Schema               string                        `json:"$schema"`
	Type                 string                        `json:"type"`
	ID                   string                        `json:"$id"`
	Title                string                        `json:"title"`
	Description          string                        `json:"description"`
	Properties           map[string]JSONSchemaProperty `json:"properties"`
	Required             []string                      `json:"required"`
	AdditionalProperties bool                          `json:"additionalProperties"`
}

type JSONSchemaProperty struct {
	Description          string                        `json:"description,omitempty"`
	Type                 string                        `json:"type,omitempty"`       // e.g., "string", "object", "array"
	Items                any                           `json:"items,omitempty"`      // For arrays
	Properties           map[string]JSONSchemaProperty `json:"properties,omitempty"` // For objects
	Required             []string                      `json:"required,omitempty"`   // For nested objects
	Enum                 []interface{}                 `json:"enum,omitempty"`       // Allowed values
	OneOf                []JSONSchemaProperty          `json:"oneOf,omitempty"`      // For multiple types
	AnyOf                []JSONSchemaProperty          `json:"anyOf,omitempty"`
	AllOf                []JSONSchemaProperty          `json:"allOf,omitempty"`
	Format               string                        `json:"format,omitempty"`  // e.g., "date-time", "email"
	Default              interface{}                   `json:"default,omitempty"` // Default value
	Example              interface{}                   `json:"example,omitempty"` // Example value
	AdditionalProperties bool                          `json:"additionalProperties"`
}

type ResponseFormatJSONSchemaJSONSchemaParam struct {
	// The name of the response format. Must be a-z, A-Z, 0-9, or contain underscores
	// and dashes, with a maximum length of 64.
	Name string `json:"name,required"`
	// A description of what the response format is for, used by the model to determine
	// how to respond in the format.
	Description string `json:"description"`
	// The schema for the response format, described as a JSON Schema object.
	Schema any `json:"schema"`
	// Whether to enable strict schema adherence when generating the output. If set to
	// true, the model will always follow the exact schema defined in the `schema`
	// field. Only a subset of JSON Schema is supported when `strict` is `true`. To
	// learn more, read the
	// [Structured Outputs guide](https://platform.openai.com/docs/guides/structured-outputs).
	Strict bool `json:"strict"`
}

// GenOpenAIResponseFormatJSONSchema generates a JSON schema for the provided data instance.
func GenOpenAIResponseFormatJSONSchema(desc string, data any) *ResponseFormatJSONSchemaJSONSchemaParam {
	if data == nil {
		return nil
	}
	schema := GenerateSchema(data)
	return &ResponseFormatJSONSchemaJSONSchemaParam{
		Name:        getTypeName(data),
		Description: desc,
		Schema:      schema,
		Strict:      true,
	}
}

// getTypeName returns the name of the provided data's type.
func getTypeName(data any) string {
	if data == nil {
		return ""
	}
	t := reflect.TypeOf(data)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	return t.Name()
}

// GenerateSchema generates a JSON schema for the provided data instance.
// the input data must be a pointer to a struct.
func GenerateSchema(data any) *jsonschema.Schema {
	if data == nil {
		return nil
	}
	reflector := jsonschema.Reflector{
		AllowAdditionalProperties: false,
		DoNotReference:            true,
	}
	return reflector.Reflect(data)
}

// isPointerToStruct checks if the provided parameter is a pointer to a struct.
// or map, slice
func isPointerToSpecificTypes(param any) bool {
	t := reflect.TypeOf(param)
	// Check if the type is a pointer
	if t == nil {
		return false
	}
	if t.Kind() == reflect.Ptr {
		// Get the type it points to
		elemType := t.Elem()
		switch elemType.Kind() {
		case reflect.Struct:
			return true
		case reflect.Map:
			return true
		case reflect.Slice:
			return true
		}
	} else {
		// Directly check if the parameter is a map or slice
		switch t.Kind() {
		case reflect.Map:
			return true
		case reflect.Slice:
			return true
		}
	}
	return false
}
