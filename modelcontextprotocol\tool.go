package modelcontextprotocol

import (
	"fmt"
	"strings"

	"github.com/mark3labs/mcp-go/mcp"
)

// FormatToolForLLM accepts mcp.Tool and formats it for LLM.
func FormatToolForLLM(tool mcp.Tool) string {
	argsDesc := []string{}

	for paramName, paramInfoInterface := range tool.InputSchema.Properties {
		paramInfo, ok := paramInfoInterface.(map[string]interface{})
		if !ok {
			continue
		}

		description := "No description"
		if desc, ok := paramInfo["description"].(string); ok {
			description = desc
		}

		argDesc := fmt.Sprintf("- %s: %s", paramName, description)

		// Check if this parameter is required
		isRequired := false
		for _, req := range tool.InputSchema.Required {
			if req == paramName {
				isRequired = true
				break
			}
		}

		if isRequired {
			argDesc += " (required)"
		}

		argsDesc = append(argsDesc, argDesc)
	}

	return fmt.Sprintf(`
Tool: %s
Description: %s
Arguments:
%s
`, tool.Name, tool.Description, strings.Join(argsDesc, "\n"))

}
