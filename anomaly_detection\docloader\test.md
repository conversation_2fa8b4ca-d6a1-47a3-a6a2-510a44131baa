![Alt text](images/image14.png)
<h4 style="text-align: center; color:gray">BlackBear TechHive.</h4>


<h2 style="text-align: center;">Nimbl Network Information Management </h2>
<h4 style="text-align: center;">User Manual</h4>
<h4 style="text-align: center;"> v1.0.5</h4>
<h4 style="text-align: center;"> April 1, 2024</h4>

<div style="page-break-after: always;"></div>

## Important Announcement

The information contained in this document is the property of BlackBear TechHive, Inc., and is supplied for the sole purpose of operation and maintenance of BlackBear TechHive, Inc., products.

No part of this publication is to be used for any other purposes, and it is not to be reproduced, copied, disclosed, transmitted, stored in a retrieval system, or translated into any human or computer language, in any form, by any means, in whole or in part, without the prior explicit written consent of BlackBear TechHive.

Offenders will be held liable for damages and prosecution.

All rights, including rights created by patent grant or registration of a utility model or design, are reserved.


## Disclaimer 

We have checked the contents of this manual for agreement with the hardware and the software described. Since deviations cannot be precluded entirely, we cannot guarantee full agreement. However, the data in this manual is reviewed regularly and any necessary corrections will be included in subsequent editions.

Suggestions for improvement are welcome. All other product names referenced herein are registered trademarks of their respective companies.


## Who Should Use This User Manual

This manual is to be used by qualified network personnel or support technicians who are familiar with network operations and might be useful for system programmers or network planners as well. This manual also provides helpful and handy information for first-time users. For any related problems, please contact your local distributor.


## Warranty Period

There is no warranty for the software. Please refer to the license agreement.



#####  Create the Client Settings file

* Manually create an OpenVPN client file with the necessary settings for communication with the server.
* Save the file as "client.ovpn" and copy the following settings, then paste them into the file.
```
Client
resolv-retry infinite
dev tun
remote *************** 1194
proto udp
nobind
persist-key
persist-tun
ca "C:/Program Files/OpenVPN/config/ca.crt"
cert "C:/Program Files/OpenVPN/config/Client1.crt"
key "C:/Program Files/OpenVPN/config/Client1.key"
cipher AES-256-CBC
auth SHA256
comp-lzo
verb 3
```
 
> Note: If you have changed the name of the client key files during the server configuration, make sure to replace "client1" with the actual name you used for the client.


##### ******* Uploading OpenVPN client Keys

**Step 1 : Upload OpenVPN client keys which are copied and saved in the path C:\Program Files\OpenVPN\config as shown in figure 14.2.2.**

![Alt text](images/image68.png)


Figure OpenVPN Client Files

**Step 2 : Navigate to the device settings and access the OpenVPN keys section. Upload the keys following the steps outlined in Figures 14.2.3 and 14.2.4.**

<img src='images/image12.png' width='400'>

Figure OpenVPN Client Files

<img src='images/image84.png' width='400'>

Figure OpenVPN Keys Upload


##### ******* Connect to Remote OpenVPN Server


* Navigate to the OpenVPN status within the device settings. Click on the "Connect" tab and patiently wait for 2-3 minutes until the server assigns a Remote Virtual IP Address, and the status reflects as connected. Follow the steps depicted in Figures 14.2.5 and 14.2.6 for reference.
<!-- TODO check chapter and image number!!!! -->

<img src='images/image12.png' width='400'>

Figure OpenVPN Status


<img src='images/image3.png' width='400'>

Figure OpenVPN Connected Status

 


##### *******  Client-Side Setup on Windows Computer
**Step 1 : Install OpenVPN on Windows:**

* Download OpenVPN from the following link:[https://openvpn.net/community-downloads/](https://openvpn.net/community-downloads/)  Choose version openvpn-install-2.4.6-I602.exe for installation.

**Step 2 : Create the client.opvn File**
* Manually create the client.opvn file and insert the OpenVPN client settings provided below.
```
Client
resolv-retry infinite
dev tun
remote *************** 1194
proto udp
nobind
persist-key
persist-tun
ca "C:/Program Files/OpenVPN/config/ca.crt"
cert "C:/Program Files/OpenVPN/config/Client1.crt"
key "C:/Program Files/OpenVPN/config/Client1.key"
cipher AES-256-CBC
auth SHA256
comp-lzo
verb 3
```

**Step 3 : Steps to use OpenVPN application**

* Kindly follow the instructions depicted in the figure 14.3.1.

<img src='images/image21.png' width='300'>

Figure OpenVPN import file

*  A notification will appear indicating that the client is now connected. Observe the change in color to confirm the successful connection, as illustrated in Figures 14.3.2 and 14.3.3.

<img src='images/image11.png' width='300'>

Figure OpenVPN Connected Notification 

<img src='images/image28.png' width='300'>

Figure OpenVPN Connected status icon


* Now users Once connected, you should have access to the resources on the remote network, as defined by the server's configuration.
* This test is to ensure that the local PC can connect to the MB5901 using the virtual IP of the VPN without connecting the cable to the MB5901.


##### ******* Test Results

* Local PC virtual IP : ********

<img src='images/image11.png' width='300'>

Figure OpenVPN Connected status in local PC

* MB5901 Virtual IP : ********

<img src='images/image101.png' width='300'>

Figure OpenVPN Connected status in MB5901
