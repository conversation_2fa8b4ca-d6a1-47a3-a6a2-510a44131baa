package utils

import "testing"

// TestCompletion
func TestCompletion(t *testing.T) {
	// make prompt
	prompt := []CompletionMessage{
		{
			Role:    "system",
			Content: "Consider a message to be an anomaly if it indicates unexpected system behavior,errors, or security concerns. , return a json with key 'normal' and 'reason'. 'normal` is a boolean, 'reason' is a string.",
		},
		{
			Role:    "user",
			Content: `<5>May 15 15:31:18 jilllocal main: device 00:11:23:A1:2C:3D is not in the ACL`,
		},
	}
	//please determine if it is normal or an anomaly based on the criteria provided.
	//Consider a message to be an anomaly if it indicates unexpected system behavior,
	// errors, or security concerns. Normal messages are routine logs that do not suggest any issues.

	result, err := CompletionRequest(prompt)
	if err != nil {
		t.Fatal(err)
	}
	for _, choice := range result.Choices {
		t.Log(choice.Message.Content)
	}
}

// TestRequestOpenAISyslogAnalysis tests the RequestOpenAISyslogAnalysis function
func TestRequestOpenAISyslogAnalysis(t *testing.T) {
	log := `<5>May 15 15:31:18 jilllocal main: device 00:11:23:A1:2C:3D is not in the ACL`
	result, err := OpenAIDetect(log)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(result)
	if result.Normal {
		t.Fatal("expected anomaly message")
	}
	log = `<5>May 15 15:31:18 jilllocal main: device 00:11:23:A1:2C:3D port 4 down`
	result, err = OpenAIDetect(log)
	if err != nil {
		t.Fatal(err)
	}
	if result.Normal {
		t.Fatal("expected anomaly message")
	}
	t.Log(result)

	log = `<1>May 15 15:23:05 jilllocal InsertDev: new device: 00-60-E9-26-31-0D`
	result, err = OpenAIDetect(log)
	if err != nil {
		t.Fatal(err)
	}
	if !result.Normal {
		t.Fatal("expected normal message")
	}
	t.Log(result)
	log = `<134>Jul 17 06:14:36 combo ftpd[23587]: connection from 83.116.207.11 (aml-sfh-3310b.adsl.wanadoo.nl) at Sun Jul 17 06:14:36 2005 `
	result, err = OpenAIDetect(log)
	if err != nil {
		t.Fatal(err)
	}
	if !result.Normal {
		t.Fatal("expected normal message")
	}
	t.Log(result)
}
