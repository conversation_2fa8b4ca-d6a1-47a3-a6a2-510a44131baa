package modelcontextprotocol

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"testing"
	"time"

	"github.com/mark3labs/mcp-go/client"
	mcpclient "github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
)

func cleanupTest(tempDir string) {
	os.RemoveAll(tempDir)
	viper.Reset()
}

func TestAddMCPServer(t *testing.T) {
	tempDir := setupTestViper(t)
	defer cleanupTest(tempDir)

	// Create test server settings
	testServer := MCPServer{
		Type:    MCPServerTypeStdio,
		Name:    "test-server",
		Command: "test-command",
		Env:     []string{"ENV1=value1"},
		Args:    []string{"--arg1", "value1"},
	}

	// Test adding the server
	err := AddMCPServerToConfig(testServer)
	assert.NoError(t, err)

	// Verify the server was added
	var servers []MCPServer
	err = viper.UnmarshalKey("mcpservers", &servers)
	assert.NoError(t, err)

	assert.Len(t, servers, 1)
	assert.Equal(t, "test-server", servers[0].Name)
	assert.Equal(t, MCPServerTypeStdio, servers[0].Type)
	assert.Equal(t, "test-command", servers[0].Command)
	assert.Equal(t, []string{"ENV1=value1"}, servers[0].Env)
	assert.Equal(t, []string{"--arg1", "value1"}, servers[0].Args)

	// Test adding a second server
	testServer2 := MCPServer{
		Type: MCPServerTypeSSE,
		Name: "test-server-sse",
		URL:  "http://test-url",
	}

	err = AddMCPServerToConfig(testServer2)
	assert.NoError(t, err)

	// Verify both servers exist
	err = viper.UnmarshalKey("mcpservers", &servers)
	assert.NoError(t, err)

	assert.Len(t, servers, 2)
	assert.Equal(t, "test-server-sse", servers[1].Name)
	assert.Equal(t, MCPServerTypeSSE, servers[1].Type)
	assert.Equal(t, "http://test-url", servers[1].URL)
}

func TestGetMCPServers(t *testing.T) {
	tempDir := setupTestViper(t)
	defer cleanupTest(tempDir)

	// Add test servers to config
	testServers := []MCPServer{
		{
			Type:    MCPServerTypeStdio,
			Name:    "test-server-1",
			Command: "test-command-1",
		},
		{
			Type:    MCPServerTypeStdio,
			Name:    "test-server-2",
			Command: "test-command-2",
		},
		{
			Type: MCPServerTypeSSE,
			Name: "test-server-3",
			URL:  "http://test-url-3",
		},
	}

	// Add servers directly to viper config
	viper.Set("mcpservers", testServers)
	err := viper.WriteConfig()
	assert.NoError(t, err)

	// Test getting a single server
	servers, err := GetMCPServersFromConfig([]string{"test-server-1"})
	assert.NoError(t, err)
	assert.Len(t, servers, 1)
	assert.Equal(t, "test-server-1", servers[0].Name)

	// Test getting multiple servers
	servers, err = GetMCPServersFromConfig([]string{"test-server-1", "test-server-3"})
	assert.NoError(t, err)
	assert.Len(t, servers, 2)

	// Check if server names are correct
	serverNames := []string{servers[0].Name, servers[1].Name}
	assert.Contains(t, serverNames, "test-server-1")
	assert.Contains(t, serverNames, "test-server-3")

	// Test getting a non-existent server
	servers, err = GetMCPServersFromConfig([]string{"non-existent-server"})
	assert.Error(t, err)
	assert.Nil(t, servers)

	// Test getting servers with empty config
	viper.Set("mcpservers", nil)
	servers, err = GetMCPServersFromConfig([]string{"test-server-1"})
	assert.Error(t, err)
	assert.Nil(t, servers)
}

func compileTestServer(outputPath string) error {
	cmd := exec.Command(
		"go",
		"build",
		"-o",
		outputPath,
		"./testdata/mockstdio_server.go",
	)
	if output, err := cmd.CombinedOutput(); err != nil {
		return fmt.Errorf("compilation failed: %v\nOutput: %s", err, output)
	}
	return nil
}

func TestCreateMCPClient(t *testing.T) {
	mockServerPath := filepath.Join(os.TempDir(), "mockstdio_server")
	if runtime.GOOS == "windows" {
		mockServerPath += ".exe"
	}
	if err := compileTestServer(mockServerPath); err != nil {
		t.Fatalf("Failed to compile mock server: %v", err)
	}
	defer os.Remove(mockServerPath)

	server := MCPServer{
		Type:    MCPServerTypeStdio,
		Name:    "test-server",
		Command: mockServerPath,
	}

	err := server.InitializeMCPClient()
	assert.NoError(t, err)
	defer server.Client.Close()

	// client.Ping(ctx) to check if the server is running
	t.Run("Ping", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		err := server.Client.Ping(ctx)
		if err != nil {
			t.Errorf("Ping failed: %v", err)
		}
	})

	t.Run("ListResources and read resource", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		request := mcp.ListResourcesRequest{}
		result, err := server.Client.ListResources(ctx, request)
		if err != nil {
			t.Errorf("ListResources failed: %v", err)
		}

		if len(result.Resources) != 1 {
			t.Errorf("Expected 1 resource, got %d", len(result.Resources))
		}

		for _, resource := range result.Resources {

			resourceURI := resource.URI
			request := mcp.ReadResourceRequest{}
			request.Params.URI = resourceURI
			t.Logf("Read resource %s: ", resourceURI)
			result, err := server.Client.ReadResource(ctx, request)
			if err != nil {
				t.Errorf("ReadResource failed: %v", err)
			}
			// dump result
			for _, content := range result.Contents {
				t.Logf("Content: %+v", content)
			}
		}
	})

	t.Run("Subscribe and Unsubscribe", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		// Test Subscribe
		subRequest := mcp.SubscribeRequest{}
		subRequest.Params.URI = "test://resource"
		err := server.Client.Subscribe(ctx, subRequest)
		if err != nil {
			t.Errorf("Subscribe failed: %v", err)
		}

		// Test Unsubscribe
		unsubRequest := mcp.UnsubscribeRequest{}
		unsubRequest.Params.URI = "test://resource"
		err = server.Client.Unsubscribe(ctx, unsubRequest)
		if err != nil {
			t.Errorf("Unsubscribe failed: %v", err)
		}
	})

	t.Run("ListPrompts", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		request := mcp.ListPromptsRequest{}
		result, err := server.Client.ListPrompts(ctx, request)
		if err != nil {
			t.Errorf("ListPrompts failed: %v", err)
		}

		if len(result.Prompts) != 1 {
			t.Errorf("Expected 1 prompt, got %d", len(result.Prompts))
		}
	})

	t.Run("GetPrompt", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		request := mcp.GetPromptRequest{}
		request.Params.Name = "test-prompt"

		result, err := server.Client.GetPrompt(ctx, request)
		if err != nil {
			t.Errorf("GetPrompt failed: %v", err)
		}

		if len(result.Messages) != 1 {
			t.Errorf("Expected 1 message, got %d", len(result.Messages))
		}
	})

	t.Run("ListTools", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		request := mcp.ListToolsRequest{}
		result, err := server.Client.ListTools(ctx, request)
		if err != nil {
			t.Errorf("ListTools failed: %v", err)
		}

		if len(result.Tools) != 1 {
			t.Errorf("Expected 1 tool, got %d", len(result.Tools))
		}
	})

	t.Run("CallTool", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		request := mcp.CallToolRequest{}
		request.Params.Name = "test-tool"
		request.Params.Arguments = map[string]interface{}{
			"param1": "value1",
		}

		result, err := server.Client.CallTool(ctx, request)
		if err != nil {
			t.Errorf("CallTool failed: %v", err)
		}

		if len(result.Content) != 1 {
			t.Errorf("Expected 1 content item, got %d", len(result.Content))
		}
		t.Logf("Content: %+v", result.Content)
	})

	t.Run("SetLevel", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		request := mcp.SetLevelRequest{}
		request.Params.Level = mcp.LoggingLevelInfo

		err := server.Client.SetLevel(ctx, request)
		if err != nil {
			t.Errorf("SetLevel failed: %v", err)
		}
	})

	t.Run("Complete", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		request := mcp.CompleteRequest{}
		request.Params.Ref = mcp.PromptReference{
			Type: "ref/prompt",
			Name: "test-prompt",
		}
		request.Params.Argument.Name = "test-arg"
		request.Params.Argument.Value = "test-value"

		result, err := server.Client.Complete(ctx, request)
		if err != nil {
			t.Errorf("Complete failed: %v", err)
		}

		if len(result.Completion.Values) != 1 {
			t.Errorf(
				"Expected 1 completion value, got %d",
				len(result.Completion.Values),
			)
		}
	})

}

func TestSSEMCPClient(t *testing.T) {
	// Create MCP server with capabilities
	mcpServer := server.NewMCPServer(
		"test-server",
		"1.0.0",
		server.WithResourceCapabilities(true, true),
		server.WithPromptCapabilities(true),
		server.WithToolCapabilities(true),
	)

	// Add a test tool
	mcpServer.AddTool(mcp.NewTool(
		"test-tool",
		mcp.WithDescription("Test tool"),
		mcp.WithString("parameter-1", mcp.Description("A string tool parameter")),
	), func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: "Input parameter: " + request.Params.Arguments["parameter-1"].(string),
				},
			},
		}, nil
	})

	// Initialize
	// testServer := server.NewTestServer(mcpServer)
	// defer testServer.Close()
	type ttt struct {
		URL string
	}
	testServer := ttt{URL: "http://***************:27182/api/v1/mcp"}

	t.Run("Can create client", func(t *testing.T) {
		client, err := mcpclient.NewSSEMCPClient(testServer.URL + "/sse")
		if err != nil {
			t.Fatalf("Failed to create client: %v", err)
		}
		defer client.Close()

	})

	t.Run("Can initialize and make requests", func(t *testing.T) {
		client, err := mcpclient.NewSSEMCPClient(testServer.URL + "/sse")
		if err != nil {
			t.Fatalf("Failed to create client: %v", err)
		}
		defer client.Close()

		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		// Start the client
		if err := client.Start(ctx); err != nil {
			t.Fatalf("Failed to start client: %v", err)
		}

		// Initialize
		initRequest := mcp.InitializeRequest{}
		initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
		initRequest.Params.ClientInfo = mcp.Implementation{
			Name:    "test-client",
			Version: "1.0.0",
		}

		_, err = client.Initialize(ctx, initRequest)
		if err != nil {
			t.Fatalf("Failed to initialize: %v", err)
		}

		// if result.ServerInfo.Name != "test-server" {
		// 	t.Errorf(
		// 		"Expected server name 'test-server', got '%s'",
		// 		result.ServerInfo.Name,
		// 	)
		// }

		// Test Ping
		if err := client.Ping(ctx); err != nil {
			t.Errorf("Ping failed: %v", err)
		}

		// Test ListTools
		toolsRequest := mcp.ListToolsRequest{}
		_, err = client.ListTools(ctx, toolsRequest)
		if err != nil {
			t.Errorf("ListTools failed: %v", err)
		}
	})

	// t.Run("Can handle notifications", func(t *testing.T) {
	// 	client, err := NewSSEMCPClient(testServer.URL + "/sse")
	// 	if err != nil {
	// 		t.Fatalf("Failed to create client: %v", err)
	// 	}
	// 	defer client.Close()

	// 	notificationReceived := make(chan mcp.JSONRPCNotification, 1)
	// 	client.OnNotification(func(notification mcp.JSONRPCNotification) {
	// 		notificationReceived <- notification
	// 	})

	// 	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	// 	defer cancel()

	// 	if err := client.Start(ctx); err != nil {
	// 		t.Fatalf("Failed to start client: %v", err)
	// 	}

	// 	// Initialize first
	// 	initRequest := mcp.InitializeRequest{}
	// 	initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
	// 	initRequest.Params.ClientInfo = mcp.Implementation{
	// 		Name:    "test-client",
	// 		Version: "1.0.0",
	// 	}

	// 	_, err = client.Initialize(ctx, initRequest)
	// 	if err != nil {
	// 		t.Fatalf("Failed to initialize: %v", err)
	// 	}

	// 	// Subscribe to a resource to test notifications
	// 	subRequest := mcp.SubscribeRequest{}
	// 	subRequest.Params.URI = "test://resource"
	// 	if err := client.Subscribe(ctx, subRequest); err != nil {
	// 		t.Fatalf("Failed to subscribe: %v", err)
	// 	}

	// 	select {
	// 	case <-notificationReceived:
	// 		// Success
	// 	case <-time.After(time.Second):
	// 		t.Error("Timeout waiting for notification")
	// 	}
	// })

	t.Run("Handles errors properly", func(t *testing.T) {
		client, err := mcpclient.NewSSEMCPClient(testServer.URL + "/sse")
		if err != nil {
			t.Fatalf("Failed to create client: %v", err)
		}
		defer client.Close()

		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if err := client.Start(ctx); err != nil {
			t.Fatalf("Failed to start client: %v", err)
		}

		// Try to make a request without initializing
		toolsRequest := mcp.ListToolsRequest{}
		_, err = client.ListTools(ctx, toolsRequest)
		if err == nil {
			t.Error("Expected error when making request before initialization")
		}
	})

	t.Run("Handles context cancellation", func(t *testing.T) {
		client, err := mcpclient.NewSSEMCPClient(testServer.URL + "/sse")
		if err != nil {
			t.Fatalf("Failed to create client: %v", err)
		}
		defer client.Close()

		if err := client.Start(context.Background()); err != nil {
			t.Fatalf("Failed to start client: %v", err)
		}

		ctx, cancel := context.WithCancel(context.Background())
		cancel() // Cancel immediately

		toolsRequest := mcp.ListToolsRequest{}
		_, err = client.ListTools(ctx, toolsRequest)
		if err == nil {
			t.Error("Expected error when context is cancelled")
		}
	})

	t.Run("CallTool", func(t *testing.T) {
		client, err := client.NewSSEMCPClient(testServer.URL + "/sse")
		if err != nil {
			t.Fatalf("Failed to create client: %v", err)
		}
		defer client.Close()

		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if err := client.Start(ctx); err != nil {
			t.Fatalf("Failed to start client: %v", err)
		}

		// Initialize
		initRequest := mcp.InitializeRequest{}
		initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
		initRequest.Params.ClientInfo = mcp.Implementation{
			Name:    "test-client",
			Version: "1.0.0",
		}

		_, err = client.Initialize(ctx, initRequest)
		if err != nil {
			t.Fatalf("Failed to initialize: %v", err)
		}

		request := mcp.CallToolRequest{}
		request.Params.Name = "test-tool"
		request.Params.Arguments = map[string]interface{}{
			"parameter-1": "value1",
		}

		result, err := client.CallTool(ctx, request)
		if err != nil {
			t.Fatalf("CallTool failed: %v", err)
		}

		if len(result.Content) != 1 {
			t.Errorf("Expected 1 content item, got %d", len(result.Content))
		}
	})
}
