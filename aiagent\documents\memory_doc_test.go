package documents

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestMemoryDocumentProvider tests the memory document provider
func TestMemoryDocumentProvider(t *testing.T) {
	// Create a new memory document provider
	provider := NewLocalMemoryDocumentProvider("test", "test description")
	assert.Equal(t, "test", provider.GetName())
	assert.Equal(t, "test description", provider.GetDescription())

	// Add some documents
	assert.NoError(t, provider.Update("doc1", "doc1 content"))
	assert.NoError(t, provider.Update("doc2", "doc2 content"))

	// Query the documents
	res, err := provider.Query("doc1")
	assert.NoError(t, err)
	assert.Contains(t, res, "doc1 content")
	assert.NotContains(t, res, "doc2 content")

	// Find a document
	ret, err := provider.Find("doc1")
	assert.NoError(t, err)
	assert.Equal(t, "doc1 content", ret)

	// Update a document
	assert.NoError(t, provider.Update("doc1", "doc1 content updated"))
	ret, err = provider.Find("doc1")
	assert.NoError(t, err)
	assert.Equal(t, "doc1 content updated", ret)

	// serialize and deserialize
	data, err := provider.Serialize()
	assert.NoError(t, err)
	provider2, err := DeserializeLocalMemoryDocumentProvider(data)
	assert.NoError(t, err)

	assert.Equal(t, "test", provider2.GetName())
	assert.Equal(t, "test description", provider2.GetDescription())

	// Find the document
	ret, err = provider2.Find("doc1")
	assert.NoError(t, err)
	assert.Equal(t, "doc1 content updated", ret)
}
