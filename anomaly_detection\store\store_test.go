package store

import (
	"context"
	"mnms/anomaly_detection/store/anomstore"
	"testing"
)

// defined fake data for testing
var fakeData = []Item{
	{
		Vector: []float32{1, 2, 3},
		Meta:   map[string]any{"a": 1},
		Text:   "1, 2, 3",
	},
	{
		Vector: []float32{2, 3, 4},
		Meta:   map[string]any{"b": 2},
		Text:   "2, 3, 4",
	},
	{
		Vector: []float32{3, 4, 5},
		Meta:   map[string]any{"c": 3},
		Text:   "3, 4, 5",
	},
	{
		Vector: []float32{4, 5, 6},
		Meta:   map[string]any{"d": 4},
		Text:   "4, 5, 6",
	},
}

// TestStoreBasicFuncs tests the basic functions of the store
func TestStoreBasicFuncs(t *testing.T) {
	s := NewStandardStore()
	// Add some
	for _, record := range fakeData {
		_, err := s.Add(record.Text, record.Meta, record.Vector)
		if err != nil {
			t.Fatal(err)
		}
	}
	//expect 4 items
	if s.Len() != 4 {
		t.<PERSON><PERSON><PERSON>("Unexpected length: got %v, want %v", s.<PERSON>(), 4)
	}
	// Get first and last to validate
	first, err := s.GetContent(0)
	if err != nil {
		t.Fatal(err)
	}
	if first != "1, 2, 3" {
		t.Errorf("Unexpected record: got %v, want %v", first, "1, 2, 3")
	}
	last, err := s.GetContent(3)
	if err != nil {
		t.Fatal(err)
	}
	if last != "4, 5, 6" {
		t.Errorf("Unexpected record: got %v, want %v", last, "4, 5, 6")
	}
	// check second item's content and meta
	secondContent, err := s.GetContent(1)
	if err != nil {
		t.Fatal(err)
	}
	if secondContent != "2, 3, 4" {
		t.Errorf("Unexpected record: got %v, want %v", secondContent, "2, 3, 4")
	}
	secondMeta, err := s.GetMeta(1)
	if err != nil {
		t.Fatal(err)
	}
	if secondMeta["b"] != 2 {
		t.Errorf("Unexpected record: got %v, want %v", secondMeta["b"], 2)
	}

	// Update the second item

	err = s.Update(1, "2, 3, 4, 5", map[string]any{"b": 2, "c": 3}, []float32{2, 3, 4, 5})
	if err != nil {
		t.Fatal(err)
	}

	// check second item's content and meta
	secondContent, err = s.GetContent(1)
	if err != nil {
		t.Fatal(err)
	}
	if secondContent != "2, 3, 4, 5" {
		t.Errorf("Unexpected record: got %v, want %v", secondContent, "2, 3, 4, 5")
	}
	secondMeta, err = s.GetMeta(1)
	if err != nil {
		t.Fatal(err)
	}
	if secondMeta["b"] != 2 || secondMeta["c"] != 3 {
		t.Errorf("Unexpected record: got %v, want %v", secondMeta, map[string]any{"b": 2, "c": 3})
	}

	// Test SearchSimilar
	result, err := s.Search(context.Background(), []float32{1, 2, 3}, 1)
	if err != nil {
		t.Fatal(err)
	}
	if len(result) != 1 {
		t.Errorf("Unexpected length: got %v, want %v", len(result), 1)
	}
	if result[0].Content != "1, 2, 3" {
		t.Errorf("Unexpected record: got %v, want %v", result[0].Content, "1, 2, 3")
	}
	if result[0].Metadata["a"] != 1 {
		t.Errorf("Unexpected record: got %v, want %v", result[0].Metadata["a"], 1)
	}
	t.Log("Search [1, 2, 3] result: ", result[0].Content)
	t.Log("score: ", result[0].Score)

	// Search with a different vector
	vectors := [][]float32{
		{1, 2, 3, 4},
		{1, 2, 6},
		{100, 244, 1},
	}

	for _, vector := range vectors {
		result, err := s.Search(context.Background(), vector, 1)
		if err != nil {
			t.Fatal(err)
		}
		t.Log("Search", vector, "result:", result[0].Content)
		t.Log("score:", result[0].Score)
	}

}

// TestSerialization tests the serialization and deserialization of the store
func TestSerialization(t *testing.T) {
	// create a store
	s := NewStandardStore()
	// Add some
	for _, record := range fakeData {
		_, err := s.Add(record.Text, record.Meta, record.Vector)
		if err != nil {
			t.Fatal(err)
		}
	}

	// serialize
	data, err := s.Serialize()
	if err != nil {
		t.Fatal(err)
	}

	t.Log(string(data))
	// deserialize to a new store
	s2, err := DeserializeStore(data)
	if err != nil {
		t.Fatal("DeserializeStore failed", err)
	}

	doc, err := s2.Search(context.Background(), []float32{1, 2, 3}, 1)
	if err != nil {
		t.Fatal(err)
	}
	if doc[0].Content != "1, 2, 3" {
		t.Errorf("Unexpected record: got %v, want %v", doc[0].Content, "1, 2, 3")
	}

	// Testing vectorlist
	// create a vector list
	aList := anomstore.NewVectorList()
	// Add some
	for _, record := range fakeData {
		_, err := aList.Add(record.Text, record.Meta, record.Vector)
		if err != nil {
			t.Fatal(err)
		}
	}
	// serialize
	data, err = aList.Serialize()
	if err != nil {
		t.Fatal(err)
	}

	t.Log(string(data))
	// deserialize to a new vector list
	aList2, err := anomstore.DeserializeVectorList(data)
	if err != nil {
		t.Fatal("DeserializeVectorList failed", err)
	}
	// check []float32{3, 4, 5}
	doc, err = aList2.Search(context.Background(), []float32{3, 4, 5}, 1)
	if err != nil {
		t.Fatal(err)
	}
	if doc[0].Content != "3, 4, 5" {
		t.Errorf("Unexpected record: got %v, want %v", doc[0].Content, "3, 4, 5")
	}
}
