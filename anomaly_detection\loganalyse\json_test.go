package loganalyse

import (
	"encoding/json"
	"errors"
	"testing"
)

// parseJSONFromText takes a text input and finds and parses JSON objects from it.
func parseJSONFromText(text string) ([]map[string]interface{}, error) {
	var results []map[string]interface{}
	// startIndices := []int{}
	bracketCount := 0
	start := -1

	for i, char := range text {
		if char == '{' {
			if bracketCount == 0 {
				start = i
			}
			bracketCount++
		} else if char == '}' {
			bracketCount--
			if bracketCount == 0 && start != -1 {
				jsonString := text[start : i+1]
				var result map[string]interface{}
				if err := json.Unmarshal([]byte(jsonString), &result); err == nil {
					results = append(results, result)
				}
				start = -1
			} else if bracketCount < 0 {
				// Unbalanced braces
				return nil, errors.New("unbalanced closing brace detected")
			}
		}
	}

	if bracketCount != 0 {
		return nil, errors.New("unbalanced braces in input text")
	}

	return results, nil
}

func TestSingleValidJSON(t *testing.T) {
	text := `Hello world {"name": "Alice", "age": 25}`
	results, err := parseJSONFromText(text)
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	if len(results) != 1 {
		t.Fatalf("Expected 1 JSON object, got %d", len(results))
	}
	if results[0]["name"] != "Alice" || results[0]["age"] != float64(25) {
		t.Errorf("Parsed JSON does not match expected values")
	}
}

func TestMultipleValidJSON(t *testing.T) {
	text := `Data: {"key1": "value1"}, Other data: {"key2": "value2", "count": 10}`
	results, err := parseJSONFromText(text)
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	if len(results) != 2 {
		t.Fatalf("Expected 2 JSON objects, got %d", len(results))
	}
	if results[0]["key1"] != "value1" {
		t.Errorf("First JSON object does not match expected values")
	}
	if results[1]["key2"] != "value2" || results[1]["count"] != float64(10) {
		t.Errorf("Second JSON object does not match expected values")
	}
}

func TestNoJSON(t *testing.T) {
	text := `This text has no JSON here!`
	results, err := parseJSONFromText(text)
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	if len(results) != 0 {
		t.Fatalf("Expected 0 JSON objects, got %d", len(results))
	}
}

func TestInvalidJSON(t *testing.T) {
	text := `Here is invalid JSON: {name: John, age: 30}`
	results, err := parseJSONFromText(text)
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	if len(results) != 0 {
		t.Fatalf("Expected 0 valid JSON objects, got %d", len(results))
	}
}

func TestNestedJSON(t *testing.T) {
	text := `Here is nested JSON: {"user": {"name": "Bob", "details": {"age": 30, "city": "London"}}}`
	results, err := parseJSONFromText(text)
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	if len(results) != 1 {
		t.Fatalf("Expected 1 JSON object, got %d", len(results))
	}
	user, ok := results[0]["user"].(map[string]interface{})
	if !ok {
		t.Errorf("Failed to parse nested JSON structure")
	}
	if user["name"] != "Bob" || user["details"].(map[string]interface{})["age"] != float64(30) {
		t.Errorf("Parsed nested JSON does not match expected values")
	}
}

func TestJSONArray(t *testing.T) {
	text := `Here is JSON with an array: {"fruits": ["apple", "banana", "cherry"]}`
	results, err := parseJSONFromText(text)
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	if len(results) != 1 {
		t.Fatalf("Expected 1 JSON object, got %d", len(results))
	}
	if fruits, ok := results[0]["fruits"].([]interface{}); !ok || len(fruits) != 3 {
		t.Errorf("Failed to parse JSON array or incorrect number of elements")
	}
}

func TestMultilineJSON(t *testing.T) {
	text := `Multi-line JSON: {
  "name": "Eve",
  "age": 29
}`
	results, err := parseJSONFromText(text)
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	if len(results) != 1 {
		t.Fatalf("Expected 1 JSON object, got %d", len(results))
	}
	if results[0]["name"] != "Eve" || results[0]["age"] != float64(29) {
		t.Errorf("Parsed JSON does not match expected values")
	}
}

func TestMixedValidAndInvalidJSON(t *testing.T) {
	text := `Valid JSON: {"valid": true}, Invalid JSON: {missing: "quotes"}`
	results, err := parseJSONFromText(text)
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	if len(results) != 1 {
		t.Fatalf("Expected 1 valid JSON object, got %d", len(results))
	}
	if results[0]["valid"] != true {
		t.Errorf("Parsed JSON does not match expected values")
	}
}

func TestRandomTextAroundJSON(t *testing.T) {
	text := `Random text before and after JSON: Hello {"key": "value"} World!`
	results, err := parseJSONFromText(text)
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	if len(results) != 1 {
		t.Fatalf("Expected 1 JSON object, got %d", len(results))
	}
	if results[0]["key"] != "value" {
		t.Errorf("Parsed JSON does not match expected values")
	}
}
