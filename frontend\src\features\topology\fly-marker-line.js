import { Circle } from "@antv/g";
import { Line, ExtensionCategory, register, subStyleProps } from "@antv/g6";

export class FlyMarkerLine extends Line {
  getMarkerStyle(attributes) {
    const { markerConfig = {} } = attributes;
    return {
      r: markerConfig.radius || 5,
      fill: attributes.circleColor,
      offsetPath: this.shapeMap.key,
      ...subStyleProps(attributes, "marker"),
    };
  }

  onCreate() {
    const { markerConfig = {} } = this.attributes;
    const marker = this.upsert(
      "marker",
      Circle,
      this.getMarkerStyle(this.attributes),
      this
    );

    marker.animate([{ offsetDistance: 0 }, { offsetDistance: 1 }], {
      duration: markerConfig.duration || 3000,
      iterations: markerConfig.iterations || Infinity,
    });
  }
}

register(ExtensionCategory.EDGE, "fly-marker-line", FlyMarkerLine);
