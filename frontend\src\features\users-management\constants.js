export const TABLE_CONFIG = {
  pagination: {
    position: ["bottomCenter"],
    showQuickJumper: true,
    size: "default",
    defaultPageSize: 10,
    pageSizeOptions: [10, 15, 20, 25],
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
  },
  scroll: {
    x: 400,
  },
  columnsState: {
    persistenceKey: "nimbl-user-table",
    persistenceType: "localStorage",
  },
};
