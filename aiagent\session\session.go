package session

import (
	"encoding/json"
	"errors"
	"fmt"
	"mnms/llm"
	"mnms/llm/history"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/qeof/q"
)

// Constants for session management
const (
	SessionsDir = "sessions" // Directory to store session files
)

// generateSessionID creates a unique identifier for a new session using UUID
func generateSessionID() string {
	return uuid.New().String()
}

type AIAgentSessionPermission struct {
	ID       string                 `json:"id"`
	Name     string                 `json:"name"`
	Args     map[string]interface{} `json:"args"`
	Approved bool                   `json:"approved"`
}

type AIAgentSessionMeta struct {
	Prompt  string `json:"prompt"`
	Project string `json:"project"`
	Flow    string `json:"flow"`
	NodeID  string `json:"nodeid"`
}

// AIAgentSession represents the complete state of an AI agent conversation,
// including its unique identifier, creation timestamp, initial prompt,
// message history, and flow/node information
type AIAgentSession struct {
	Mutex       sync.Mutex                          `json:"-"`
	ID          string                              `json:"id"`
	CreatedAt   time.Time                           `json:"createdat"`
	Messages    *[]history.HistoryMessage           `json:"messages"`
	Meta        AIAgentSessionMeta                  `json:"meta"`
	Permissions map[string]AIAgentSessionPermission `json:"permissions"`
}

// NewAIAgentSession creates a new AIAgentSession
func NewAIAgentSession() *AIAgentSession {
	return &AIAgentSession{
		ID:          generateSessionID(),
		CreatedAt:   time.Now(),
		Messages:    &[]history.HistoryMessage{},
		Meta:        AIAgentSessionMeta{},
		Mutex:       sync.Mutex{},
		Permissions: make(map[string]AIAgentSessionPermission),
	}
}

type ToolCall struct {
	ID        string                 `json:"id"`
	Name      string                 `json:"name"`
	Arguments map[string]interface{} `json:"arguments"`
}

func (t *ToolCall) GetName() string {
	return t.Name
}

func (t *ToolCall) GetArguments() map[string]interface{} {
	return t.Arguments
}

func (t *ToolCall) GetID() string {
	return t.ID
}

// GetToolCalls returns the approved tool calls
func (s *AIAgentSession) GetToolCalls() []llm.ToolCall {
	s.Mutex.Lock()
	defer s.Mutex.Unlock()

	var toolcalls []llm.ToolCall
	for id, toolcall := range s.Permissions {

		toolcalls = append(toolcalls, &ToolCall{
			ID:        id,
			Name:      toolcall.Name,
			Arguments: toolcall.Args,
		})

	}
	return toolcalls
}

func (s *AIAgentSession) GerRejectToolCalls() []llm.ToolCall {
	s.Mutex.Lock()
	defer s.Mutex.Unlock()
	var toolcalls []llm.ToolCall
	for id, toolcall := range s.Permissions {
		if !toolcall.Approved {
			toolcalls = append(toolcalls, &ToolCall{
				ID:        id,
				Name:      toolcall.Name,
				Arguments: toolcall.Args,
			})
		}
	}
	return toolcalls
}

// GetApprovedToolCalls returns the approved tool calls
func (s *AIAgentSession) GetApprovedToolCalls() []llm.ToolCall {
	s.Mutex.Lock()
	defer s.Mutex.Unlock()
	var toolcalls []llm.ToolCall
	for id, toolcall := range s.Permissions {
		if toolcall.Approved {
			toolcalls = append(toolcalls, &ToolCall{
				ID:        id,
				Name:      toolcall.Name,
				Arguments: toolcall.Args,
			})
		}
	}
	return toolcalls
}

// SetPermissions sets the permissions for the session
func (s *AIAgentSession) SetPermissions(jsonBytes []byte) error {
	s.Mutex.Lock()
	defer s.Mutex.Unlock()

	var toolcalls []ToolCall
	if err := json.Unmarshal(jsonBytes, &toolcalls); err != nil {
		return fmt.Errorf("failed to unmarshal tool calls: %w", err)
	}

	s.Permissions = make(map[string]AIAgentSessionPermission)
	for _, toolcall := range toolcalls {
		s.Permissions[toolcall.ID] = AIAgentSessionPermission{
			Name:     toolcall.Name,
			Args:     toolcall.Arguments,
			Approved: false,
		}
	}

	// save to file
	if err := saveSessionToFile(s); err != nil {
		return fmt.Errorf("failed to save session to file: %w", err)
	}
	return nil
}

// ToolCallPermitResponse generate a JSON bytes that has tool calls information to ask user's permission
func (s *AIAgentSession) ToolCallPermitResponse(sessionID string) ([]byte, error) {
	s.Mutex.Lock()
	defer s.Mutex.Unlock()
	type response struct {
		Type      string                     `json:"type"`
		SessionID string                     `json:"sessionid"`
		ToolCalls []AIAgentSessionPermission `json:"tool_calls"`
	}

	var toolCalls []AIAgentSessionPermission
	for id, toolcall := range s.Permissions {
		toolCalls = append(toolCalls, AIAgentSessionPermission{
			ID:       id,
			Name:     toolcall.Name,
			Args:     toolcall.Args,
			Approved: toolcall.Approved,
		})
	}

	resp := response{
		Type:      "tool_call_permit",
		SessionID: sessionID,
		ToolCalls: toolCalls,
	}

	jsonBytes, err := json.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response: %w", err)
	}

	return jsonBytes, nil
}

// ClearPermissions clears the permissions for the session
func (s *AIAgentSession) ClearPermissions() error {
	s.Mutex.Lock()
	defer s.Mutex.Unlock()
	s.Permissions = make(map[string]AIAgentSessionPermission)
	// save to file
	if err := saveSessionToFile(s); err != nil {
		q.Q("Failed to save session to file", "id", s.ID, "error", err)
		return err
	}
	return nil
}

// PermitToolCall permits a tool call
func (s *AIAgentSession) PermitToolCall(toolcallID string) error {
	s.Mutex.Lock()
	defer s.Mutex.Unlock()
	// check if the toolcallID is in the permissions
	if _, ok := s.Permissions[toolcallID]; !ok {
		return fmt.Errorf("toolcallID not found in permissions")
	}

	perm := s.Permissions[toolcallID]
	perm.Approved = true
	s.Permissions[toolcallID] = perm
	return nil
}

// CheckAllToolCallsPermitted checks if all tool calls are permitted
func (s *AIAgentSession) CheckAllToolCallsPermitted() bool {
	s.Mutex.Lock()
	defer s.Mutex.Unlock()
	return len(s.Permissions) == 0
}

// mutex for session operations
var sessionMutex = sync.Mutex{}

// init sets up the session management system by ensuring the sessions directory exists
func init() {
	if err := os.MkdirAll(SessionsDir, 0755); err != nil {
		q.Q("Failed to create sessions directory", err)
	}
}

// getSessionFilePath constructs the full path to a session's JSON file
func getSessionFilePath(id string) string {
	return filepath.Join(SessionsDir, fmt.Sprintf("%s.json", id))
}

// SessionFileData represents the serializable format of session data stored in JSON files
type SessionFileData struct {
	Messages []history.HistoryMessage `json:"messages"`
}

// saveSessionToFile persists a session's data to disk in JSON format
func saveSessionToFile(s *AIAgentSession) error {
	sessionMutex.Lock()
	defer sessionMutex.Unlock()

	filePath := getSessionFilePath(s.ID)

	data, err := json.MarshalIndent(s, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal session data: %w", err)
	}
	q.Q("saveSessionToFile", "id", s.ID, "data", string(data))
	return os.WriteFile(filePath, data, 0644)
}

// loadSessionFromFile retrieves and deserializes a session from its JSON file
func loadSessionFromFile(id string) (*AIAgentSession, error) {
	sessionMutex.Lock()
	defer sessionMutex.Unlock()

	filePath := getSessionFilePath(id)
	data, err := os.ReadFile(filePath)
	if err != nil {

		return nil, fmt.Errorf("failed to read session file: %w", err)
	}

	var sessionData AIAgentSession

	if err := json.Unmarshal(data, &sessionData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal session data: %w", err)
	}

	if sessionData.Messages == nil {
		sessionData.Messages = &[]history.HistoryMessage{}
	}
	// mutex
	sessionData.Mutex = sync.Mutex{}

	return &sessionData, nil
}

// ChatSessionList represents a summary view of a session for listing purposes
type ChatSessionList struct {
	SessionID string    `json:"sessionid"`
	CreatedAt time.Time `json:"createdat"`
	Prompt    string    `json:"prompt"`
}

// ClearAllSessions clears all sessions
func ClearAllSessions() error {
	sessionMutex.Lock()
	defer sessionMutex.Unlock()
	return os.RemoveAll(SessionsDir)
}

// GetAllSessions retrieves a list of all available sessions with their basic information
func GetAllSessions() ([]ChatSessionList, error) {

	files, err := os.ReadDir(SessionsDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read sessions directory: %w", err)
	}

	var sessions []ChatSessionList
	for _, file := range files {
		if !file.IsDir() {
			// Read the file
			data, err := os.ReadFile(filepath.Join(SessionsDir, file.Name()))
			if err != nil {
				return nil, fmt.Errorf("failed to read session file: %w", err)
			}

			// Parse the file
			var sessionData AIAgentSession
			if err := json.Unmarshal(data, &sessionData); err != nil {
				return nil, fmt.Errorf("failed to unmarshal session data: %w", err)
			}

			// Get the prompt
			var prompt string
			if len(*sessionData.Messages) > 0 && len((*sessionData.Messages)[0].Content) > 0 {
				prompt = (*sessionData.Messages)[0].Content[0].Text
			} else {
				prompt = "[Empty Session]" // Handle empty sessions
			}

			// Get the session ID
			sessionID := strings.TrimSuffix(file.Name(), ".json")

			// Get the creation time
			fileInfo, err := file.Info()
			if err != nil {
				continue // Skip this file if info cannot be retrieved
			}
			createdAt := fileInfo.ModTime()

			// Add to the sessions list
			sessions = append(sessions, ChatSessionList{
				SessionID: sessionID,
				CreatedAt: createdAt,
				Prompt:    prompt,
			})
		}
	}
	return sessions, nil
}

type CreateAIAgentSessionOption struct {
	Project string
	Flow    string
	NodeID  string
}

// SetMeta sets the meta for the session
// project, flow, nodeid
func (s *AIAgentSession) SetMeta(project, flow, nodeid string) error {
	s.Meta = AIAgentSessionMeta{
		Project: project,
		Flow:    flow,
		NodeID:  nodeid,
	}
	// save to file
	if err := saveSessionToFile(s); err != nil {
		q.Q("Failed to save session to file", "id", s.ID, "error", err)
		return err
	}
	return nil
}

// SetNodeID sets the nodeid for the session
func (s *AIAgentSession) SetNodeID(nodeid string) error {
	s.Meta.NodeID = nodeid
	// save to file
	if err := saveSessionToFile(s); err != nil {
		q.Q("Failed to save session to file", "id", s.ID, "error", err)
		return err
	}
	return nil
}

// CreateAIAgentSession initializes a new AI agent conversation session
// params can be nil, if nil that means session is a normal chat session without project, flow and nodeid
func CreateAIAgentSession(params *CreateAIAgentSessionOption) string {

	if params == nil {
		params = &CreateAIAgentSessionOption{
			Project: "",
			Flow:    "",
			NodeID:  "",
		}
	}

	s := NewAIAgentSession()
	s.Meta = AIAgentSessionMeta{
		Project: params.Project,
		Flow:    params.Flow,
		NodeID:  params.NodeID,
	}

	// Create empty session file
	if err := saveSessionToFile(s); err != nil {
		q.Q("Failed to create session file", "id", s.ID, "error", err)
	}

	return s.ID
}

// GetAIAgentSession retrieves a session by its unique identifier
func GetAIAgentSession(id string) (*AIAgentSession, error) {

	// try to load session from file
	s, err := loadSessionFromFile(id)
	if err != nil {
		return nil, fmt.Errorf("session not found: %w", err)
	}

	return s, nil
}

// GetHistoryMessages retrieves the message history for a specific session
func GetHistoryMessages(id string) (*[]history.HistoryMessage, error) {
	q.Q("GetHistoryMessages", "id", id)

	// try to load from file
	s, err := loadSessionFromFile(id)
	if err != nil {
		q.Q("Failed to load session from file", "id", id, "error", err)
		return nil, fmt.Errorf("session not found or corrupt: %w", err)
	}

	if s == nil {
		return nil, errors.New("session not found")
	}

	return s.Messages, nil
}

// ClearToolCallMessages clears the tool call messages for a specific session
func ClearToolCallMessages(id string) error {
	s, err := loadSessionFromFile(id)
	if err != nil {
		return fmt.Errorf("session not found: %w", err)
	}
	s.Mutex.Lock()
	defer s.Mutex.Unlock()

	history.ClearToolMessages(s.Messages)

	return saveSessionToFile(s)
}

// UpdateHistoryMessages updates the message history for a specific session
func UpdateHistoryMessages(id string, messages []history.HistoryMessage) error {

	s, err := loadSessionFromFile(id)
	if err != nil {
		return fmt.Errorf("session not found: %w", err)
	}

	s.Messages = &messages

	// Persist to file
	if err := saveSessionToFile(s); err != nil {
		q.Q("Failed to save session to file", "id", id, "error", err)
		return err
	}
	return nil
}

// UpdateChatSession updates the complete state of a session
func UpdateChatSession(id string, chatSession *AIAgentSession) error {

	// Persist to file
	if err := saveSessionToFile(chatSession); err != nil {
		q.Q("Failed to save session to file", "id", id, "error", err)
		return err
	}
	return nil
}

// DeleteAIAgentSession removes a session and its associated file
func DeleteAIAgentSession(id string) error {
	sessionMutex.Lock()
	defer sessionMutex.Unlock()

	// Remove file if it exists
	filePath := getSessionFilePath(id)
	if err := os.Remove(filePath); err != nil && !os.IsNotExist(err) {
		q.Q("Failed to delete session file", "id", id, "error", err)
		return err
	}
	return nil
}
