import React, { useMemo } from "react";
import { useCommandTableColumn } from "../../../components/table-column/command-table";
import ProTable from "@ant-design/pro-table";
import { useCommandFilters } from "../hooks/useCommandFilter";
import { theme } from "antd";
import ExportData from "../../../components/export-data/export-data";

const CommandTable = () => {
  const { token } = theme.useToken();
  const columns = useCommandTableColumn();
  const { filteredData, isFetching, inputSearch, setInputSearch, refetch } =
    useCommandFilters(columns);

  // Memoize table configurations to prevent unnecessary re-renders
  const tableConfig = useMemo(
    () => ({
      toolbar: {
        search: {
          allowClear: true,
          onSearch: setInputSearch,
          onClear: () => setInputSearch(""),
        },
        actions: [
          <ExportData
            Columns={columns}
            DataSource={filteredData}
            title="Command_Results"
          />,
        ],
      },
      pagination: {
        position: ["bottomCenter"],
        showQuickJumper: true,
        size: "default",
        total: filteredData.length,
        defaultPageSize: 10,
        pageSizeOptions: [10, 15, 20, 25],
        showTotal: (total, range) =>
          `${range[0]}-${range[1]} of ${total} items`,
      },
      options: {
        reload: refetch,
        fullScreen: false,
      },
    }),
    [filteredData.length, setInputSearch, refetch]
  );

  return (
    <ProTable
      dataSource={filteredData}
      loading={isFetching}
      columns={columns}
      bordered={false}
      rowKey="index"
      options={tableConfig.options}
      search={false}
      size="small"
      scroll={{ x: 1100 }}
      cardProps={{
        style: {
          boxShadow: token?.Card?.boxShadow,
        },
      }}
      dateFormatter="string"
      toolbar={tableConfig.toolbar}
      pagination={tableConfig.pagination}
      columnsState={{
        persistenceKey: "nms-command-table",
        persistenceType: "localStorage",
      }}
    />
  );
};

export default React.memo(CommandTable);
