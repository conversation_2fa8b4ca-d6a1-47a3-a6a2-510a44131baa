package project

import (
	"fmt"
	"mnms/aiagent/documents"
	"mnms/llm"
	"mnms/llm/ollama"
	"mnms/llm/openai"
	"os"
)

// ProjectEnvironment is an abstract object used to describe the environment of an AI agent project. An environment can:
type ProjectEnvironment struct {
	LLMSettings              *llm.LargeLanguageModel            `json:"llm_settings"`
	RelevantDocumentProvider documents.RelevantDocumentProvider `json:"-"`
	NumberRelevantDocs       int                                `json:"number_relevant_docs,omitempty"`
	RelevantSimilarity       float32                            `json:"relevant_similarity,omitempty"`
	m                        llm.Completer                      `json:"-"`
}

// GetLLMProvider returns the LLM provider.
func (pe *ProjectEnvironment) GetLLMProvider() (llm.Provider, error) {
	var provider llm.Provider
	var err error
	switch pe.LLMSettings.Type {
	case "open-ai":
		// check api key
		apikey := pe.LLMSettings.GetAPIKey()
		if apikey == "" {
			return nil, fmt.Errorf("LLM API key is not set, please set NIMBL_LLM_API_KEY environment variable")
		}
		provider = openai.NewProvider(apikey, "", pe.LLMSettings.Model)
	case "ollama":
		token := os.Getenv("NIMBL_LLM_TOKEN")
		provider, err = ollama.NewProvider(pe.LLMSettings.Model, map[string]interface{}{
			"host":  pe.LLMSettings.Host,
			"token": token,
		})
		if err != nil {
			return nil, err
		}
	default:
		return nil, fmt.Errorf("unsupported LLM type: %s", pe.LLMSettings.Type)
	}
	return provider, nil
}

// NewProjectEnvironmentWithLLMSettings creates a new project environment with the given LLM settings.
func NewProjectEnvironmentWithLLMSettings(llmsetting *llm.LargeLanguageModel) (*ProjectEnvironment, error) {
	err := llmsetting.Valid()
	if err != nil {
		return nil, fmt.Errorf("invalid LLM settings: %w", err)
	}
	relevant := documents.NewLocalRelevantDocuments(llmsetting)

	llmClient, err := llm.NewLLMClient(llmsetting)
	if err != nil {
		return nil, fmt.Errorf("failed to create LLM client: %w", err)
	}
	env := &ProjectEnvironment{
		LLMSettings:              llmsetting,
		RelevantDocumentProvider: relevant,
		//defualt values
		NumberRelevantDocs: 5,
		RelevantSimilarity: 0.75,
		m:                  llmClient,
	}
	return env, nil
}

// SetRelevantDocumentProvider sets the relevant document provider.
func (pe *ProjectEnvironment) SetRelevantDocumentProvider(r documents.RelevantDocumentProvider) {
	pe.RelevantDocumentProvider = r
}

// SetNumberRelevantDocs sets the number of relevant documents to return.
func (pe *ProjectEnvironment) SetNumberRelevantDocs(n int) {
	pe.NumberRelevantDocs = n
}

// SetRelevantSimilarity sets the similarity threshold for relevant documents.
func (pe *ProjectEnvironment) SetRelevantSimilarity(sim float32) {
	pe.RelevantSimilarity = sim
}

// GetNumberRelevantDocs returns the number of relevant documents to return.
func (pe *ProjectEnvironment) GetNumberRelevantDocs() int {
	if pe.NumberRelevantDocs == 0 {
		return 5
	}
	return pe.NumberRelevantDocs
}

// GetRelevantSimilarity returns the similarity threshold for relevant documents.
func (pe *ProjectEnvironment) GetRelevantSimilarity() float32 {
	if pe.RelevantSimilarity == 0 {
		return 0.75
	}
	return pe.RelevantSimilarity
}
