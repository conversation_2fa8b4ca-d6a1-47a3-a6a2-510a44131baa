import { Button, Input, Space, Typography } from "antd";
import React, { useState, useCallback } from "react";
import { useSettingStore } from "../store/setting-store";

const { Text } = Typography;

/**
 * Component for changing the WebSocket URL of the Network Management System
 * @returns {React.ReactElement} Change WebSocket URL form component
 */
const ChangeWSUrl = () => {
  const { wsURL, changeWsURL } = useSettingStore();
  const [inputWsUrl, setInputWsUrl] = useState(wsURL);
  const [error, setError] = useState("");

  const validateWSUrl = useCallback((url) => {
    try {
      const wsUrl = new URL(url);
      return wsUrl.protocol === "ws:" || wsUrl.protocol === "wss:";
    } catch {
      return false;
    }
  }, []);

  const handleChange = (e) => {
    const newUrl = e.target.value;
    setInputWsUrl(newUrl);
    setError("");
  };

  const handleSave = () => {
    if (!inputWsUrl.trim()) {
      setError("WebSocket URL cannot be empty");
      return;
    }

    if (!validateWSUrl(inputWsUrl)) {
      setError("Please enter a valid WebSocket URL (ws:// or wss://)");
      return;
    }

    changeWsURL(inputWsUrl);
  };

  return (
    <Space direction="vertical" size="small" style={{ width: "100%" }}>
      <Space.Compact style={{ width: "100%" }}>
        <Input
          value={inputWsUrl}
          onChange={handleChange}
          placeholder="Enter WebSocket URL"
          status={error ? "error" : ""}
          data-testid="ws-url-input"
        />
        <Button
          type="primary"
          onClick={handleSave}
          data-testid="save-ws-url-button"
        >
          Save
        </Button>
      </Space.Compact>
      {error && (
        <Text type="danger" data-testid="ws-url-error">
          {error}
        </Text>
      )}
    </Space>
  );
};

export default ChangeWSUrl;
