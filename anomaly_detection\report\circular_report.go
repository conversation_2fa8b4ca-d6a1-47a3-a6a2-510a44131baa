package report

import "errors"

// CircularMessages is a cyclic buffer that objjects of ReportMessage
type CircularMessages struct {
	count  int
	size   int
	head   int
	tail   int
	buffer []ReportMessage
}

// NewCircularMessages creates a new CircularMessages
func NewCircularMessages(size int) *CircularMessages {
	return &CircularMessages{
		size:   size,
		buffer: make([]ReportMessage, size),
	}
}

// Add adds a message to the buffer, if overwrite existing data return data
func (cb *CircularMessages) Add(msg ReportMessage) (*ReportMessage, error) {
	pre := cb.buffer[cb.tail]
	cb.buffer[cb.tail] = msg
	cb.tail = (cb.tail + 1) % cb.size
	if cb.count == cb.size {
		// Overwrite the oldest data, move the head forward
		cb.head = (cb.head + 1) % cb.size
		return &pre, nil
	} else {
		cb.count++
	}
	return nil, nil
}

// Update updates a message in the buffer by index relative to the head.
func (cb *CircularMessages) Update(index int, msg ReportMessage) error {
	if index < 0 || index >= cb.count {
		return errors.New("index out of range")
	}
	cb.buffer[(cb.head+index)%cb.size] = msg
	return nil
}

// Get  retrieves an item from the buffer by index relative to the head.
func (cb *CircularMessages) Get(index int) (ReportMessage, error) {
	if index < 0 || index >= cb.count {
		return ReportMessage{}, errors.New("index out of range")
	}
	return cb.buffer[(cb.head+index)%cb.size], nil
}

// GetAll
func (cb *CircularMessages) GetAll() ([]ReportMessage, error) {
	var msgs []ReportMessage
	for i := 0; i < cb.count; i++ {
		msgs = append(msgs, cb.buffer[(cb.head+i)%cb.size])
	}
	return msgs, nil
}

// IsFull checks if the buffer is full
func (cb *CircularMessages) IsFull() bool {
	return cb.count == cb.size
}

// IsEmpty checks if the buffer is empty
func (cb *CircularMessages) IsEmpty() bool {
	return cb.count == 0
}

// Clear clears all messages in the buffer
func (cb *CircularMessages) Clear() {
	cb.count = 0
	cb.head = 0
	cb.tail = 0
}

// Len returns the number of messages in the buffer
func (cb *CircularMessages) Len() int {
	return cb.count
}
