/**
 * Converts command parameters to a JSON string with associated flags
 * @param {string} params - Command parameters as string
 * @param {Object} flags - Configuration flags object
 * @returns {[]} [] representation of commands
 */
export const processCommands = (params, flags) => {
  // Validate input
  if (!params || typeof params !== "string") {
    return "[]";
  }

  // Create flags object with optional chaining
  const flagsObject = {
    client: flags?.cc,
    tag: flags?.ct,
    kind: flags?.ck,
    nooverwrite: flags?.cno,
    nosyslog: flags?.cns,
  };

  // Process commands based on line endings
  const commands = processCommandLines(params);

  // Convert to JSON and return
  return commands.map((cmd) => ({
    command: cmd,
    ...flagsObject,
  }));
};

/**
 * Helper function to process command lines
 * @param {string} params - Command parameters as string
 * @returns {string[]} Array of valid commands
 */
const processCommandLines = (params) => {
  // Handle single line case
  if (params.indexOf("\n") < 0) {
    const trimmedParam = params.trim();
    return !trimmedParam.startsWith("#") ? [trimmedParam] : [];
  }

  // Split by appropriate line ending and process
  const separator = params.includes("\r\n") ? "\r\n" : "\n";
  return params
    .split(separator)
    .map((line) => line.trim())
    .filter((line) => line && !line.startsWith("#"));
};
