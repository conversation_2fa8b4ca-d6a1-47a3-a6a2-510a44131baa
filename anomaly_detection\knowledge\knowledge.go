package knowledge

import (
	"encoding/json"
	"fmt"
	"mnms/anomaly_detection/def"
	"mnms/anomaly_detection/retriever"
	"mnms/anomaly_detection/store"
	"mnms/llm"
)

// Knowledge is a interface for storing and retrieving knowledge (embedding vectors) and metadata.
type Knowledge struct {
	Retriever def.Retriever
	Store     def.Store
	LLM       *llm.LargeLanguageModel
}

// getString returns the string from map[string]interface{} with key
func getString(m map[string]interface{}, key string) (string, error) {
	if val, ok := m[key]; ok {
		if str, ok := val.(string); ok {
			return str, nil
		} else {
			return "", fmt.Errorf("value %v is not a string", val)
		}
	}
	return "", fmt.Errorf("key %v not found", key)
}

type wrapper struct {
	Store []byte `json:"store"`
	LLM   []byte `json:"llm"`
}

// Serialize returns the serialized representation of the Knowledge
func (k *Knowledge) Serialize() ([]byte, error) {
	storeData, err := k.Store.Serialize()
	if err != nil {
		return nil, err
	}
	llmData, err := k.LLM.Serialize()
	if err != nil {
		return nil, err
	}

	w := wrapper{
		Store: storeData,
		LLM:   llmData,
	}
	return json.Marshal(w)

}

// NewKnowledge creates a new instance of Knowledge
func NewKnowledge(llmSettings *llm.LargeLanguageModel, s def.Store) (*Knowledge, error) {
	mod, err := llm.NewLLMClient(llmSettings)
	if err != nil {
		return nil, err
	}
	r := retriever.NewRetriever(s, mod)
	return &Knowledge{
		Retriever: r,
		Store:     s,
		LLM:       llmSettings,
	}, nil
}

// DeserializeKnowledge deserializes the Knowledge from the given byte slice
func DeserializeKnowledge(data []byte) (*Knowledge, error) {
	w := wrapper{}
	err := json.Unmarshal(data, &w)
	if err != nil {
		return nil, err
	}
	s, err := store.DeserializeStore(w.Store)
	if err != nil {
		return nil, err
	}

	llmSettings, err := llm.DeserializeLargeLanguageModel(w.LLM)
	if err != nil {
		return nil, err
	}

	mod, err := llm.NewLLMClient(llmSettings)
	if err != nil {
		return nil, err
	}

	r := retriever.NewRetriever(s, mod)
	return &Knowledge{
		Retriever: r,
		Store:     s,
		LLM:       llmSettings,
	}, nil
}
