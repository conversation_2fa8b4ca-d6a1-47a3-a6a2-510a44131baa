package documents

import (
	"mnms/aiagent/documents/def"
	"sync"
)

// DocumentDescriber is an interface that describes a document
type DocumentDescriber interface {
	GetDescription() string
	GetName() string
}

// Querier is an interface that describes how to fuzzy query a document
// The Query method accepts a string as a full or partial query and returns
// related documents
type Querier interface {
	Query(string) ([]string, error)
}

// Finder is an interface that describes how to find a document
// Find method accepts a string as a unique ID and returns the document
type Finder interface {
	Find(string) (string, error)
}

// Updater is an interface that describes how to update|insert a document
type Updater interface {
	// Update(id, content string) error
	Update(id string, content string) error
}

type Deleter interface {
	Delete(id string) error
}

type Paginatable interface {
	GetPage(page, pageSize int) ([]def.DocumentItem, error)
	TotalCount() int
}

var ExtraDocLock = sync.Mutex{}

// DocumentProvider is an interface that combines the DocumentDescriber, Querier, and Finder interfaces
type DocumentProvider interface {
	DocumentDescriber
	Querier
	Finder
	Deleter
	Updater
	Paginatable
	Serialize() ([]byte, error)
}
