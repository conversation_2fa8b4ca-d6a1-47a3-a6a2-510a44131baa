import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>po<PERSON>,
  theme as antdTheme,
} from "antd";
import { InfoCircleOutlined, SettingOutlined } from "@ant-design/icons";
import packageInfo from "../../package.json";
import Chang<PERSON>aseUR<PERSON> from "./ChangBaseURL";
import ChangeWSUrl from "./ChangeWSUrl";

const { Text } = Typography;

/**
 * Settings component that provides URL configuration and about information
 * for the Network Management System
 */
const SettingsComp = () => {
  const { modal } = App.useApp();
  const { token } = antdTheme.useToken();

  const handleAboutClick = () => {
    modal.info({
      icon: null,
      width: 360,
      className: "settings-about-modal",
      content: <AboutModal packageInfo={packageInfo} token={token} />,
    });
  };

  return (
    <Popover
      placement="bottom"
      title="NIMBL URL Settings"
      content={
        <SettingsContent
          onAboutClick={handleAboutClick}
          packageInfo={packageInfo}
        />
      }
      trigger="click"
      showArrow={false}
      style={{ width: "300px" }}
    >
      <Button
        type="primary"
        aria-label="URL Settings"
        icon={<SettingOutlined />}
        data-testid="settings-button"
      />
    </Popover>
  );
};

export default SettingsComp;

export const SettingsContent = ({ onAboutClick }) => (
  <Flex vertical align="center" gap={4}>
    <Typography.Text>Base URL</Typography.Text>
    <ChangBaseURL />
    <Divider style={{ margin: "8px 0" }} />
    <Typography.Text>WebSocket URL</Typography.Text>
    <ChangeWSUrl />
    <Divider style={{ margin: "8px 0" }} />
    <Button
      type="text"
      icon={<InfoCircleOutlined />}
      onClick={onAboutClick}
      block
      data-testid="about-button"
    >
      About
    </Button>
  </Flex>
);

export const AboutModal = ({ packageInfo, token }) => (
  <Flex align="center" vertical>
    <InfoCircleOutlined
      style={{
        color: token.colorInfo,
        fontSize: 64,
      }}
    />
    <Typography.Title level={4}>
      {packageInfo?.name?.replace(/_/g, " ")}
    </Typography.Title>
    <Text strong>{packageInfo?.version}</Text>
    <Text strong>&#169; 2025 - BlackBear TechHive</Text>
  </Flex>
);
