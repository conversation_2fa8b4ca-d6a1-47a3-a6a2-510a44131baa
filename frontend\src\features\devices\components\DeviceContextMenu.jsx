import React, { useCallback } from "react";
import { Item, Menu } from "react-contexify";
import { createStyles } from "antd-style";
import { Flex, App } from "antd";
import { CloseCircleFilled, ExclamationCircleFilled } from "@ant-design/icons";
import {
  MENU_IDS,
  getDeviceMenuItems,
  massMenuItems,
  mdrMenuItems,
} from "../../../components/context-menu/menu-items";
import { useAuthStore } from "../../../store/auth-store";
import { useDeviceCommands, DEVICE_COMMANDS } from "../hooks/useDeviceCommands";
import { useAppStore } from "../../../store/store";
import DeviceConfirmationModal from "./DeviceConfirmationModal";

const useStyles = createStyles(({ token, css }) => ({
  contexify: css`
    --contexify-menu-bgColor: ${token.colorBgContainer};
    --contexify-separator-color: #4c4c4c;
    --contexify-item-color: ${token.colorText};
    --contexify-activeItem-bgColor: ${token.colorPrimaryActive};
  `,
}));

const CONFIRMATIONS = {
  beep: {
    icon: ExclamationCircleFilled,
    title: "Confirm Beep Device",
    text: "This will let device beep.",
    command: DEVICE_COMMANDS.BEEP,
  },
  reboot: {
    icon: CloseCircleFilled,
    title: "Confirm Reboot Device",
    text: "This will let device reboot.",
    command: DEVICE_COMMANDS.RESET,
  },
  enablesnmp: {
    icon: ExclamationCircleFilled,
    title: "Device SNMP enable",
    text: "This will enable device SNMP.",
    command: DEVICE_COMMANDS.SNMP_ENABLE,
  },
  saveConfig: {
    icon: ExclamationCircleFilled,
    title: "Save Device Config",
    text: "This will save device config.",
    command: DEVICE_COMMANDS.SAVE_CONFIG,
  },
  openwebtunnel: {
    icon: ExclamationCircleFilled,
    title: "Device web tunnel",
    text: "This will add device web tunnel.",
    command: DEVICE_COMMANDS.OPEN_WEB_TUNNEL,
  },
  massReboot: {
    icon: CloseCircleFilled,
    title: "Confirm Mass Reboot Device",
    text: "This will let device reboot.",
    command: DEVICE_COMMANDS.RESET,
  },
  massBeep: {
    icon: ExclamationCircleFilled,
    title: "Confirm Mass Beep Device",
    text: "This will let device beep.",
    command: DEVICE_COMMANDS.BEEP,
  },
  massEnablesnmp: {
    icon: ExclamationCircleFilled,
    title: "Mass Device SNMP enable",
    text: "This will enable device SNMP.",
    command: DEVICE_COMMANDS.SNMP_ENABLE,
  },
  deleteDevice: {
    icon: CloseCircleFilled,
    title: "Confirm Delete Selected Device",
    text: "This will delete selected device.",
    command: DEVICE_COMMANDS.DEVICE_DELETE,
  },
};

const DeviceContextMenu = () => {
  const { styles } = useStyles();
  const { role: userRole } = useAuthStore();
  const { modal } = App.useApp();
  const { openDialogs } = useAppStore();
  const { executeCommand, handleWebOpen, executeMassCommand } =
    useDeviceCommands();

  const deviceItems = React.useMemo(
    () => getDeviceMenuItems(userRole),
    [userRole]
  );

  const handleDeviceCommand = useCallback(
    (type, data) => {
      const config = CONFIRMATIONS[type];
      if (config) {
        modal.confirm({
          icon: null,
          className: "confirm-class",
          width: 400,
          content: (
            <DeviceConfirmationModal
              icon={config.icon}
              title={config.title}
              text={config.text}
            />
          ),
          onOk: () =>
            Array.isArray(data)
              ? executeMassCommand(config.command, data)
              : executeCommand(config.command, data.mac),
          onCancel: () => console.log("Cancel"),
        });
      }
    },
    [modal, executeCommand, executeMassCommand]
  );

  const handleContextMenuItemClick = useCallback(
    ({ id, props: { record, selectedRowKeys } }) => {
      switch (id) {
        case "openweb":
          handleWebOpen(`http://${record.ipaddress}`);
          break;
        case "openwebtunnel":
          if (record.tunneled_url) handleWebOpen(record.tunneled_url);
          else handleDeviceCommand(id, record);
          break;
        case "beep":
        case "reboot":
        case "enablesnmp":
        case "saveConfig":
          handleDeviceCommand(id, record);
          break;
        case "networkSetting":
        case "syslogSetting":
        case "trapSetting":
        case "uploadFirmware":
        case "portInfo":
          openDialogs({
            id,
            data: record,
          });
          break;
        case "massReboot":
        case "massBeep":
        case "massEnablesnmp":
        case "deleteDevice":
          if (selectedRowKeys.length > 0) {
            handleDeviceCommand(id, selectedRowKeys);
          }
          break;
        case "massSyslogSetting":
        case "massTrapSetting":
        case "massUploadFirmware":
          openDialogs({
            id,
            data: selectedRowKeys,
          });
        default:
          break;
      }
    },
    [handleWebOpen, handleDeviceCommand, openDialogs]
  );

  return (
    <div data-testid="device-context-menu">
      <Menu id={MENU_IDS.DEVICE} className={styles.contexify}>
        {deviceItems.map((item) => (
          <Item
            key={item.key}
            id={item.key}
            onClick={handleContextMenuItemClick}
            data-testid={`menu-item-${item.key}`}
          >
            <Flex gap={10}>
              {item.icon}
              {item.label}
            </Flex>
          </Item>
        ))}
      </Menu>

      <Menu id={MENU_IDS.MASS} className={styles.contexify}>
        {massMenuItems.map((item) => (
          <Item
            key={item.key}
            id={item.key}
            onClick={handleContextMenuItemClick}
            data-testid={`menu-item-${item.key}`}
          >
            <Flex gap={10}>
              {item.icon}
              {item.label}
            </Flex>
          </Item>
        ))}
      </Menu>

      <Menu id={MENU_IDS.MDR} className={styles.contexify}>
        {mdrMenuItems.map((item) => (
          <Item
            key={item.key}
            id={item.key}
            onClick={handleContextMenuItemClick}
            data-testid={`menu-item-${item.key}`}
          >
            <Flex gap={10}>{item.label}</Flex>
          </Item>
        ))}
      </Menu>
    </div>
  );
};

export default DeviceContextMenu;
