package main

import (
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"net/url"

	"mnms"
	anmReport "mnms/anomaly_detection/report"
	"mnms/anomaly_detection/statistic"
	"mnms/anomaly_detection/utils"
	"mnms/bbanomsvc/realtime"

	"os"
	"runtime/debug"
	"runtime/pprof"
	"strings"
	"sync"
	"time"

	anm "mnms/anomaly_detection/service"

	"github.com/MakeNowJust/heredoc"
	"github.com/qeof/q"
)

var Version string

// sendReportSyslog send report to root server or remote syslog server
func sendReportSyslog(report *anmReport.Report, err error) {
	if err != nil {
		msg := fmt.Sprintf("Create anomaly report fail: %s", err)
		err := mnms.SendSyslog(mnms.LOG_ERR, "anomaly_report: ", msg)
		if err != nil {
			q.Q(err)
		}
		return
	}
	msg := fmt.Sprintf("Create anomaly report: %s", report.ID)
	err = mnms.SendSyslog(mnms.LOG_INFO, "anomaly_report: ", msg)
	if err != nil {
		q.Q(err)
	}

}

// gethost
func gethost(rawURL string) (string, error) {
	// Add scheme if missing
	if !strings.Contains(rawURL, "://") {
		rawURL = "http://" + rawURL
	}
	parsedURL, err := url.Parse(rawURL)
	if err != nil {
		return "", err
	}
	// extract the hostname (without port and shceme)
	host := parsedURL.Host
	if colonIndex := strings.Index(host, ":"); colonIndex != -1 {
		host = host[:colonIndex]
	}

	return host, nil
}

func printVersion() {
	if Version == "" {
		Version = "Development version"
	}
	// print out version
	fmt.Fprintf(os.Stderr, "BlackBear NIMBL Version: %s\n", Version)
	info, _ := debug.ReadBuildInfo()
	fmt.Fprintln(os.Stderr, "Build information:")
	fmt.Fprintln(os.Stderr, info.GoVersion, info.Main, info.Settings)
}

func main() {
	var wg sync.WaitGroup
	q.O = "stderr"
	q.P = ""

	stop := func() {
		mnms.ClientExit()
		mnms.SyslogExit()
	}

	flagversion := flag.Bool("version", false, "print version")
	flag.StringVar(&mnms.QC.RootURL, "r", "", "root URL")
	flag.StringVar(&mnms.QC.Name, "n", "", "name")
	flag.StringVar(&q.O, "O", "stderr", "debug log output")
	dp := flag.String("P", "", "debug log pattern string")
	noRealtimeDetect := flag.Bool("no-realtime-detect", false, "Disable real time detection")
	bufferSize := flag.Int("buffer-size", 500, "real time detection messages buffer size")
	debuglog := flag.Bool("debuglog", false, "enable debug log, this will override -P to .*")
	flag.StringVar(&mnms.QC.SyslogServerAddr, "ss",
		"", "syslog server address")
	pp := flag.Bool("pprof", false, "enable pprof analysis")
	flag.BoolVar(&mnms.QC.DumpStackTrace, "ds", false, "dump stack trace when exiting with non zero code")
	flag.IntVar(&mnms.QC.CmdInterval, "ic", mnms.QC.CmdInterval, "command processing interval")
	flag.IntVar(&mnms.QC.RegisterInterval, "ir", mnms.QC.RegisterInterval, "Anomaly service registration interval")
	flag.StringVar(&mnms.QC.RemoteSyslogServerAddr, "rs",
		mnms.QC.RemoteSyslogServerAddr, "remote syslog server address")
	var daemon string
	flag.StringVar(&daemon, mnms.DaemonFlag, "", mnms.Usage)
	flag.Parse()
	if *flagversion {
		printVersion()
		mnms.DoExit(0)
	}

	mnms.SendSyslog(mnms.LOG_NOTICE, "bbanomsvc", "Starting service...")
	// check -rs
	if len(mnms.QC.RemoteSyslogServerAddr) == 0 {
		// missing remote syslog addr, realtime alert will missing
		fmt.Fprintln(os.Stderr, "Missing remote syslog server address (-rs), realtime alert will not work on the frontend. To have realtime alert, please set -rs to the bbrootsvc")
	}
	// pprof
	if *pp {
		// cpu profile
		f, err := os.OpenFile("cpu.pprof", os.O_CREATE|os.O_RDWR, 0o644)
		if err != nil {
			q.Q("cpu profile", err)
			mnms.DoExit(1)
		}
		defer f.Close()
		err = pprof.StartCPUProfile(f)
		if err == nil {
			defer pprof.StopCPUProfile()
		}

		// memory profile
		fm, _ := os.OpenFile("mem.pprof", os.O_CREATE|os.O_RDWR, 0o644)

		defer fm.Close()
		pprof.Lookup("heap").WriteTo(fm, 0)

	}

	// version
	if Version != "" {
		mnms.QC.Version = Version
	}

	service := func() {
		mnms.QC.Kind = "anomaly"
		if *debuglog {
			*dp = ".*"
		}

		if *dp == "." {
			fmt.Fprintln(os.Stderr, "error: invalid debug pattern")
			mnms.SendSyslog(mnms.LOG_ERR, "bbanomsvc", "Invalid start option -P, service exit")
			mnms.DoExit(1)
		}
		q.P = *dp

		if mnms.QC.Name == "" {
			fmt.Fprintln(os.Stderr, "error: -n name is required")
			mnms.SendSyslog(mnms.LOG_ERR, "bbanomsvc", "Missing start option -n, service exit")
			mnms.DoExit(1)
		}
		// check reserved name any, all
		if mnms.QC.Name == "any" || mnms.QC.Name == "all" {
			fmt.Fprintln(os.Stderr, "error: name can not be any or all")
			mnms.SendSyslog(mnms.LOG_ERR, "bbanomsvc", "Invalid start option -n, service exit")
			mnms.DoExit(1)
		}

		if mnms.QC.RootURL == "" {
			fmt.Fprintln(os.Stderr, "error: -r root URL is required")
			mnms.SendSyslog(mnms.LOG_ERR, "bbanomsvc", "Missing start option -r, service exit")
			mnms.DoExit(1)
		}

		var err error
		mnms.QC.AdminToken, err = mnms.GetToken("admin")
		if err != nil {
			q.Q(err)
			fmt.Fprintln(os.Stderr, "error: can't get admin token")
			mnms.DoExit(1)
		}
		q.Q(mnms.QC.Name)

		// real-time instance
		ri := realtime.GetInstance()
		port := mnms.QC.SyslogServerAddr
		if port == "" {
			availablePort, err := mnms.GetAvailablePort()
			if err != nil {
				q.Q(err)
			}
			port = fmt.Sprintf(":%d", availablePort)
		}
		ri.InitSyslogServer()

		if !*noRealtimeDetect {
			// update realtime statistic every minute
			wg.Add(1)
			go func() {
				defer wg.Done()
				for {
					time.Sleep(1 * time.Minute)
					err := statistic.LocalStatistic.Realtime.ResetLastMinuteData()
					if err != nil {
						q.Q("update last minute data", err)
					}
				}
			}()

			// realtime messages upload
			wg.Add(1)
			go func() {
				defer wg.Done()
				for {
					time.Sleep(1 * time.Second)
					messages := statistic.GetNonSyncedMessages()
					if len(messages) == 0 {
						continue
					}
					q.Q("upload nonSynced messages", len(messages))

					err = anm.UploadRealtimeMessages(messages)
					if err != nil {
						q.Q("upload nonSynced messages fail: ", err)
					}
					statistic.ClearNonSyncedMessages()
				}
			}()

			statistic.LocalStatistic.Settings.RealTimeDetection.BufferSize = *bufferSize
			// real time detection
			statistic.LocalStatistic.Settings.RealTimeDetection.Enabled = true
			fmt.Fprintln(os.Stderr, "Real time detection enabled")
			ri.Start()
		}

		// monitorreal-time settings changed
		wg.Add(1)
		go func() {
			// real-time settings changed
			defer wg.Done()
			for {
				select {
				case <-statistic.LocalStatistic.Settings.RealTimeDetection.Changed:
					ri := realtime.GetInstance()
					if statistic.LocalStatistic.Settings.RealTimeDetection.Enabled == false {

						q.Q("real time detection disabled")

						ri.Stop()
						continue
					} else {
						q.Q("real time detection enabled")
						ri.Resume()
					}
					q.Q("real time detection settings changed")
					ri.SetMaxMessages(statistic.LocalStatistic.Settings.RealTimeDetection.BufferSize)
				}
			}
		}()

		runDetect := func() {
			wg.Add(1)
			fmt.Fprintf(os.Stderr, "Starting anomaly detection service...\n")
			go func() {
				defer wg.Done()
				for {
					time.Sleep(time.Duration(mnms.QC.CmdInterval) * time.Second) // XXX

					err := mnms.CheckCmds()
					if err != nil {
						q.Q("check anomaly cmds", err)
					}

				}
			}()

			//Upload anomaly statistic periodically
			wg.Add(1)
			go func() {
				defer wg.Done()
				for {
					time.Sleep(time.Duration(mnms.QC.CmdInterval) * time.Second) // XXX

					err := anm.UploadAnomalyStatistic()
					if err != nil {
						q.Q("upload anomaly statistic", err)
					}

				}
			}()
			// Pull syslog
			autodetectFail := func(err error) {
				rep := anmReport.NewErrorReport(
					mnms.QC.Name,
					"auto detect",
					err)
				err = anm.UploadAnomalyReport("Auto detect", rep)
				if err != nil {
					q.Q("upload report fail: ", err)
				}
				sendReportSyslog(rep, err)
			}

			// Get logs from log server
			wg.Add(1)
			go func() {

				defer wg.Done()
				for {
					interval := time.Duration(statistic.LocalStatistic.Settings.PullIntervalMins) * time.Minute
					time.Sleep(interval) // XXX

					if statistic.LocalStatistic.Settings.Detect {
						// post command to pull syslog
						cmd := anm.GenSyslogGenurlCmd(interval)
						syslogSvc, err := GetSyslogSvcName()
						if err != nil || syslogSvc == "" {
							autodetectFail(fmt.Errorf("can't get syslog svc name"))
							continue
						}
						go func() {
							cmdkey, err := PostCommandsToRoot(cmd, syslogSvc)
							if err != nil {
								autodetectFail(err)
								return
							}
							// enqueue cmd
							q.Q("enqueue cmd", cmdkey)
							anm.EnqueuDetectCmd(cmdkey)
						}()

					}

				}
			}()
			// Do auto detect
			wg.Add(1)
			go func() {
				// If syslog didn't provide a file over 30 mins, we will skip the job
				timeout := time.Duration(30) * time.Minute
				defer wg.Done()
				for {

					time.Sleep(time.Duration(mnms.QC.CmdInterval) * time.Second)
					if mnms.QC.RootURL == "" {
						continue
					}
					job := anm.DequeueDetectJob()
					if job != nil {
						// Get cmd result
						ci, err := mnms.GetCmdResult(mnms.QC.RootURL, job.Cmd)
						q.Q("anoamly job queue get ", ci)
						if err != nil || ci == nil {
							q.Q("cmd fail: ", job.Cmd)
							if !job.Timeout(timeout) {
								anm.EnqueueDetectJob(*job)
							} else {
								// report time out
								autodetectFail(fmt.Errorf("cmd %s timeout", job.Cmd))
							}
							continue
						}
						if strings.HasPrefix(ci.Status, "error") {
							q.Q("cmd fail", ci)
							autodetectFail(fmt.Errorf("cmd %s was fail %s", job.Cmd, ci.Status))
							continue
						}
						if ci.Status != "ok" {
							q.Q("Not ready: ", ci)
							if !job.Timeout(timeout) {
								anm.EnqueueDetectJob(*job)
							}
							continue
						}
						q.Q(ci)
						// detect
						score := statistic.LocalStatistic.Settings.Score
						rep := anm.AnalyseURL(ci.Result, score)
						msg := "Auto detect: " + job.StartTime.Format("2006/01/02 15:04:05")
						err = anm.UploadAnomalyReport(msg, rep)
						if err != nil {
							q.Q("upload report fail: ", err)
						}
						sendReportSyslog(rep, err)

					}
				}
			}()
		}

		if mnms.QC.RootURL != "" {
			wg.Add(1)
			q.Q(mnms.QC.RegisterInterval)
			go func() {
				defer wg.Done()
				mnms.RegisterMain()
			}()
		}
		RunAnomalyService(mnms.QC.RootURL, mnms.QC.Name)
		runDetect()

		// automatically check service version
		wg.Add(1)
		go func() {
			defer wg.Done()
			err = mnms.RunAutomaticallyCheckServiceVersion()
			if err != nil {
				q.Q(err)
			}
		}()

		wg.Wait()
		q.Q("exit normally")
		mnms.DoExit(0)
	}
	// enable Daemon
	s, err := mnms.NewDaemon(mnms.QC.Name, os.Args)
	if err != nil {
		fmt.Fprintln(os.Stderr, err)
		mnms.DoExit(1)
	}
	s.RegisterRunEvent(service)
	s.RegisterStopEvent(stop)
	err = s.RunMode(daemon)
	if err != nil {
		fmt.Fprintln(os.Stderr, err)
		mnms.DoExit(1)
	}
}

// RunAnomalyService run anomaly service and do necessary setup
func RunAnomalyService(rooturl string, name string) {
	statistic.LocalStatistic.ClientName = name
	statistic.LocalStatistic.Settings.Root = rooturl

	t, err := mnms.GetToken("admin")
	if err != nil {
		q.Q("get token", err)
	}
	statistic.LocalStatistic.Settings.Token = t

	// Check api key
	if utils.IsDemoAPIKey() {
		msg := fmt.Sprintf(`
		WARNING: bbanomsvc started with demo key, please use your own key in the settings.
		To use your own key, please setup the key in the UI.
		`)
		fmt.Fprintf(os.Stderr, "%s", heredoc.Doc(msg))

	} else {
		if !anm.IsOpenAIKeyWork() {
			msg := fmt.Sprintf(`
			WARNING: bbanomsvc's current OpenAI key is not working, please check the key in the settings.
			Please setup the key in the UI.

			`)
			fmt.Fprintf(os.Stderr, "%s", heredoc.Doc(msg))
		}
	}

	mnms.QC.AnomalySvcEnabled = true
}

// PostCommandsToRoot post command to root server, return a command key
func PostCommandsToRoot(cmd string, client string) (string, error) {
	cmddata := []mnms.CmdInfo{}
	cmdinfo := mnms.CmdInfo{
		Command: cmd,
		Client:  client,
	}
	cmddata = append(cmddata, cmdinfo)
	apiEndpoint := fmt.Sprintf("%s/api/v1/commands", mnms.QC.RootURL)
	josnBytes, err := json.Marshal(cmddata)
	if err != nil {
		return "", err
	}
	res, err := mnms.PostWithToken(apiEndpoint, mnms.QC.AdminToken, bytes.NewBuffer(josnBytes))
	if err != nil {
		return "", err
	}
	if res != nil {
		defer res.Body.Close()
	}
	bodytext, err := io.ReadAll(res.Body)
	if err != nil {
		return "", err
	}
	if res.StatusCode != 200 {
		return "", fmt.Errorf("post command fail, %d, %s", res.StatusCode, bodytext)
	}
	// key := mnms.BuildCmdKey(cmdinfo)
	return cmd, nil
}

// GetSyslogSvcName get syslog service name, if not set return first syslog service name
func GetSyslogSvcName() (string, error) {
	settings := statistic.LocalStatistic.Settings
	if settings.Root == "" {
		q.Q("root url is empty")
		return "", fmt.Errorf("root url is empty")
	}

	if len(statistic.LocalStatistic.Settings.LogSvc) > 0 {
		return statistic.LocalStatistic.Settings.LogSvc, nil
	}

	resp, err := mnms.GetWithToken(settings.Root+"/api/v1/clients", settings.Token)
	if err != nil {
		q.Q("Post anomaly syslogsvc fail: ", err)
		return "", err
	}

	if resp != nil {
		// save close, in resp != nil block
		defer resp.Body.Close()
		if resp.StatusCode != 200 {
			q.Q("Post anomaly syslogsvc fail, ", resp.StatusCode)
			return "", fmt.Errorf("Post anomaly syslogsvc fail, %d", resp.StatusCode)
		}
	}

	clients := map[string]mnms.ClientInfo{}
	err = json.NewDecoder(resp.Body).Decode(&clients)
	if err != nil {
		q.Q("decode syslogsvc fail: ", err)
		return "", err
	}
	// find first syslog svc
	for _, v := range clients {
		if v.Kind == "syslog" {
			return v.Name, nil
		}
	}
	return "", fmt.Errorf("can't find syslog svc")
}
