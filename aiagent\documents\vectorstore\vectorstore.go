package vectorstore

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mnms/llm"
	"runtime"
	"sync"

	"github.com/austinjan/chromem-go"
	"github.com/google/uuid"
)

// GetEmbeddedFunction returns the embedded function
func GetEmbeddedFunction(llmSettings *llm.LargeLanguageModel) chromem.EmbeddingFunc {
	switch llmSettings.Type {
	case "open-ai":
		return chromem.NewEmbeddingFuncOpenAI(llmSettings.GetAPIKey(), chromem.EmbeddingModelOpenAI2Ada)

	case "ollama":
		// baseURL = host+port
		var baseURL string
		if llmSettings.Port != 0 {
			baseURL = fmt.Sprintf("%s:%d", llmSettings.Host, llmSettings.Port)
		} else {
			baseURL = llmSettings.Host
		}
		// add /api
		baseURL += "/api"

		return chromem.NewEmbeddingFuncOllama("mxbai-embed-large", baseURL)
	default:
		return nil
	}
}

var StoreLock = sync.RWMutex{}

type ChromemeStore struct {
	DB          *chromem.DB
	LLMSettings *llm.LargeLanguageModel
}

var localChromemeStore *ChromemeStore

// GetLocalChromemeStore returns the local chromeme store
func GetLocalChromemeStore(llmSettings *llm.LargeLanguageModel) *ChromemeStore {
	if localChromemeStore == nil {
		if llmSettings == nil {
			return nil
		}
		localChromemeStore = NewChromeme(llmSettings)
	}
	return localChromemeStore
}

// NewChromeme returns a new ChromemeStore
func NewChromeme(llmSettings *llm.LargeLanguageModel) *ChromemeStore {
	return &ChromemeStore{
		LLMSettings: llmSettings,
		DB:          chromem.NewDB(),
	}
}

// DeserializeLocalChromemeStore deserializes a ChromemeStore
func DeserializeLocalChromemeStore(reader io.Reader, encryptionKey string) (*ChromemeStore, error) {
	type wrapper struct {
		LLMSettings []byte `json:"llm_settings"`
		DBBlob      []byte `json:"db_blob"`
	}
	w := wrapper{}
	err := json.NewDecoder(reader).Decode(&w)
	if err != nil {
		return nil, err
	}

	llmSettings := &llm.LargeLanguageModel{}
	err = json.Unmarshal(w.LLMSettings, llmSettings)
	if err != nil {
		return nil, err
	}
	buffer := bytes.NewReader(w.DBBlob)
	db := chromem.NewDB()
	err = db.ImportFromReader(buffer, "")
	if err != nil {
		return nil, err
	}
	localChromemeStore = &ChromemeStore{
		LLMSettings: llmSettings,
		DB:          db,
	}
	return localChromemeStore, nil
}

// Serialize serializes the ChromemeStore
func (r *ChromemeStore) Serialize(writer io.Writer) error {
	llmBytes, err := json.Marshal(r.LLMSettings)
	if err != nil {
		return err
	}
	type wrapper struct {
		LLMSettings []byte `json:"llm_settings"`
		DBBlob      []byte `json:"db_blob"`
	}
	dbblob := bytes.Buffer{}
	err = r.DB.ExportToWriter(&dbblob, true, "")
	if err != nil {
		return err
	}
	w := wrapper{
		LLMSettings: llmBytes,
		DBBlob:      dbblob.Bytes(),
	}
	return json.NewEncoder(writer).Encode(w)
}

// AddTextDocument adds a text document to the chromeme store
func (r *ChromemeStore) AddTextDocument(ctx context.Context, category, content, text string) error {
	if r.DB == nil {
		return fmt.Errorf("DB is not initialized")
	}
	embedFunc := GetEmbeddedFunction(r.LLMSettings)
	if embedFunc == nil {
		return fmt.Errorf("embedding function is not initialized")
	}
	c, err := r.DB.GetOrCreateCollection(category, nil, embedFunc)
	if err != nil {
		return err
	}
	doc := chromem.Document{
		ID:       uuid.New().String(),
		Metadata: map[string]string{"text": content},
		Content:  content,
	}
	return c.AddDocument(ctx, doc)
}

// AddDocumentRef adds a document reference to the chromeme store
func (r *ChromemeStore) AddDocumentRef(ctx context.Context, docRef *DocumentRef) error {
	if r.DB == nil {
		return fmt.Errorf("DB is not initialized")
	}
	embedFunc := GetEmbeddedFunction(r.LLMSettings)
	if embedFunc == nil {
		return fmt.Errorf("embedding function is not initialized")
	}
	collectionName := docRef.DocKey + "_ref"
	c, err := r.DB.GetOrCreateCollection(collectionName, nil, embedFunc)
	if err != nil {
		return err
	}
	doc := chromem.Document{
		ID:       uuid.New().String(),
		Metadata: docRef.MapStringString(),
		Content:  docRef.Query,
	}
	return c.AddDocument(ctx, doc)
}

// ListCollections lists all the collections in the chromeme store
func (r *ChromemeStore) ListCollections() []string {
	collectsionMap := r.DB.ListCollections()
	keys := []string{}
	for k := range collectsionMap {
		keys = append(keys, k)
	}
	return keys
}

// AddDocumentText adds a document text to the chromeme store
func (r *ChromemeStore) AddDocumentText(ctx context.Context, category string, docText *DocumentText) error {
	if r.DB == nil {
		return fmt.Errorf("DB is not initialized")
	}
	embedFunc := GetEmbeddedFunction(r.LLMSettings)
	if embedFunc == nil {
		return fmt.Errorf("embedding function is not initialized")
	}
	c, err := r.DB.GetOrCreateCollection(category, nil, embedFunc)
	if err != nil {
		return fmt.Errorf("failed to get or create collection: %w", err)
	}
	doc := chromem.Document{
		ID:       uuid.New().String(),
		Metadata: docText.MapStingString(),
		Content:  docText.Content,
	}
	return c.AddDocument(ctx, doc)
}

// ADDDocumentJSON adds a document JSON to the chromeme store
func (r *ChromemeStore) ADDDocumentJSON(ctx context.Context, category, content string, docJSON map[string]string) error {
	if r.DB == nil {
		return fmt.Errorf("DB is not initialized")
	}
	embedFunc := GetEmbeddedFunction(r.LLMSettings)
	if embedFunc == nil {
		return fmt.Errorf("embedding function is not initialized")
	}
	c, err := r.DB.GetOrCreateCollection(category, nil, embedFunc)
	if err != nil {
		return fmt.Errorf("failed to get or create collection: %w", err)
	}
	doc := chromem.Document{
		ID:       uuid.New().String(),
		Metadata: docJSON,
		Content:  content,
	}
	return c.AddDocument(ctx, doc)
}

// AddDocumentRefs adds a list of document references to the chromeme store
func (r *ChromemeStore) AddDocumentRefs(ctx context.Context, docRefs []*DocumentRef) error {
	if r.DB == nil {
		return fmt.Errorf("DB is not initialized")
	}
	embedFunc := GetEmbeddedFunction(r.LLMSettings)
	if embedFunc == nil {
		return fmt.Errorf("embedding function is not initialized")
	}
	if len(docRefs) == 0 {
		return fmt.Errorf("no document references")
	}
	docs := []chromem.Document{}
	for _, docRef := range docRefs {
		doc := chromem.Document{
			ID:       uuid.New().String(),
			Metadata: docRef.MapStringString(),
			Content:  docRef.Query,
		}
		docs = append(docs, doc)
	}
	collectioName := docRefs[0].DocKey + "_ref"
	c, err := r.DB.GetOrCreateCollection(collectioName, nil, embedFunc)
	if err != nil {
		return err
	}
	return c.AddDocuments(ctx, docs, runtime.NumCPU())
}

type ReferenceCollection struct {
	Collection *chromem.Collection
}

// Count returns the number of documents in the collection
func (r *ReferenceCollection) Count() int {
	return r.Collection.Count()
}

func (r *ReferenceCollection) DeleteReferences(ctx context.Context, ids []string) error {
	return r.Collection.Delete(ctx, nil, nil, ids...)
}

// Query serch the most similar result
func (r *ReferenceCollection) Query(ctx context.Context, query string) (chromem.Result, error) {
	nResult, err := r.Collection.Query(ctx, query, 1, nil, nil)
	if err != nil {
		return chromem.Result{}, err
	}
	if len(nResult) == 0 {
		return chromem.Result{}, fmt.Errorf("no results found")
	}
	// return most similar result
	var mostSimilar chromem.Result
	for _, result := range nResult {
		if result.Similarity > mostSimilar.Similarity {
			mostSimilar = result
		}
	}
	return mostSimilar, nil
}

// QueryWithNResult search most n similar results
func (r *ReferenceCollection) QueryWithNResult(ctx context.Context, query string, nResults int) ([]chromem.Result, error) {
	return r.Collection.Query(ctx, query, nResults, nil, nil)
}

type QueryOptions struct {
	Query   string            `json:"query"`
	NResult int               `json:"n_result"`
	Where   map[string]string `json:"where"`
}

// QueryWithOption search most n similar results with option
// options:
// QueryText string
// QueryEmbedding []float32 if both QueryText and QueryEmbedding are provided, QueryEmbedding will be used.
// NResults int number of results to return
// Where map[string]string conditional filtering on metadata
// Navigate bool if true, the query will be navigated
func (r *ReferenceCollection) QueryWithOption(ctx context.Context, options QueryOptions) ([]chromem.Result, error) {
	chromemOptions := chromem.QueryOptions{
		QueryText: options.Query,
		NResults:  options.NResult,
		Where:     options.Where,
	}
	return r.Collection.QueryWithOptions(ctx, chromemOptions)
}

// GetDocuments returns the documents in the collection
func (r *ReferenceCollection) GetDocuments() map[string]chromem.Document {
	return r.Collection.GetDocuments()
}

// GetDocumentWithFilter returns the documents in the collection with a filter
func (r *ReferenceCollection) GetDocumentWithFilter(filter map[string]string) map[string]chromem.Document {
	return r.Collection.GetDocumentsWithFilter(filter)
}

// GetCollection returns a collection
func (r *ChromemeStore) GetCollection(collectionName string) (*chromem.Collection, error) {
	c := r.DB.GetCollection(collectionName, GetEmbeddedFunction(r.LLMSettings))
	if c == nil {
		return nil, fmt.Errorf("collection not found")
	}
	return c, nil
}

// GetReference returns a new resolver
func (r *ChromemeStore) GetReference(docName string) (*ReferenceCollection, error) {
	collectionName := docName + "_ref"
	c := r.DB.GetCollection(collectionName, GetEmbeddedFunction(r.LLMSettings))
	if c == nil {
		return nil, fmt.Errorf("collection not found")
	}
	return &ReferenceCollection{
		Collection: c,
	}, nil
}

// SetLLMSettings sets the LLM settings
func (store *ChromemeStore) SetLLMSettings(llmSettings *llm.LargeLanguageModel) {
	store.LLMSettings = llmSettings
}
