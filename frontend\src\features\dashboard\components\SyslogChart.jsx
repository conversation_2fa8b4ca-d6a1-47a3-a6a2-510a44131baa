import React, { useMemo, memo } from "react";
import { Card, Spin, theme } from "antd";
import ReactApex<PERSON>hart from "react-apexcharts";
import { useTheme } from "antd-style";

/**
 * Component to display syslog count chart for the last 7 days
 * @param {Object} props Component props
 * @param {Array} props.data Syslog data for the chart
 * @param {boolean} props.loading Loading state
 * @returns {JSX.Element} SyslogChart component
 */
const SyslogChart = ({ data = [], loading = false }) => {
  const { appearance } = useTheme();
  const { token } = theme.useToken();

  const chartOptions = useMemo(() => {
    const dates = data.map((item) => item.date);
    const counts = data.map((item) => item.count);

    return {
      series: [
        {
          name: "Syslog Count",
          data: counts,
        },
      ],
      options: {
        chart: {
          type: "bar",
          height: 350,
          toolbar: {
            show: false,
          },
          background: "transparent",
        },
        theme: {
          mode: appearance === "dark" ? "dark" : "light",
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: "55%",
            borderRadius: 4,
          },
        },
        dataLabels: {
          enabled: false,
        },
        stroke: {
          show: true,
          width: 2,
          colors: ["transparent"],
        },
        colors: [token.colorPrimary],
        xaxis: {
          categories: dates,
          title: {
            text: "Date",
          },
        },
        yaxis: {
          title: {
            text: "Count",
          },
        },
        fill: {
          opacity: 1,
        },
        tooltip: {
          y: {
            formatter: function (val) {
              return val + " logs";
            },
          },
        },
        title: {
          text: "Syslog Count - Last 7 Days",
          align: "left",
        },
      },
    };
  }, [data, appearance, token]);

  return (
    <Card variant="borderless">
      <Spin spinning={loading}>
        {data.length > 0 ? (
          <ReactApexChart
            options={chartOptions.options}
            series={chartOptions.series}
            type="bar"
            height={350}
          />
        ) : (
          <div
            style={{
              height: 350,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            No syslog data available
          </div>
        )}
      </Spin>
    </Card>
  );
};

export default memo(SyslogChart);
