import React, { memo } from 'react';
import { Card, Button, Input, Space, Upload } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { useScript } from '../hooks/useScript';

/**
 * Script Form component for entering and running commands
 * @returns {JSX.Element} Script Form component
 */
const ScriptForm = () => {
  const { 
    inputCmd, 
    setInputCmd, 
    clearInputCmd, 
    handleRunCommand, 
    getUploadProps 
  } = useScript();
  
  const uploadProps = getUploadProps();

  return (
    <Card
      title="Request Script"
      variant="borderless"
      extra={
        <Space>
          <Upload {...uploadProps}>
            <Button type="primary" icon={<UploadOutlined />}>
              Upload CMDS Script
            </Button>
          </Upload>
          <Button type="primary" onClick={handleRunCommand}>
            Run Command
          </Button>
        </Space>
      }
    >
      <Input.TextArea
        rows={12}
        value={inputCmd}
        onChange={(e) => setInputCmd(e.target.value)}
        placeholder="Enter commands here, one per line"
      />
      <Button 
        style={{ marginTop: 8 }} 
        onClick={clearInputCmd}
        block
      >
        Clear Command Input
      </Button>
    </Card>
  );
};

export default memo(ScriptForm);
