package knowledge

import (
	"context"
	"fmt"
	"mnms/anomaly_detection/store"
	"mnms/llm"
	"testing"
)

// TestSerialization tests the serialization and deserialization of the Knowledge
func TestSerialization(t *testing.T) {

	gpt3 := llm.GPTSettings()
	s := store.NewStandardStore()
	k, err := NewKnowledge(gpt3, s)
	if err != nil {
		t.Fatal(err)
	}

	for i := 0; i < 10; i++ {
		content := fmt.Sprintf("ID: %d", i*10000+568*i)
		fakeData := map[string]interface{}{
			"text": content,
			"data": fmt.Sprintf("data-%d.%d.%d", i, i, i),
		}

		id, err := k.Retriever.AddWithThreshold(context.Background(), content, fakeData, 0.1)
		if err != nil {
			t.Fatal(err)
		}
		t.Log("added: ", id, fakeData)

	}

	// check data

	docs, err := k.Retriever.GetReleventDocumentsWithThreshold(context.Background(), "ID: 0", 5, 0.1)
	if err != nil {
		t.Fatal(err)
	}
	if len(docs) != 1 {
		t.<PERSON><PERSON>rf("Unexpected number of documents: got %v, want %v", len(docs), 1)
	}

	serializedData, err := k.Serialize()
	if err != nil {
		t.Fatal(err)
	}

	k2, err := DeserializeKnowledge(serializedData)
	if err != nil {
		t.Fatal(err)
	}

	docs, err = k2.Retriever.GetReleventDocumentsWithThreshold(context.Background(), "ID: 0", 5, 0.1)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(docs)

}
