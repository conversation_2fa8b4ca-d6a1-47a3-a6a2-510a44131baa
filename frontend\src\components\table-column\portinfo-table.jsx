import { CheckCircleOutlined, StopOutlined } from "@ant-design/icons";
import { useMemo } from "react";

export const usePortInfoColumns = (token) => {
  return useMemo(
    () => [
      {
        title: "PortName",
        dataIndex: "portName",
        width: 100,
        key: "portName",
        fixed: "left",
      },
      {
        title: "PortStatus",
        width: 100,
        dataIndex: "portStatus",
        key: "portStatus",
        render: (data, _) => (data ? "Up" : "Down"),
      },
      {
        title: "Speed",
        width: 100,
        dataIndex: "speed",
        key: "speed",
      },
      {
        title: "PortMode",
        width: 100,
        dataIndex: "portMode",
        key: "portMode",
      },
      {
        title: "InOctets",
        width: 150,
        dataIndex: "inOctets",
        key: "inOctets",
      },
      {
        title: "InErrors",
        width: 150,
        dataIndex: "inErrors",
        key: "inErrors",
      },
      {
        title: "InUcastPkts",
        width: 150,
        dataIndex: "inUcastPkts",
        key: "inUcastPkts",
      },
      {
        title: "InMulticastPkts",
        width: 150,
        dataIndex: "inMulticastPkts",
        key: "inMulticastPkts",
      },
      {
        title: "InBroadcastPkts",
        width: 150,
        dataIndex: "inBroadcastPkts",
        key: "inBroadcastPkts",
      },
      {
        title: "OutOctets",
        width: 150,
        dataIndex: "outOctets",
        key: "outOctets",
      },
      {
        title: "OutErrors",
        width: 150,
        dataIndex: "outErrors",
        key: "outErrors",
      },
      {
        title: "OutUcastPkts",
        width: 150,
        dataIndex: "outUcastPkts",
        key: "outUcastPkts",
      },
      {
        title: "OutMulticastPkts",
        width: 150,
        dataIndex: "outMulticastPkts",
        key: "outMulticastPkts",
      },
      {
        title: "OutBroadcastPkts",
        dataIndex: "outBroadcastPkts",
        width: 150,
        key: "outBroadcastPkts",
      },
      {
        title: "Status",
        width: 120,
        fixed: "right",
        key: "enableStatus",
        align: "center",
        render: (text, record) => (
          <>
            {record.enableStatus ? (
              <CheckCircleOutlined style={{ color: token.colorSuccess }} />
            ) : (
              <StopOutlined style={{ color: token.colorError }} />
            )}
          </>
        ),
      },
    ],
    [token]
  );
};
