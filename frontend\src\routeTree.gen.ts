/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as LoginImport } from './routes/login'
import { Route as AuthImport } from './routes/_auth'
import { Route as AuthIndexImport } from './routes/_auth/index'
import { Route as AuthUsersManagementImport } from './routes/_auth/users-management'
import { Route as AuthTunnelsImport } from './routes/_auth/tunnels'
import { Route as AuthTopologyImport } from './routes/_auth/topology'
import { Route as AuthScriptImport } from './routes/_auth/script'
import { Route as AuthMibBrowserImport } from './routes/_auth/mib-browser'
import { Route as AuthLogsImport } from './routes/_auth/logs'
import { Route as AuthKeyStoreImport } from './routes/_auth/key-store'
import { Route as AuthDevicesImport } from './routes/_auth/devices'
import { Route as AuthClusterinfoImport } from './routes/_auth/clusterinfo'
import { Route as AuthDashboardIndexImport } from './routes/_auth/dashboard/index'
import { Route as AuthDashboardIdpsImport } from './routes/_auth/dashboard/idps'
import { Route as AuthDashboardDeviceImport } from './routes/_auth/dashboard/device'

// Create/Update Routes

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const AuthRoute = AuthImport.update({
  id: '/_auth',
  getParentRoute: () => rootRoute,
} as any)

const AuthIndexRoute = AuthIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthRoute,
} as any)

const AuthUsersManagementRoute = AuthUsersManagementImport.update({
  id: '/users-management',
  path: '/users-management',
  getParentRoute: () => AuthRoute,
} as any)

const AuthTunnelsRoute = AuthTunnelsImport.update({
  id: '/tunnels',
  path: '/tunnels',
  getParentRoute: () => AuthRoute,
} as any)

const AuthTopologyRoute = AuthTopologyImport.update({
  id: '/topology',
  path: '/topology',
  getParentRoute: () => AuthRoute,
} as any)

const AuthScriptRoute = AuthScriptImport.update({
  id: '/script',
  path: '/script',
  getParentRoute: () => AuthRoute,
} as any)

const AuthMibBrowserRoute = AuthMibBrowserImport.update({
  id: '/mib-browser',
  path: '/mib-browser',
  getParentRoute: () => AuthRoute,
} as any)

const AuthLogsRoute = AuthLogsImport.update({
  id: '/logs',
  path: '/logs',
  getParentRoute: () => AuthRoute,
} as any)

const AuthKeyStoreRoute = AuthKeyStoreImport.update({
  id: '/key-store',
  path: '/key-store',
  getParentRoute: () => AuthRoute,
} as any)

const AuthDevicesRoute = AuthDevicesImport.update({
  id: '/devices',
  path: '/devices',
  getParentRoute: () => AuthRoute,
} as any)

const AuthClusterinfoRoute = AuthClusterinfoImport.update({
  id: '/clusterinfo',
  path: '/clusterinfo',
  getParentRoute: () => AuthRoute,
} as any)

const AuthDashboardIndexRoute = AuthDashboardIndexImport.update({
  id: '/dashboard/',
  path: '/dashboard/',
  getParentRoute: () => AuthRoute,
} as any)

const AuthDashboardIdpsRoute = AuthDashboardIdpsImport.update({
  id: '/dashboard/idps',
  path: '/dashboard/idps',
  getParentRoute: () => AuthRoute,
} as any)

const AuthDashboardDeviceRoute = AuthDashboardDeviceImport.update({
  id: '/dashboard/device',
  path: '/dashboard/device',
  getParentRoute: () => AuthRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_auth': {
      id: '/_auth'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/_auth/clusterinfo': {
      id: '/_auth/clusterinfo'
      path: '/clusterinfo'
      fullPath: '/clusterinfo'
      preLoaderRoute: typeof AuthClusterinfoImport
      parentRoute: typeof AuthImport
    }
    '/_auth/devices': {
      id: '/_auth/devices'
      path: '/devices'
      fullPath: '/devices'
      preLoaderRoute: typeof AuthDevicesImport
      parentRoute: typeof AuthImport
    }
    '/_auth/key-store': {
      id: '/_auth/key-store'
      path: '/key-store'
      fullPath: '/key-store'
      preLoaderRoute: typeof AuthKeyStoreImport
      parentRoute: typeof AuthImport
    }
    '/_auth/logs': {
      id: '/_auth/logs'
      path: '/logs'
      fullPath: '/logs'
      preLoaderRoute: typeof AuthLogsImport
      parentRoute: typeof AuthImport
    }
    '/_auth/mib-browser': {
      id: '/_auth/mib-browser'
      path: '/mib-browser'
      fullPath: '/mib-browser'
      preLoaderRoute: typeof AuthMibBrowserImport
      parentRoute: typeof AuthImport
    }
    '/_auth/script': {
      id: '/_auth/script'
      path: '/script'
      fullPath: '/script'
      preLoaderRoute: typeof AuthScriptImport
      parentRoute: typeof AuthImport
    }
    '/_auth/topology': {
      id: '/_auth/topology'
      path: '/topology'
      fullPath: '/topology'
      preLoaderRoute: typeof AuthTopologyImport
      parentRoute: typeof AuthImport
    }
    '/_auth/tunnels': {
      id: '/_auth/tunnels'
      path: '/tunnels'
      fullPath: '/tunnels'
      preLoaderRoute: typeof AuthTunnelsImport
      parentRoute: typeof AuthImport
    }
    '/_auth/users-management': {
      id: '/_auth/users-management'
      path: '/users-management'
      fullPath: '/users-management'
      preLoaderRoute: typeof AuthUsersManagementImport
      parentRoute: typeof AuthImport
    }
    '/_auth/': {
      id: '/_auth/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthIndexImport
      parentRoute: typeof AuthImport
    }
    '/_auth/dashboard/device': {
      id: '/_auth/dashboard/device'
      path: '/dashboard/device'
      fullPath: '/dashboard/device'
      preLoaderRoute: typeof AuthDashboardDeviceImport
      parentRoute: typeof AuthImport
    }
    '/_auth/dashboard/idps': {
      id: '/_auth/dashboard/idps'
      path: '/dashboard/idps'
      fullPath: '/dashboard/idps'
      preLoaderRoute: typeof AuthDashboardIdpsImport
      parentRoute: typeof AuthImport
    }
    '/_auth/dashboard/': {
      id: '/_auth/dashboard/'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof AuthDashboardIndexImport
      parentRoute: typeof AuthImport
    }
  }
}

// Create and export the route tree

interface AuthRouteChildren {
  AuthClusterinfoRoute: typeof AuthClusterinfoRoute
  AuthDevicesRoute: typeof AuthDevicesRoute
  AuthKeyStoreRoute: typeof AuthKeyStoreRoute
  AuthLogsRoute: typeof AuthLogsRoute
  AuthMibBrowserRoute: typeof AuthMibBrowserRoute
  AuthScriptRoute: typeof AuthScriptRoute
  AuthTopologyRoute: typeof AuthTopologyRoute
  AuthTunnelsRoute: typeof AuthTunnelsRoute
  AuthUsersManagementRoute: typeof AuthUsersManagementRoute
  AuthIndexRoute: typeof AuthIndexRoute
  AuthDashboardDeviceRoute: typeof AuthDashboardDeviceRoute
  AuthDashboardIdpsRoute: typeof AuthDashboardIdpsRoute
  AuthDashboardIndexRoute: typeof AuthDashboardIndexRoute
}

const AuthRouteChildren: AuthRouteChildren = {
  AuthClusterinfoRoute: AuthClusterinfoRoute,
  AuthDevicesRoute: AuthDevicesRoute,
  AuthKeyStoreRoute: AuthKeyStoreRoute,
  AuthLogsRoute: AuthLogsRoute,
  AuthMibBrowserRoute: AuthMibBrowserRoute,
  AuthScriptRoute: AuthScriptRoute,
  AuthTopologyRoute: AuthTopologyRoute,
  AuthTunnelsRoute: AuthTunnelsRoute,
  AuthUsersManagementRoute: AuthUsersManagementRoute,
  AuthIndexRoute: AuthIndexRoute,
  AuthDashboardDeviceRoute: AuthDashboardDeviceRoute,
  AuthDashboardIdpsRoute: AuthDashboardIdpsRoute,
  AuthDashboardIndexRoute: AuthDashboardIndexRoute,
}

const AuthRouteWithChildren = AuthRoute._addFileChildren(AuthRouteChildren)

export interface FileRoutesByFullPath {
  '': typeof AuthRouteWithChildren
  '/login': typeof LoginRoute
  '/clusterinfo': typeof AuthClusterinfoRoute
  '/devices': typeof AuthDevicesRoute
  '/key-store': typeof AuthKeyStoreRoute
  '/logs': typeof AuthLogsRoute
  '/mib-browser': typeof AuthMibBrowserRoute
  '/script': typeof AuthScriptRoute
  '/topology': typeof AuthTopologyRoute
  '/tunnels': typeof AuthTunnelsRoute
  '/users-management': typeof AuthUsersManagementRoute
  '/': typeof AuthIndexRoute
  '/dashboard/device': typeof AuthDashboardDeviceRoute
  '/dashboard/idps': typeof AuthDashboardIdpsRoute
  '/dashboard': typeof AuthDashboardIndexRoute
}

export interface FileRoutesByTo {
  '/login': typeof LoginRoute
  '/clusterinfo': typeof AuthClusterinfoRoute
  '/devices': typeof AuthDevicesRoute
  '/key-store': typeof AuthKeyStoreRoute
  '/logs': typeof AuthLogsRoute
  '/mib-browser': typeof AuthMibBrowserRoute
  '/script': typeof AuthScriptRoute
  '/topology': typeof AuthTopologyRoute
  '/tunnels': typeof AuthTunnelsRoute
  '/users-management': typeof AuthUsersManagementRoute
  '/': typeof AuthIndexRoute
  '/dashboard/device': typeof AuthDashboardDeviceRoute
  '/dashboard/idps': typeof AuthDashboardIdpsRoute
  '/dashboard': typeof AuthDashboardIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/_auth': typeof AuthRouteWithChildren
  '/login': typeof LoginRoute
  '/_auth/clusterinfo': typeof AuthClusterinfoRoute
  '/_auth/devices': typeof AuthDevicesRoute
  '/_auth/key-store': typeof AuthKeyStoreRoute
  '/_auth/logs': typeof AuthLogsRoute
  '/_auth/mib-browser': typeof AuthMibBrowserRoute
  '/_auth/script': typeof AuthScriptRoute
  '/_auth/topology': typeof AuthTopologyRoute
  '/_auth/tunnels': typeof AuthTunnelsRoute
  '/_auth/users-management': typeof AuthUsersManagementRoute
  '/_auth/': typeof AuthIndexRoute
  '/_auth/dashboard/device': typeof AuthDashboardDeviceRoute
  '/_auth/dashboard/idps': typeof AuthDashboardIdpsRoute
  '/_auth/dashboard/': typeof AuthDashboardIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | ''
    | '/login'
    | '/clusterinfo'
    | '/devices'
    | '/key-store'
    | '/logs'
    | '/mib-browser'
    | '/script'
    | '/topology'
    | '/tunnels'
    | '/users-management'
    | '/'
    | '/dashboard/device'
    | '/dashboard/idps'
    | '/dashboard'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/login'
    | '/clusterinfo'
    | '/devices'
    | '/key-store'
    | '/logs'
    | '/mib-browser'
    | '/script'
    | '/topology'
    | '/tunnels'
    | '/users-management'
    | '/'
    | '/dashboard/device'
    | '/dashboard/idps'
    | '/dashboard'
  id:
    | '__root__'
    | '/_auth'
    | '/login'
    | '/_auth/clusterinfo'
    | '/_auth/devices'
    | '/_auth/key-store'
    | '/_auth/logs'
    | '/_auth/mib-browser'
    | '/_auth/script'
    | '/_auth/topology'
    | '/_auth/tunnels'
    | '/_auth/users-management'
    | '/_auth/'
    | '/_auth/dashboard/device'
    | '/_auth/dashboard/idps'
    | '/_auth/dashboard/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  AuthRoute: typeof AuthRouteWithChildren
  LoginRoute: typeof LoginRoute
}

const rootRouteChildren: RootRouteChildren = {
  AuthRoute: AuthRouteWithChildren,
  LoginRoute: LoginRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.jsx",
      "children": [
        "/_auth",
        "/login"
      ]
    },
    "/_auth": {
      "filePath": "_auth.jsx",
      "children": [
        "/_auth/clusterinfo",
        "/_auth/devices",
        "/_auth/key-store",
        "/_auth/logs",
        "/_auth/mib-browser",
        "/_auth/script",
        "/_auth/topology",
        "/_auth/tunnels",
        "/_auth/users-management",
        "/_auth/",
        "/_auth/dashboard/device",
        "/_auth/dashboard/idps",
        "/_auth/dashboard/"
      ]
    },
    "/login": {
      "filePath": "login.jsx"
    },
    "/_auth/clusterinfo": {
      "filePath": "_auth/clusterinfo.jsx",
      "parent": "/_auth"
    },
    "/_auth/devices": {
      "filePath": "_auth/devices.jsx",
      "parent": "/_auth"
    },
    "/_auth/key-store": {
      "filePath": "_auth/key-store.jsx",
      "parent": "/_auth"
    },
    "/_auth/logs": {
      "filePath": "_auth/logs.jsx",
      "parent": "/_auth"
    },
    "/_auth/mib-browser": {
      "filePath": "_auth/mib-browser.jsx",
      "parent": "/_auth"
    },
    "/_auth/script": {
      "filePath": "_auth/script.jsx",
      "parent": "/_auth"
    },
    "/_auth/topology": {
      "filePath": "_auth/topology.jsx",
      "parent": "/_auth"
    },
    "/_auth/tunnels": {
      "filePath": "_auth/tunnels.jsx",
      "parent": "/_auth"
    },
    "/_auth/users-management": {
      "filePath": "_auth/users-management.jsx",
      "parent": "/_auth"
    },
    "/_auth/": {
      "filePath": "_auth/index.jsx",
      "parent": "/_auth"
    },
    "/_auth/dashboard/device": {
      "filePath": "_auth/dashboard/device.jsx",
      "parent": "/_auth"
    },
    "/_auth/dashboard/idps": {
      "filePath": "_auth/dashboard/idps.jsx",
      "parent": "/_auth"
    },
    "/_auth/dashboard/": {
      "filePath": "_auth/dashboard/index.jsx",
      "parent": "/_auth"
    }
  }
}
ROUTE_MANIFEST_END */
