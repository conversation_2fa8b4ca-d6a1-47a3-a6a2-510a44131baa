package mnms

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
	"github.com/mitchellh/mapstructure"
	"github.com/qeof/q"
)

// Function metadata for the MCP functions endpoint
type MCPToolMetadata struct {
	Name        string              `json:"name"`
	Description string              `json:"description"`
	Parameters  mcp.ToolInputSchema `json:"parameters"`
}

type ConfigNetworkRequest struct {
	MAC      string `json:"mac" mapstructure:"mac" validate:"required,mac"`
	IP       string `json:"ip" mapstructure:"ip" validate:"required,ip"`
	NewIP    string `json:"new_ip" mapstructure:"new_ip" validate:"required,ip"`
	Subnet   string `json:"subnet" mapstructure:"subnet" validate:"required,cidr"`
	Gateway  string `json:"gateway" mapstructure:"gateway" validate:"required,ip"`
	Hostname string `json:"hostname" mapstructure:"hostname" validate:"required"`
	DHCP     bool   `json:"dhcp" mapstructure:"dhcp"`
}

type ConfigSyslogRequest struct {
	MAC        string `json:"mac" mapstructure:"mac" validate:"required,mac"`
	Enable     bool   `json:"enable" mapstructure:"enable"`
	ServerIP   string `json:"server_ip" mapstructure:"server_ip" validate:"required,ip"`
	ServerPort int    `json:"server_port" mapstructure:"server_port" validate:"required"`
	Level      string `json:"level" mapstructure:"level"`
	LogToFlash bool   `json:"log_to_flash" mapstructure:"log_to_flash"`
}

type GenerateLogRequest struct {
	Facility int    `json:"facility" mapstructure:"facility" validate:"required"`
	Severity int    `json:"severity" mapstructure:"severity" validate:"required"`
	Tag      string `json:"tag" mapstructure:"tag" validate:"required"`
	Message  string `json:"message" mapstructure:"message" validate:"required"`
}

// NewMCPServer creates a new MCP server with the supported commands
// refer to startMCPTestServer in mcp_test.go to see how to use this function
func NewMCPServer() (*server.MCPServer, error) {
	mcpServer := server.NewMCPServer(
		"nimbl-mcp-server",
		"1.0.0",
	)

	tools, err := SupportedMCPTools()
	if err != nil {
		q.Q(err)
		return nil, err
	}

	for _, t := range tools {
		jsonBytes, err := json.Marshal(t.Parameters)
		if err != nil {
			q.Q(err)
			return nil, err
		}
		mcpServer.AddTool(mcp.NewToolWithRawSchema(t.Name, t.Description, json.RawMessage(jsonBytes)), handleToolRequest)
	}

	return mcpServer, nil
}

// SupportedMCPTools returns a list of supported commands
func SupportedMCPTools() ([]MCPToolMetadata, error) {
	// normally data should be passed with addResource, but tool can work with it

	tools := []MCPToolMetadata{
		{
			Name:        "beep_device",
			Description: "Make the device beep",
			Parameters: mcp.ToolInputSchema{
				Properties: map[string]any{
					"mac": map[string]any{
						"type":        "string",
						"description": "The MAC address of the device to beep",
					},
				},
				Required: []string{"mac"},
				Type:     "object",
			},
		},
		{
			Name:        "reset_device",
			Description: "Reset or reboot the device",
			Parameters: mcp.ToolInputSchema{
				Properties: map[string]any{
					"mac": map[string]any{
						"type":        "string",
						"description": "The MAC address of the device to reset",
					},
				},
				Required: []string{"mac"},
				Type:     "object",
			},
		},
		{
			Name:        "config_device_network",
			Description: "Configure the device's network settings",
			Parameters: mcp.ToolInputSchema{
				Properties: map[string]any{
					"mac": map[string]any{
						"type":        "string",
						"description": "The MAC address of the device to configure",
					},
					"ip": map[string]any{
						"type":        "string",
						"description": "The current IP address of the device",
					},
					"new_ip": map[string]any{
						"type":        "string",
						"description": "The new IP address to assign to the device",
					},
					"subnet": map[string]any{
						"type":        "string",
						"description": "The subnet mask for the device",
					},
					"gateway": map[string]any{
						"type":        "string",
						"description": "The gateway IP address for the device",
					},
					"hostname": map[string]any{
						"type":        "string",
						"description": "The hostname to assign to the device",
					},
					"dhcp": map[string]any{
						"type":        "boolean",
						"description": "Whether to use DHCP to configure the device",
					},
				},
				Required: []string{"mac", "ip", "new_ip", "subnet", "gateway", "hostname", "dhcp"},
				Type:     "object",
			},
		},
		{
			Name:        "update_device_firmware",
			Description: "Update the device's firmware",
			Parameters: mcp.ToolInputSchema{
				Properties: map[string]any{
					"mac": map[string]any{
						"type":        "string",
						"description": "The MAC address of the device to beep",
					},
					"file_url": map[string]any{
						"type":        "string",
						"description": "The URL of the firmware file to download and install",
					},
				},
				Required: []string{"mac", "file_url"},
				Type:     "object",
			},
		},
		{
			Name:        "config_device_syslog",
			Description: "Configure the device's syslog settings",
			Parameters: mcp.ToolInputSchema{
				Properties: map[string]any{
					"mac": map[string]any{
						"type":        "string",
						"description": "The MAC address of the device to configure",
					},
					"enable": map[string]any{
						"type":        "boolean",
						"description": "Whether to enable or disable syslog",
					},
					"server_ip": map[string]any{
						"type":        "string",
						"description": "The IP address of the syslog server",
					},
					"server_port": map[string]any{
						"type":        "integer",
						"description": "The port number of the syslog server",
					},
					"level": map[string]any{
						"type":        "string",
						"description": "The syslog level to set",
					},
					"log_to_flash": map[string]any{
						"type":        "boolean",
						"description": "Whether to log to flash memory",
					},
				},
				Required: []string{"mac", "enable", "server_ip", "server_port", "level", "log_to_flash"},
				Type:     "object",
			},
		},
		{
			Name:        "get_device_info",
			Description: "Get a specific device information including the MAC address, IP address, model name, firmware version ..etc",
			Parameters: mcp.ToolInputSchema{
				Properties: map[string]any{
					"mac": map[string]any{
						"type":        "string",
						"description": "The MAC address of the device to get information about",
					},
				},
				Required: []string{"mac"},
				Type:     "object",
			},
		},
		{
			Name:        "list_devices",
			Description: "List all devices in NIMBL in summary. If a filter is provided, only devices whose MAC address, model name, scanned-by field, or IP address match the filter will be listed. A summary and a list of 20 devices MAC address will be returned. If the number of devices is more than 20, the list will be paginated. The page number is 1-based.",
			Parameters: mcp.ToolInputSchema{
				Properties: map[string]any{
					"filter": map[string]any{
						"type":        "string",
						"description": "Optional: A substring to match device MAC address, model name, scanned-by field, or IP address. If provided, only matching devices will be returned.",
					},
					"page": map[string]any{
						"type":        "integer",
						"description": "Optional: The page number to return. Default is 1. Only used if the number of devices is too large (more than 20).",
					},
				},
				Required: []string{},
				Type:     "object",
			},
		},
		{
			Name:        "list_commands",
			Description: "List all commands in summary. If a filter is provided, only commands matching the filter will be returned. A summary and a list of 5 commands will be returned. If the number of commands is more than 5, the list will be paginated. The page number is 1-based.",
			Parameters: mcp.ToolInputSchema{
				Properties: map[string]any{
					"filter": map[string]any{
						"type":        "string",
						"description": "Optional: the filter to apply to the commands. Only commands matching this filter will be returned.",
					},
					"page": map[string]any{
						"type":        "integer",
						"description": "Optional: The page number to return. Default is 1. Only used if the number of commands is too large (more than 5).",
					},
				},
				Required: []string{},
				Type:     "object",
			},
		},
		{
			Name:        "get_command_info",
			Description: "Get a specific command information, inclding the command, status, result, created time, and executed by which client",
			Parameters: mcp.ToolInputSchema{
				Properties: map[string]any{
					"command": map[string]any{
						"type":        "string",
						"description": "The command to get information about",
					},
				},
				Required: []string{"command"},
				Type:     "object",
			},
		},
		{
			Name:        "get_last_commands_info",
			Description: "Get the lastest commands, default 10 commands. You can optionally filter by keyword, look back a duration and limit the number of results. If a filter is provided, only commands matching the filter are returned.",
			Parameters: mcp.ToolInputSchema{
				Properties: map[string]any{
					"filter": map[string]any{
						"type":        "string",
						"description": "Optional: the filter to apply to the commands. Only commands matching this filter will be returned.",
					},
					"duration": map[string]any{
						"type":        "string",
						"description": "Optional: the duration to look back (e.g., '15m' for 15 minutes or '2h' for 2 hours)",
					},
					"limit": map[string]any{
						"type":        "number",
						"description": "Optional: Maximum number of commands to retur., Default is 10, maximum 100.",
					},
				},
				Required: []string{"limit"},
				Type:     "object",
			},
		},
		{
			Name:        "get_topology",
			Description: "Get a specific topology information. The topology is a JSON object that contains the topology information.",
			Parameters: mcp.ToolInputSchema{
				Properties: map[string]any{
					"mac": map[string]any{
						"type":        "string",
						"description": "The MAC address of the device to get topology information about",
					},
				},
				Required: []string{"mac"},
				Type:     "object",
			},
		},
		{
			Name:        "get_logs",
			Description: "Get the latest system logs. You can optionally filter by keyword and limit the number of results. Logs are sorted by newest first.",
			Parameters: mcp.ToolInputSchema{
				Properties: map[string]any{
					"limit": map[string]any{
						"type":        "number",
						"description": "Optional: Maximum number of logs to return. Default is 10, maximum 100.",
					},
					"filter": map[string]any{
						"type":        "string",
						"description": "Optional: A keyword to search within the logs. Only matching logs will be returned.",
					},
				},
				Required: []string{},
				Type:     "object",
			},
		},
		{
			Name:        "generate_log",
			Description: "Generate a log message in root in syslog format RFC3164. The log message is generated with the specified facility, severity, tag, and message. If the severity is 0 or 1, the message is sent to frontend to be displayed.",
			Parameters: mcp.ToolInputSchema{
				Properties: map[string]any{
					"facility": map[string]any{
						"type":        "number",
						"description": "The syslog facility to use for the log message, 0-23, default 1(LOG_USER)",
					},
					"severity": map[string]any{
						"type":        "number",
						"description": "The syslog severity to use for the log message, 0: Emergency, 1: Alert, 2: Critical, 3: Error, 4: Warning, 5: Notice, 6: Informational, 7: Debug, default 6(LOG_INFO)",
					},
					"tag": map[string]any{
						"type":        "string",
						"description": "The syslog tag to use for the log message",
					},
					"message": map[string]any{
						"type":        "string",
						"description": "The syslog message to use for the log message",
					},
				},
				Required: []string{"facility", "severity", "tag", "message"},
				Type:     "object",
			},
		},
	}
	tools = append(tools, IdpsMcpTool()...)     // idps tools
	tools = append(tools, DeviceMcpTool()...)   // device tools
	tools = append(tools, TopologyMcpTool()...) // topology tools
	return tools, nil
}

func integerConvert(v any) (int, error) {
	if i, ok := v.(int); ok {
		return i, nil
	}
	if i, ok := v.(float64); ok {
		return int(i), nil
	}
	if i, ok := v.(string); ok {
		return strconv.Atoi(i)
	}
	return 0, fmt.Errorf("cannot convert %v to integer", v)
}

func handleToolRequest(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	args := request.Params.Arguments
	q.Q("Received request: %s", request.Params.Name)
	q.Q("Arguments: %v", args)
	var cmd string
	var result []byte
	var err error
	cmdinfo := CmdInfo{}

	switch request.Params.Name {
	case "beep_device":
		if len(args) != 1 {
			return nil, fmt.Errorf("expected 1 argument, got %d", len(args))
		}
		mac := args["mac"].(string)
		cmd = fmt.Sprintf("beep %s", mac)
	case "reset_device":
		if len(args) != 1 {
			return nil, fmt.Errorf("expected 1 argument, got %d", len(args))
		}
		mac := args["mac"].(string)
		cmd = fmt.Sprintf("reset %s", mac)
	case "config_device_network":
		if len(args) != 7 {
			return nil, fmt.Errorf("expected 7 arguments, got %d", len(args))
		}
		// config network <mac> <ip> <new_ip> <subnet> <gateway> <hostname> <dhcp>
		var params ConfigNetworkRequest
		if err := mapstructure.Decode(args, &params); err != nil {
			return nil, fmt.Errorf("invalid parameters: %v", err)
		}
		boolInt := map[bool]int{true: 1, false: 0}
		cmd = fmt.Sprintf("config network set %s %s %s %s %s %s %d", params.MAC, params.IP, params.NewIP, params.Subnet, params.Gateway, params.Hostname, boolInt[params.DHCP])
	case "update_device_firmware":
		if len(args) != 2 {
			return nil, fmt.Errorf("expected 2 arguments, got %d", len(args))
		}
		mac, ok := args["mac"].(string)
		if !ok {
			return nil, fmt.Errorf("expected string argument, got %T", args["mac"])
		}
		fileURL, ok := args["file_url"].(string)
		if !ok {
			return nil, fmt.Errorf("expected string argument, got %T", args["file_url"])
		}
		cmd = fmt.Sprintf("update firmware %s %s", mac, fileURL)
	case "config_device_syslog":
		if len(args) != 6 {
			return nil, fmt.Errorf("expected 6 arguments, got %d", len(args))
		}
		// config syslog set <mac> <enable> <server_ip> <server_port> <level> <log_to_flash>
		var params ConfigSyslogRequest
		if err := mapstructure.Decode(args, &params); err != nil {
			return nil, fmt.Errorf("invalid parameters: %v", err)
		}
		boolInt := map[bool]int{true: 1, false: 0}
		cmd = fmt.Sprintf("config syslog set %s %d %s %d %s %d", params.MAC, boolInt[params.Enable], params.ServerIP, params.ServerPort, params.Level, boolInt[params.LogToFlash])
	case "get_device_info":
		if len(args) > 2 || len(args) < 1 {
			return nil, fmt.Errorf("expected 1 argument, got %d", len(args))
		}
		mac, ok := args["mac"].(string)
		if !ok {
			return nil, fmt.Errorf("expected string argument, got %T", args["mac"])
		}
		if _, ok := QC.DevData[mac]; !ok {
			return nil, fmt.Errorf("device with MAC address %s not found", mac)
		}
		result, err = json.Marshal(QC.DevData[mac])
		if err != nil {
			return nil, err
		}
	case "list_devices":
		filter := ""
		if f, ok := args["filter"]; ok {
			filter, _ = f.(string)
		}
		page := 1
		if p, ok := args["page"]; ok {
			if converted, err := integerConvert(p); err == nil {
				page = max(converted, 1)
			}
		}
		pageSize := 20
		filterLower := strings.ToLower(filter)
		keys := make([]string, 0, len(QC.DevData))
		q.Q("filter: %s", filterLower)
		QC.DevMutex.Lock()
		for k, v := range QC.DevData {
			if len(filterLower) > 0 {
				if !strings.Contains(strings.ToLower(k), filterLower) &&
					!strings.Contains(strings.ToLower(v.ModelName), filterLower) &&
					!strings.Contains(strings.ToLower(v.IPAddress), filterLower) &&
					!strings.Contains(strings.ToLower(v.ScannedBy), filterLower) {
					continue
				}
			}
			if v.Mac == "11-22-33-44-55-66" {
				continue
			}
			keys = append(keys, k)
		}
		QC.DevMutex.Unlock()

		// Sort the keys in descending order.
		sort.Slice(keys, func(i, j int) bool {
			return keys[i] > keys[j]
		})

		// Collect summary info.
		agent := 0
		scannedByCount := make(map[string]int)
		modelNameCount := make(map[string]int)
		for _, k := range keys {
			v, ok := QC.DevData[k]
			if !ok {
				continue
			}
			if b, ok := v.Capabilities["agent"]; ok && b {
				agent++
			}
			if v.ScannedBy != "" {
				scannedByCount[v.ScannedBy]++
			}
			if v.ModelName != "" {
				modelNameCount[v.ModelName]++
			}
		}
		scannedByMsg := ""
		for k, v := range scannedByCount {
			scannedByMsg += fmt.Sprintf("Scanned by %s: %d\n", k, v)
		}
		modelNameMsg := ""
		for k, v := range modelNameCount {
			modelNameMsg += fmt.Sprintf("Model name %s: %d\n", k, v)
		}

		// Apply pagination using the helper.
		pagedKeys, totalPages := mcpToolPaginate(keys, page, pageSize)
		showDev := ""
		for i, k := range pagedKeys {
			if i > 0 {
				showDev += ", "
			}
			showDev += fmt.Sprintf("%s (%s) (%s)", k, QC.DevData[k].Mac, QC.DevData[k].ModelName)
		}
		if len(keys) < pageSize {
			showDev = fmt.Sprintf("MAC addresses: %s", showDev)
		} else {
			showDev = fmt.Sprintf("Page %d of %d. MAC addresses: %s", page, totalPages, showDev)
			showDev += " Device list is summarized due to large volume. Use a keyword (MAC, model name, IP) as a filter to see more specific devices."
		}
		result = fmt.Appendf(nil, `
		Total devices: %d, 
		Total agent supported: %d, 
		%s. 
		%s.
		%s.
		`, len(keys), agent, scannedByMsg, modelNameMsg, showDev)

	case "list_commands":
		filter := ""
		if f, ok := args["filter"]; ok {
			filter, _ = f.(string)
		}
		page := 1
		if p, ok := args["page"]; ok {
			if converted, err := integerConvert(p); err == nil {
				page = max(converted, 1)
			}
		}
		pageSize := 5
		keys := make([]string, 0, len(QC.CmdData))
		filter = strings.ToLower(filter)
		QC.CmdMutex.Lock()
		for k := range QC.CmdData {
			if len(filter) > 0 && !strings.Contains(strings.ToLower(k), filter) {
				continue
			}
			keys = append(keys, k)
		}
		QC.CmdMutex.Unlock()

		// Sort the keys in descending order.
		sort.Slice(keys, func(i, j int) bool {
			return keys[i] > keys[j]
		})

		pagedKeys, totalPages := mcpToolPaginate(keys, page, pageSize)
		showCmd := ""
		for i, k := range pagedKeys {
			if i > 0 {
				showCmd += ", "
			}
			showCmd += k
		}
		if len(keys) > pageSize {
			showCmd += fmt.Sprintf(" Page %d of %d. Commands: %s.", page, totalPages, showCmd)
			showCmd += " Command list is summarized due to large volume. Use a keyword (command) as a filter to see more specific commands."
		}
		result = fmt.Appendf(nil, `
		Total commands: %d. 
		%s.
		`, len(keys), showCmd)
	case "get_command_info":
		if len(args) != 1 {
			return nil, fmt.Errorf("expected 1 argument, got %d", len(args))
		}
		QC.CmdMutex.Lock()
		defer QC.CmdMutex.Unlock()
		command, ok := args["command"].(string)
		if !ok {
			return nil, fmt.Errorf("expected string argument, got %T", args["command"])
		}
		if _, ok := QC.CmdData[command]; !ok {
			return nil, fmt.Errorf("command %s not found", command)
		}
		result, err = json.Marshal(QC.CmdData[command])
		if err != nil {
			return nil, err
		}
	case "get_last_commands_info":
		limit := 10
		if converted, err := integerConvert(args["limit"]); err == nil {
			if converted > 100 {
				limit = 100
			} else {
				limit = max(converted, 1)
			}
		}
		filter := ""
		if _, ok := args["filter"]; ok {
			filter = args["filter"].(string)
		}
		duration := ""
		if _, ok := args["duration"]; ok {
			duration = args["duration"].(string)
		}
		QC.CmdMutex.Lock()
		lastcmds, err := RetrieveLastCmds(LastCmdsQuery{Filter: filter, Duration: duration, Count: limit})
		QC.CmdMutex.Unlock()
		if err != nil {
			return nil, err
		}
		result, err = json.Marshal(lastcmds)
		if err != nil {
			return nil, err
		}
	case "get_topology":
		if len(args) != 1 {
			return nil, fmt.Errorf("expected 1 argument, got %d", len(args))
		}
		mac, ok := args["mac"].(string)
		if !ok {
			return nil, fmt.Errorf("expected string argument, got %T", args["mac"])
		}
		QC.TopologyMutex.Lock()
		defer QC.TopologyMutex.Unlock()
		if _, ok := QC.TopologyData[mac]; !ok {
			return nil, fmt.Errorf("topology with MAC address %s not found", mac)
		}
		result, err = json.Marshal(QC.TopologyData[mac])
		if err != nil {
			return nil, err
		}
	case "get_logs":
		limit := 10
		if converted, err := integerConvert(args["limit"]); err == nil {
			if converted > 100 {
				limit = 100
			} else {
				limit = max(converted, 1)
			}
		}
		page := 1
		filter := ""
		if _, ok := args["filter"]; ok {
			filter = args["filter"].(string)
		}
		logs, err := getSyslogWithQuery([]string{QC.SyslogLocalPath}, SyslogQuery{MaxLine: limit, Page: page, Filter: filter})
		if err != nil {
			return nil, err
		}
		result, err = json.Marshal(logs)
		if err != nil {
			return nil, err
		}
	case "generate_log":
		if len(args) != 4 {
			return nil, fmt.Errorf("expected 4 arguments, got %d", len(args))
		}
		// not a command, validate and send to syslog
		var params GenerateLogRequest
		if err := mapstructure.Decode(args, &params); err != nil {
			return nil, fmt.Errorf("invalid parameters: %v", err)
		}
		if params.Facility < LOG_KERN || params.Facility > LOG_LOCAL7 {
			return nil, fmt.Errorf("invalid facility: %d", params.Facility)
		}
		if params.Severity < LOG_EMERG || params.Severity > LOG_DEBUG {
			return nil, fmt.Errorf("invalid severity: %d", params.Severity)
		}
		priority := params.Facility*8 | params.Severity
		err = SendSyslog(priority, params.Tag, params.Message)
		if err != nil {
			q.Q(err)
			// return nil, fmt.Errorf("error sending syslog: %v", err)
		}
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Log message sent: %s", params.Message),
				},
			},
		}, nil
	case "idps_import_rules":
		c, err := handleImportRuleTool(&cmdinfo, args)
		if err != nil {
			return nil, err
		}
		cmd = c
	case "idps_add_rule":
		c, err := handleAddRuleTool(&cmdinfo, args)
		if err != nil {
			return nil, err
		}
		cmd = c
	case "idps_delete_rules":
		c, err := handleDeleteRuleTool(&cmdinfo, args)
		if err != nil {
			return nil, err
		}
		cmd = c
	case "delete_all_devices":
		c, err := handleMcpDeleteAllDevices(args)
		if err != nil {
			return nil, err
		}
		cmd = c
	case "delete_device":
		c, err := handleMcpDeleteDevice(args)
		if err != nil {
			return nil, err
		}
		cmd = c
	case "scan_ip_range":
		c, err := handleMcpScanIPRange(args)
		if err != nil {
			return nil, err
		}
		cmd = c
	case "edit_device":
		c, err := handleMcpEditDevice(args)
		if err != nil {
			return nil, err
		}
		cmd = c
	case "update_topology":
		topdata, err := handleMcpTopologyUpdate(args)
		if err != nil {
			return nil, err
		}
		result, err = json.Marshal(topdata)
		if err != nil {
			return nil, err
		}
	case "save_topology":
		filepath, err := handleMcpSaveTopology(args)
		if err != nil {
			return nil, err
		}
		result = []byte(filepath)
	case "restore_topology":
		filepath, err := handleMcpRestoreTopology(args)
		if err != nil {
			return nil, err
		}
		result = []byte(filepath)
	default:
		return nil, fmt.Errorf("unsupported function: %s", request.Params.Name)
	}

	if len(result) > 0 {
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: string(json.RawMessage(result)),
				},
			},
		}, nil
	}

	if len(cmd) > 0 {
		q.Q(cmd)
		cmdinfo.Command = cmd

		_cmdinfo, err := ValidateCommands(&cmdinfo)
		if err != nil {
			return nil, err
		}

		cmds := []CmdInfo{*_cmdinfo}
		err = UpdateCmds(cmds)
		if err != nil {
			return nil, err
		}

		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Command sent: %s", cmd),
				},
			},
		}, nil
	}

	return nil, fmt.Errorf("no command or data to send")
}

// mcpToolPaginate returns the paginated slice of keys and the total number of pages.
func mcpToolPaginate(keys []string, page, pageSize int) (paginated []string, totalPages int) {
	totalPages = (len(keys) + pageSize - 1) / pageSize
	if totalPages == 0 {
		totalPages = 1
	}
	if page > totalPages {
		page = totalPages
	}
	start := (page - 1) * pageSize
	end := page * pageSize
	if end > len(keys) {
		end = len(keys)
	}
	return keys[start:end], totalPages
}
