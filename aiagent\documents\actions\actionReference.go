package actions

import (
	_ "embed"
	"encoding/json"
)

//go:embed data/queries.json
var predefinedActionsReference []byte

type ActionReference struct {
	Queries []string `json:"queries"`
	Action  string   `json:"action"`
}

type ActionReferences []ActionReference

// GetPredefinedActionsReferences initializes the predefined actions references
func GetPredefinedActionsReferences() (ActionReferences, error) {
	return DeserializeActionReferences(predefinedActionsReference)
}

// DeserializeActionReferences deserializes a ActionReferences
func DeserializeActionReferences(data []byte) (ActionReferences, error) {
	actionReferences := ActionReferences{}
	err := json.Unmarshal(data, &actionReferences)
	if err != nil {
		return nil, err
	}
	return actionReferences, nil
}
