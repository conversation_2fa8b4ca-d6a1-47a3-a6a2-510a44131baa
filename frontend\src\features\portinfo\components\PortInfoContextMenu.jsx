import { createStyles } from "antd-style";
import { Item, Menu } from "react-contexify";
import {
  MENU_IDS,
  portInfoMenuItem,
} from "../../../components/context-menu/menu-items";
import { useCallback } from "react";
import { App, Flex } from "antd";
import { useSendCommand } from "../../../services/mutations";
import { handleCommandExecution } from "../../../utils/command-execution";

const useStyles = createStyles(({ token, css }) => ({
  contexify: css`
    --contexify-menu-bgColor: ${token.colorBgContainer};
    --contexify-separator-color: #4c4c4c;
    --contexify-item-color: ${token.colorText};
    --contexify-activeItem-bgColor: ${token.colorPrimaryActive};
  `,
}));

export const PortInfoContextMenu = () => {
  const { styles } = useStyles();
  const { notification } = App.useApp();
  const sendCommand = useSendCommand();
  const executeCommand = useCallback(
    (command) => {
      const commands = [{ command }];
      return handleCommandExecution(commands, sendCommand, notification);
    },
    [sendCommand, notification]
  );
  const handleContextMenuItemClick = useCallback(
    ({ id, props: { record } }) => {
      const { mac, portName } = record;
      const command = `agent config port enable ${mac} ${portName} ${id === "enable" ? 1 : 0}`;
      executeCommand(command);
    },
    []
  );

  const isItemHidden = useCallback(({ id, props }) => {
    if (id === "disable" && !props.record.enableStatus) {
      return true;
    }
    if (id === "enable" && props.record.enableStatus) {
      return true;
    }
    return false;
  }, []);

  return (
    <Menu id={MENU_IDS.PORT_INFO} className={styles.contexify}>
      {portInfoMenuItem.map((item) => (
        <Item
          key={item.key}
          id={item.key}
          onClick={handleContextMenuItemClick}
          data-testid={`menu-item-${item.key}`}
          hidden={isItemHidden}
        >
          <Flex gap={10}>{item.label}</Flex>
        </Item>
      ))}
    </Menu>
  );
};
