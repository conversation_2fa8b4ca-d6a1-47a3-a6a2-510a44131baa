/**
 * Generates a command for network settings.
 * @param {string} mac - The MAC address of the device.
 * @param {string} currentIp - The current IP address of the device.
 * @param {object} values - The values to be used in the command.
 * @returns {Array} - An array containing the generated command.
 */
export const generateNetworkCommand = (mac, currentIp, values) => {
  const ipAddress = values.isdhcp ? "0.0.0.0" : values.new_ip_address;
  const dhcpFlag = values.isdhcp ? 1 : 0;

  return [
    {
      command: `config network set ${mac} ${currentIp} ${ipAddress} ${values.netmask} ${values.gateway} ${values.hostname} ${dhcpFlag}`,
    },
  ];
};

/**
 * Generates a command for syslog settings.
 * @param {string} mac - The MAC address of the device.
 * @param {object} values - The values to be used in the command.
 * @returns {Array} - An array containing the generated command.
 */
export const generateSyslogCommand = (mac, values) => {
  const logToFlash = values.logToFlash ? 1 : 0;
  const logToServer = values.logToServer ? 1 : 0;
  return [
    {
      command: `config syslog set ${mac} ${logToServer} ${values.serverIP} ${values.serverPort} ${values.logLevel} ${logToFlash}`,
    },
  ];
};

/**
 * Generates a command for SNMP trap settings.
 * @param {string} mac - The MAC address of the device.
 * @param {object} values - The values to be used in the command.
 * @returns {Array} - An array containing the generated command.
 */
const generateTrapCommand = (mac, values) => {
  const { serverIP, serverPort, comString } = values;
  return [
    {
      command: `snmp trap add ${mac} ${serverIP} ${serverPort} ${comString}`,
    },
  ];
};

/**
 * Generates a command for firmware update.
 * @param {string} mac - The MAC address of the device.
 * @param {object} values - The values to be used in the command.
 * @returns {Array} - An array containing the generated command.
 */
const generateFwCommand = (mac, values) => {
  return [
    {
      command: `firmware update ${mac} ${values.fwUrl}`,
    },
  ];
};

/**
 * Generates a command for device edit.
 * @param {string} mac - The MAC address of the device.
 * @param {object} values - The values to be used in the command.
 * @returns {Array} - An array containing the generated command.
 */
const generateDeviceEditCommand = (mac, values) => {
  const { modelname, hostname, netmask } = values;
  return [
    {
      command: `device edit ${mac} ${modelname} ${netmask} ${hostname}`,
    },
  ];
};

/**
 * Generates a command based on the type and values provided.
 * @param {string} mac - The MAC address of the device.
 * @param {string} currentIp - The current IP address of the device.
 * @param {object} values - The values to be used in the command.
 * @param {string} type - The type of command to generate (network, syslog, trap).
 * @returns {Array} - An array containing the generated command.
 */
export const generateCommand = (mac, currentIp, values, type) => {
  switch (type) {
    case "network":
      return generateNetworkCommand(mac, currentIp, values);
    case "syslog":
      return generateSyslogCommand(mac, values);
    case "trap":
      return generateTrapCommand(mac, values);
    case "firmware":
      return generateFwCommand(mac, values);
    case "device-edit":
      return generateDeviceEditCommand(mac, values);
    default:
      throw new Error("Invalid command type");
  }
};
