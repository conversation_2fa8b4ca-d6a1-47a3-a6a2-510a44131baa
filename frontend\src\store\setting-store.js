import { create } from "zustand";
import { createJSONStorage, devtools, persist } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";
import { axiosInstance } from "../services/api";

/**
 * @typedef {Object} SettingState
 * @property {string} mode - Theme mode (light/dark/auto)
 * @property {string} colorPrimary - Primary theme color
 * @property {string} baseURL - API base URL
 * @property {string} wsURL - WebSocket URL
 * @property {string} inventoryType - Type of inventory view
 */

/**
 * @typedef {Object} SettingActions
 * @property {(mode: string) => void} changeMode - Change theme mode
 * @property {(color: string) => void} changePrimaryColor - Change primary color
 * @property {(url: string) => void} changeBaseURL - Change API base URL
 * @property {(url: string) => void} changeWsURL - Change WebSocket URL
 * @property {(type: string) => void} changeInventoryType - Change inventory type
 */

const initialState = {
  mode: "dark",
  colorPrimary: "#13c2c2",
  baseURL: "http://localhost:27182",
  wsURL: "ws://localhost:27182",
  inventoryType: "device",
};

/**
 * Settings store for managing application configuration
 * @type {import("zustand").UseBoundStore<SettingState & SettingActions>}
 */
export const useSettingStore = create(
  devtools(
    immer(
      persist(
        (set) => ({
          ...initialState,
          changeMode: (mode) =>
            set((state) => {
              state.mode = mode;
            }),
          changePrimaryColor: (colorPrimary) =>
            set((state) => {
              state.colorPrimary = colorPrimary;
            }),
          changeBaseURL: (baseURL) =>
            set((state) => {
              axiosInstance.defaults.baseURL = baseURL;
              state.baseURL = baseURL;
            }),
          changeWsURL: (wsURL) =>
            set((state) => {
              state.wsURL = wsURL;
            }),
          changeInventoryType: (type) =>
            set((state) => {
              state.inventoryType = type;
            }),
        }),
        {
          name: "nimbl-setting",
          storage: createJSONStorage(() => localStorage),
          partialize: (state) => ({
            mode: state.mode,
            colorPrimary: state.colorPrimary,
            baseURL: state.baseURL,
            wsURL: state.wsURL,
            inventoryType: state.inventoryType,
          }),
        }
      )
    ),
    {
      name: "SettingStore",
      enabled: process.env.NODE_ENV === "development",
    }
  )
);
