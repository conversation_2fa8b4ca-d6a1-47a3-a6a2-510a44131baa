# BBNIM code assist guide

Use AI to enhance existing code base

## Prepare

- Gemini api key
  - Use google to search `Get Gemini api key`, and you can get `Gemini api key` step by step.

## Quick Start

1. Run `make code-assist `
2. Export Gemini api key `export GEMINI_API_KEY=XXXX`, or step 3. need to add flag `-geminikey`
3. Run code-assist
```shell
# export Gemini api key `export GEMINI_API_KEY=XXXX`
$ ./code-assist -debuglog
# no export Gemini api key
$ ./code-assist -geminikey "XXXX" -debuglog
```
4. Code-assist will write down result to `code-assist/goVetResult-gemini-1.5-flash.md` and `code-assist/analyzeResult-gemini-1.5-flash.md`.
