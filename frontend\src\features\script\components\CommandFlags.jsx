import React, { memo } from 'react';
import { Card, Divider, Flex, Form, Input, Checkbox, Button } from 'antd';
import { useScript } from '../hooks/useScript';

/**
 * Command Flags component for configuring command options
 * @returns {JSX.Element} Command Flags component
 */
const CommandFlags = () => {
  const { cmdFlags, setCmdFlags, clearCmdFlags, updateCmdFlag } = useScript();
  
  return (
    <Card variant="borderless">
      <Divider orientation="left">Command Flags</Divider>
      <Flex gap={10} justify="space-between">
        <Form.Item
          label="Client"
          tooltip="Enter the network service name to send the command to the specified network service."
        >
          <Input
            placeholder="-cc"
            value={cmdFlags.cc}
            onChange={(e) => updateCmdFlag('cc', e.target.value)}
          />
        </Form.Item>
        <Form.Item
          label="Tag"
          tooltip="Add tag to the command. This will change the syslog program name style. The default is RunCmd."
        >
          <Input
            placeholder="-ct"
            value={cmdFlags.ct}
            onChange={(e) => updateCmdFlag('ct', e.target.value)}
          />
        </Form.Item>
        <Form.Item
          label="Kind"
          tooltip="Send the command to the root service. Currently, you can only enter root."
        >
          <Input
            placeholder="-ck"
            value={cmdFlags.ck}
            onChange={(e) => updateCmdFlag('ck', e.target.value)}
          />
        </Form.Item>
      </Flex>
      <Flex gap={10} justify="space-between">
        <Form.Item
          label="No Overwrite"
          tooltip="The command will not be overwritten, so the same command will not take effect again. You should be careful when using this flag."
        >
          <Checkbox
            checked={cmdFlags.cno}
            onChange={(e) => updateCmdFlag('cno', e.target.checked)}
          />
        </Form.Item>
        <Form.Item 
          label="No Syslog" 
          tooltip="This command will not send syslog."
        >
          <Checkbox
            checked={cmdFlags.cns}
            onChange={(e) => updateCmdFlag('cns', e.target.checked)}
          />
        </Form.Item>
      </Flex>
      <Button block onClick={clearCmdFlags}>
        Clear Command Flags
      </Button>
    </Card>
  );
};

export default memo(CommandFlags);
