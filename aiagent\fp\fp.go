package fp

import "context"

type Invoker interface {
	Invoke(context.Context, string) (string, error)
}

type Functor func(context.Context, string) (string, error)

type AddPostfix struct {
	Postfix string
}

func (a AddPostfix) Invoke(ctx context.Context, input string) (string, error) {
	return input + a.Postfix, nil
}

// Concatenate takes strings and accumulates them
func Concatenate(invokers ...Invoker) Functor {
	return func(ctx context.Context, input string) (string, error) {
		var result string
		for _, invoker := range invokers {
			out, err := invoker.Invoke(ctx, input)
			if err != nil {
				return "", err
			}
			result += out
		}
		return result, nil
	}
}

func Pipeline(invokers ...Invoker) func(context.Context, string) (string, error) {
	return func(ctx context.Context, input string) (string, error) {
		var err error
		for _, invoker := range invokers {
			input, err = invoker.Invoke(ctx, input)
			if err != nil {
				return "", err
			}
		}
		return input, nil
	}
}

func Reduce(ctx context.Context, input string, invokers ...Invoker) (string, error) {
	result := ""
	for _, invoker := range invokers {
		out, err := invoker.Invoke(ctx, input)
		if err != nil {
			return "", err
		}
		result += out // Aggregate output
	}
	return result, nil
}
