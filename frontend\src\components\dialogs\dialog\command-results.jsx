import { Modal } from "antd";
import React from "react";
import { CommandTable } from "../../../features/command-results";

const CommandResultsDialog = ({ onClose }) => {
  return (
    <Modal
      title="Command Results"
      maskClosable={false}
      open
      destroyOnClose
      onCancel={onClose}
      width="100%"
      footer={null}
      style={{ top: 20 }}
    >
      <CommandTable />
    </Modal>
  );
};

export default React.memo(CommandResultsDialog);
