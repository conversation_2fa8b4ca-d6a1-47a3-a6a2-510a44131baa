// Package statistic provides anomaly detection statistic related functions and data structures
package statistic

import (
	"errors"
	"fmt"
	"mnms/encrypt"
	"mnms/llm"
	"sync"
	"time"

	"github.com/qeof/q"
)

var StatisticMutex = sync.Mutex{}

type OpenAISettings struct {
	APIKey       string `json:"api_key" mapstructure:"api_key"`
	IsDemoAPIKey bool   `json:"is_demo_api_key" mapstructure:"is_demo_api_key"`
}

type OllamaSettings struct {
	Host  string `json:"host" mapstructure:"host"`
	Model string `json:"model" mapstructure:"model"`
	Port  int    `json:"port" mapstructure:"port"`
}

// RealTimeDetectionSettings is a real time detection settings
type RealTimeDetectionSettings struct {
	Enabled    bool          `json:"enabled" mapstructure:"enabled"`
	SyslogAddr string        `json:"syslog_addr" mapstructure:"syslog_addr"`
	BufferSize int           `json:"buffer_size" mapstructure:"buffer_size"`
	Changed    chan struct{} `json:"-"`
}

type DailyData struct {
	Data [1440]int `json:"data" mapstructure:"data"`
}

// GetLargeLanguageModelFromAnomalyStatistics () get large language model from anomaly statistics
func GetLargeLanguageModelFromAnomalyStatistics() (*llm.LargeLanguageModel, error) {
	var llmSettings llm.LargeLanguageModel
	switch LocalStatistic.Settings.Model {
	case "open-ai":
		llmSettings = *llm.GPTSettings()
	case "ollama":
		llmSettings = *llm.OllamaSettings(LocalStatistic.Settings.OllamaSettings.Model)
	default:
		return nil, fmt.Errorf("unknown LLM server name %s", LocalStatistic.Settings.Model)
	}
	llmSettings.APIKey = LocalStatistic.Settings.OpenAISettings.APIKey
	return &llmSettings, nil
}

// Input to place a value in the array base on the time provided.
func (d *DailyData) Input(t time.Time, value int) error {

	minuteOfDay := t.Hour()*60 + t.Minute()
	if minuteOfDay < 0 || minuteOfDay >= 1440 {
		return errors.New("time out of bounds for daily data")
	}
	q.Q("Input", t, value, minuteOfDay)
	d.Data[minuteOfDay] = value
	return nil
}

// Get value from array based on the time provided.
func (d *DailyData) Get(t time.Time) (int, error) {
	minuteOfDay := t.Hour()*60 + t.Minute()
	if minuteOfDay < 0 || minuteOfDay >= 1440 {
		return 0, errors.New("time out of bounds for daily data")
	}
	return d.Data[minuteOfDay], nil
}

// GetLastNValue get last n values from array based on the time provided.
func (d *DailyData) GetLastNValue(t time.Time, n int) ([]int, error) {
	minuteOfDay := t.Hour()*60 + t.Minute()
	if minuteOfDay < 0 || minuteOfDay >= 1440 {
		return nil, errors.New("time out of bounds for daily data")
	}
	if n > minuteOfDay {
		return nil, errors.New("n is greater than the minute of day")
	}

	return d.Data[minuteOfDay-n : minuteOfDay], nil
}

// RealtimeStatistic is a real time statistic
type RealtimeStatistic struct {
	Syslog                    string    `json:"syslog" mapstructure:"syslog"`
	TotalMessages             int       `json:"total_messages" mapstructure:"total_messages"`
	TotalAlerts               int       `json:"total_alerts" mapstructure:"total_alerts"`
	LastMinuteMessagesCount   int       `json:"last_minute_messages_count" mapstructure:"last_minute_messages_count"`
	LastMinuteAlertCount      int       `json:"last_minute_alert_count" mapstructure:"last_minute_alert_count"`
	IncomingMessagesDailyData DailyData `json:"incoming_messages_daily_data" mapstructure:"incoming_messages_daily_data"`
	AlertMessagesDailyData    DailyData `json:"alert_messages_daily_data" mapstructure:"alert_messages_daily_data"`
}

// IncMessage increase message count
func (r *RealtimeStatistic) IncMessage() {
	r.TotalMessages++
}

// UpdateSyslog update syslog
func (r *RealtimeStatistic) UpdateSyslog(syslog string) {
	r.Syslog = syslog
}

// IncAlert increase alert count
func (r *RealtimeStatistic) IncAlert() {
	r.TotalAlerts++
}

// ResetLastMinuteData reset the last minute data
func (r *RealtimeStatistic) ResetLastMinuteData() error {
	q.Q("ResetLastMinuteData")
	lastMinMessages := r.TotalMessages - r.LastMinuteMessagesCount
	lastMinAlerts := r.TotalAlerts - r.LastMinuteAlertCount

	err := r.IncomingMessagesDailyData.Input(time.Now(), lastMinMessages)
	if err != nil {
		return err
	}

	err = r.AlertMessagesDailyData.Input(time.Now(), lastMinAlerts)
	if err != nil {
		return err
	}
	r.LastMinuteMessagesCount = r.TotalMessages
	r.LastMinuteAlertCount = r.TotalAlerts
	return nil
}

// Settings is a anomaly detection settings
type Settings struct {
	LastError         string                    `json:"last_error" mapstructure:"last_error"`
	Root              string                    `json:"root" mapstructure:"root"`
	Token             string                    `json:"token" mapstructure:"token"`
	Score             float32                   `json:"score" mapstructure:"score"`
	PullIntervalMins  int                       `json:"pull_interval_mins" mapstructure:"pull_interval_mins"`
	Detect            bool                      `json:"detect" mapstructure:"detect"`
	OpenAISettings    OpenAISettings            `json:"openai_settings" mapstructure:"openai_settings"`
	OllamaSettings    OllamaSettings            `json:"ollama_settings" mapstructure:"ollama_settings"`
	Model             string                    `json:"model" mapstructure:"model"`
	LogSvc            string                    `json:"log_svc" mapstructure:"log_svc"`
	RealTimeDetection RealTimeDetectionSettings `json:"real_time_detection" mapstructure:"real_time_detection"`
}

// Statistic is a anomaly detection statistic
type Statistic struct {
	Since           string             `json:"since" mapstructure:"since"` //time.Now().Format(time.RFC3339)
	TotalMessages   int                `json:"total_messages" mapstructure:"total_messages"`
	AnomalyMessages int                `json:"anomaly_messages" mapstructure:"anomaly_messages"`
	ClientName      string             `json:"clientname" mapstructure:"clientname"` // a service name
	Realtime        *RealtimeStatistic `json:"realtime" mapstructure:"realtime"`     // real time statistic
	Settings        Settings           `json:"settings" mapstructure:"settings"`     // client settings
}

var LocalStatistic = Statistic{
	Since:           time.Now().Format(time.RFC3339),
	TotalMessages:   0,
	AnomalyMessages: 0,
	ClientName:      "anomaly_detection",
	Settings: Settings{
		LastError: "",
		Token:     "",
		Score:     0.4,
		OpenAISettings: OpenAISettings{
			APIKey:       "",
			IsDemoAPIKey: true,
		},
		OllamaSettings: OllamaSettings{
			Host:  "http://localhost",
			Model: "llama2",
			Port:  11434,
		},
		PullIntervalMins: 480, // 8 hours
		Detect:           true,
		Model:            "open-ai",
		LogSvc:           "",
		RealTimeDetection: RealTimeDetectionSettings{
			Enabled:    false,
			BufferSize: 300,
			Changed:    make(chan struct{}),
		},
	},
	Realtime: &RealtimeStatistic{
		Syslog:                  "",
		TotalMessages:           0,
		TotalAlerts:             0,
		LastMinuteMessagesCount: 0,
		LastMinuteAlertCount:    0,
		IncomingMessagesDailyData: DailyData{
			Data: [1440]int{},
		},
		AlertMessagesDailyData: DailyData{
			Data: [1440]int{},
		},
	},
}
var DemoAPIKey = "098827874f26ef21785f57488c92ae6f4f6b1beea190aad791669b94df1a273b71e41c28f767a726fe7b267b577a97e8d8b1ce4d6d382617e4bf60b111d99b54215d79ca50726cfe"

func init() {
	demokey, _ := encrypt.DecryptedKey(DemoAPIKey)
	LocalStatistic.Settings.OpenAISettings.APIKey = demokey
}

// GetClientName get client name
func GetClientName() string {
	return LocalStatistic.ClientName
}

// GetToken get token
func GetToken() string {
	return LocalStatistic.Settings.Token
}

// SetToken set token
func SetToken(token string) {
	StatisticMutex.Lock()
	defer StatisticMutex.Unlock()
	LocalStatistic.Settings.Token = token

}
