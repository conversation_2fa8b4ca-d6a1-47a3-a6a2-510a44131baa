import { Center } from "react-layout-kit";
import { Button, Form, Typography, InputNumber, Input } from "antd";
import { GenetateKey, GenetatePlainText } from "../wailsjs/go/main/App";
import { useState } from "react";

function App() {
  const [form] = Form.useForm();
  const [savedResult, setSavedResult] = useState("");
  const [plainResult, setPlainResult] = useState("");
  const onFinish = (values) => {
    setSavedResult("");
    setPlainResult("");
    GenetateKey(values.noOfServices, values.noOfDevices, values.machineID)
      .then((result) => {
        form.resetFields();
        setSavedResult(result);
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const onFinishFailed = (errorInfo) => {
    console.log("Failed:", errorInfo);
  };

  const handleOpenFile = () => {
    setSavedResult("");
    setPlainResult("");
    GenetatePlainText()
      .then((result) => {
        setPlainResult(result);
      })
      .catch((err) => {
        console.log(err);
      });
  };

  return (
    <Center
      style={{
        width: "100vw",
        height: "100vh",
      }}
    >
      <div style={{ width: 350 }}>
        <Center>
          <Typography.Title level={4}>NMS Key Generator</Typography.Title>
          {savedResult !== "" && (
            <Typography.Text>{savedResult}</Typography.Text>
          )}
        </Center>
        <Form
          layout="vertical"
          form={form}
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
        >
          <Form.Item
            label="Machine ID"
            name="machineID"
            rules={[
              {
                required: true,
                message: "Please input machine id!",
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="No of network services"
            name="noOfServices"
            rules={[
              {
                required: true,
                message: "Please input no. of network services!",
              },
            ]}
          >
            <InputNumber
              style={{
                width: "100%",
              }}
            />
          </Form.Item>
          <Form.Item
            label="No of devices"
            name="noOfDevices"
            rules={[
              {
                required: true,
                message: "Please input no. of devices!",
              },
            ]}
          >
            <InputNumber
              style={{
                width: "100%",
              }}
            />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" block>
              Submit
            </Button>
          </Form.Item>
          <Form.Item>
            <Button type="primary" block onClick={handleOpenFile}>
              open file to varify
            </Button>
          </Form.Item>
        </Form>
        <div>
          <pre>
            <code style={{ fontSize: "12px" }}>{plainResult}</code>
          </pre>
        </div>
      </div>
    </Center>
  );
}

export default App;
