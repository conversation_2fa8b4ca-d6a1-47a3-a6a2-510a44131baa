package payload

import (
	"errors"
	"fmt"
	"mnms/idpsystem/idps/mpm"
	"mnms/idpsystem/idps/spm"
	"unsafe"

	"github.com/google/gonids"
)

const (
	ciFlagsStart = 1 << iota /**< indication that current buffer is the start of the data */
	ciFlagsEnd               /**< indication that current buffer is the end of the data */
	ciFlagsDceLE             /**< DCERPC record in little endian */
	ciFlagsDceBE             /**< DCERPC record in big endian */
)
const ciFlagsSingle = ciFlagsStart | ciFlagsEnd

type matchFlag int

const (
	noMatchDiscontinue matchFlag = iota - 1
	noMatch
	match
)

type Detect struct {
	id              int
	postition       gonids.DataPos
	detectedID      detectedId
	data            any
	prev            *Detect
	next            *Detect
	prfilterPayload *mpm.Content
}

func NewdetectContent(id int, r gonids.Rule, d *Data) (*Detect, error) {
	l := newList(id)
	if r.Absent != nil {
		err := loadAbsent(l, *r.Absent)
		if err != nil {
			return nil, err
		}
	}
	err := loadOrder(l, r.Matchers, d)
	if err != nil {
		return nil, err
	}
	return l.head, nil
}

func loadAbsent(l *list, r gonids.Absent) error {
	if r.DataPosition != gonids.PayloadData {
		err := newAbsent(l, r)
		if err != nil {
			return err
		}
	}

	return nil
}

func loadOrder(l *list, orders []gonids.OrderedMatcher, d *Data) error {
	for _, v := range orders {
		switch r := v.(type) {
		case *gonids.Content:
			err := loadContent(l, r)
			if err != nil {
				return err
			}
		case *gonids.ByteMatch:
			err := loadByteMatch(l, r, d)
			if err != nil {
				return err
			}
		case *gonids.LenMatch:
			err := loadLenMatch(l, r)
			if err != nil {
				return err
			}
		case *gonids.PCRE:
			err := newPcreData(l, r)
			if err != nil {
				return err
			}

		}
	}
	return nil
}

func loadContent(l *list, v *gonids.Content) error {
	d := &Detect{
		id:         l.id,
		detectedID: detectContent,
		postition:  v.DataPosition,
	}
	c := newContent(v)
	d.data = c
	if v.Negate {
		c.flags |= contentNegated
	}
	err := l.appendList(d)
	if err != nil {
		return err
	}
	for _, v := range v.Options {
		err := mapContentHandler(l, v)
		if err != nil {
			return err
		}
	}
	return nil
}

func loadByteMatch(l *list, bm *gonids.ByteMatch, data *Data) error {
	err := mapByteHandler(l, bm, data)
	if err != nil {
		return err
	}
	return nil
}

func loadLenMatch(l *list, lm *gonids.LenMatch) error {
	err := mapSizeHandler(l, lm)
	if err != nil {
		return err
	}
	return nil
}

func (d *Detect) Build() error {
	temp := d
	for temp != nil {
		if temp.detectedID != detectContent {
			temp = temp.next
			continue
		}
		spm, err := spm.NewSpm()
		if err != nil {
			return err
		}
		err = spm.LoadContent(temp.retrieveSpm())
		if err != nil {
			return err
		}
		err = spm.Build()
		if err != nil {
			return err
		}
		cont, _ := temp.data.(*content)
		cont.spm = spm
		temp = temp.next
	}
	d.tagMpmData()
	return nil
}

func (d *Detect) DetectContentInspection(p *Packet) bool {
	p.flags = ciFlagsSingle
	r := detectEngineContentInspectionInternal(d, p)
	if r == match {
		replaceMatch(p)
	}
	return r == match
}

func detectEngineContentInspectionInternal(d *Detect, p *Packet) matchFlag {
	switch d.detectedID {
	case detectContent:
		return detectContentID(d, p)
	case detectAbsent:
		ab, _ := d.data.(*absent)
		if !ab.or_else {
			return noMatch
		}
		return matched(d, p)
	case detectIsDataAt:
		return detectIsDataAtID(d, p)
	case detectBsize:
		bsize, _ := d.data.(*bsize)
		b := bsize.inspect(p)
		if !b {
			return noMatch
		}
		return matched(d, p)
	case detectDsize:
		dsize, _ := d.data.(*dsize)
		b := dsize.inspect(int(p.payloadLen))
		if !b {
			return noMatch
		}
		return matched(d, p)
	case detectByteTest:
		btd, _ := d.data.(*byteTest)
		btflags := btd.flags
		offset := btd.offset
		value := btd.value
		nbytes := btd.nbytes
		if btflags&byteTestOffsetVar > 0 {
			offset = int32(p.byteValues[offset])
		}
		if (btflags & byteTestValueVar) > 0 {
			value = p.byteValues[value]
		}
		if btflags&byteTestNBytesVar > 0 {
			nbytes = uint8(p.byteValues[nbytes])
		}

		if btflags&byteTestDCE > 0 {
			if p.flags&ciFlagsDceLE > 0 {
				btflags |= byteTestLittle
			} else {
				btflags |= 0
			}
		}
		if btd.inspect(p, btflags, offset, int32(nbytes), value) != 1 {
			return noMatch
		}
		return matched(d, p)

	case detectByteMath:
		bmd, _ := d.data.(*byteMath)
		endian := bmd.endian
		if bmd.flags&byteMathFlagEndian > 0 && endian == endianDCE && p.flags&(ciFlagsDceLE|ciFlagsDceBE) > 0 {
			if p.flags&ciFlagsDceLE > 0 {
				endian = littleEndian
			} else {
				endian = bigEndian
			}
		}
		var rvalue uint64
		if bmd.flags&byteMathFlagRValueVar > 0 {
			rvalue = p.byteValues[bmd.rvalue]
		} else {
			rvalue = uint64(bmd.rvalue)
		}
		var nbytes uint8
		if bmd.flags&byteMathFlagNBytesVar > 0 {
			nbytes = uint8(p.byteValues[bmd.nbytes])
		} else {
			nbytes = bmd.nbytes
		}
		if bmd.inspect(p, nbytes, rvalue, &p.byteValues[bmd.localID], uint8(endian)) != 1 {
			return noMatch
		}
		return matched(d, p)
	case detectByteJump:
		bjd, _ := d.data.(*byteJump)
		bjflags := uint16(bjd.flags)
		offset := bjd.offset
		var nbytes int32

		nbytes = int32(bjd.nbytes)
		if bjflags&byteJumpDCE > 0 {
			if p.flags&ciFlagsDceLE > 0 {
				bjflags |= byteJumpLittle
			}
		}
		b := bjd.inspect(p, bjflags, offset, nbytes)
		if !b {
			return noMatch
		}
		return matched(d, p)
	case detectByteExtract:
		bed, _ := d.data.(*byteExtract)
		endian := bed.byteEndian
		if bed.flags&byteExtractFlagEndian > 0 && endian == endianDCE && p.flags&(ciFlagsDceLE|ciFlagsDceBE) > 0 {
			if p.flags&ciFlagsDceLE > 0 {
				endian = littleEndian
			} else {
				endian = bigEndian
			}
		}
		if bed.inspect(p, &p.byteValues[bed.localID], int8(endian)) != 1 {
			return noMatch
		}
		return matched(d, p)
	case detectPCRE:
		return detectPcrePayload(d, p)

	}
	return match
}

func (d *Detect) tagMpmData() {
	head := d
	var high *Detect
	for head != nil {
		if _, ok := head.data.(*absent); ok {
			return
		}
		if head.retrieveMpmPriority() == 0 {
			head = head.next
			continue
		}
		if high == nil {
			high = head
		}
		if high != nil && head.retrieveMpmPriority() > high.retrieveMpmPriority() {
			high = head
		}
		head = head.next
	}
	if high != nil {
		v := high.retrieveMpm()
		d.prfilterPayload = &v
	}
}

// for registering for  mpm  used
func (d *Detect) PrefilterData() *mpm.Content {
	return d.prfilterPayload
}

func (d *Detect) retrieveSpm() spm.Content {
	if d.detectedID == detectContent {
		dt, _ := d.data.(*content)
		c := spm.Content{}
		c.Id = d.id
		c.Content = string(dt.content)
		c.Nocase = dt.nocase
		return c
	}
	return spm.Content{}
}

func (d *Detect) retrieveMpm() mpm.Content {
	if d.detectedID == detectContent {
		dt, _ := d.data.(*content)
		c := mpm.Content{}
		c.Id = d.id
		c.Pattern = string(dt.content)
		if dt.flags&offset == offset {
			c.Offset = uint16(dt.offset)
			c.Flag |= mpm.OffsetFlag
		}
		c.Nocase = dt.nocase
		return c
	}
	return mpm.Content{}
}
func (d *Detect) RetrieveData() any {
	return d.data
}

func (d *Detect) retrieveMpmPriority() int {
	if d.detectedID == detectContent {
		dt, _ := d.data.(*content)
		l := dt.contentLen
		o := dt.offset * 10
		if dt.flags&contentNegated == contentNegated {
			return 0
		}
		return int(l + o)
	}
	return 0
}

func (d *Detect) DataPos() gonids.DataPos {
	return d.postition
}

func (d *Detect) Validate() error {
	ab, abor := false, false
	current := d
	for current != nil {
		if current.detectedID == detectAbsent {
			ab = true
			v, _ := current.data.(*absent)
			abor = v.or_else
			current = current.next
			continue
		}
		if current.detectedID == detectContent && ab && !abor {
			return fmt.Errorf("it can't have a buffer tested absent and tested with other keywords " +
				"such as content")
		}
		if current.detectedID == detectNone {
			return errors.New("detected error unknown")
		}
		current = current.next
	}

	return nil
}
func abs(n int) int {
	sign := unsafe.Sizeof(n)*8 - 1
	y := n >> sign
	return (n ^ y) - y
}

func matched(d *Detect, p *Packet) matchFlag {
	if d.next != nil {
		return detectEngineContentInspectionInternal(d.next, p)
	}
	return match
}
