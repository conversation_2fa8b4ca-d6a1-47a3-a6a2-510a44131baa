import React, { useMemo } from "react";
import { Card, Select } from "antd";
import ReactApexChart from "react-apexcharts";

const RealtimeTraffic = ({
  portData,
  selectedPort,
  setSelectedPort,
  lineChartState,
}) => {
  const portOptions = useMemo(
    () =>
      portData.map((port) => ({
        value: port.portName,
        label: port.portName,
      })),
    [portData]
  );

  return (
    <Card
      title="Realtime Traffic"
      extra={
        <Select
          style={{ width: 200 }}
          placeholder="Select port"
          value={selectedPort}
          onChange={setSelectedPort}
          options={portOptions}
        />
      }
      styles={{ body: { height: 400 } }}
      variant="borderless"
    >
      {selectedPort && (
        <ReactApexChart
          options={lineChartState.options}
          series={lineChartState.series}
          type="line"
          height={350}
        />
      )}
    </Card>
  );
};

export default React.memo(RealtimeTraffic);
