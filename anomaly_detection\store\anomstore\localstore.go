package anomstore

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"mnms/anomaly_detection/def"
	"mnms/anomaly_detection/store/algo"
	"sort"
	"sync"
)

// VectorCandidate defines vector candidate information
type VectorCandidate struct {
	ID    int64   `json:"id"`    // postgres vector id
	Text  string  `json:"text"`  // postgres vector text
	Score float32 `json:"score"` // milvus search score

}

// VectorList defines vector list
type VectorList struct {
	Items  map[int64]Vector `json:"items" mapstructure:"items"`
	NextID int64            `json:"nextID" mapstructure:"nextID"`
	mut    sync.Mutex       `json:"-" mapstructure:"-"`
}

func NewVectorList() *VectorList {
	return &VectorList{
		Items:  make(map[int64]Vector),
		NextID: 0,
		mut:    sync.Mutex{},
	}
}

var LocalVectors = VectorList{
	Items:  make(map[int64]Vector),
	NextID: 0,
	mut:    sync.Mutex{},
}

// Serialize serialize vector list
func (v *VectorList) Serialize() ([]byte, error) {

	type wrapper struct {
		Type string      `json:"type"`
		Data *VectorList `json:"data"`
	}

	// Create a wrapper that includes type information
	w := wrapper{
		Type: "VectorList",
		Data: v,
	}

	// Marshal to msgpack
	b, err := json.Marshal(w)
	if err != nil {
		return nil, err
	}

	return b, nil
}

// DeserializeVectorList deserialize vector list
func DeserializeVectorList(data []byte) (*VectorList, error) {
	// Unmarshal the data
	type wrapper struct {
		Type string      `json:"type"`
		Data *VectorList `json:"data"`
	}
	w := wrapper{}
	err := json.Unmarshal(data, &w)
	if err != nil {
		return nil, err
	}

	return w.Data, nil
}

// Clear clear vector list
func (v *VectorList) Clear() {
	v.mut.Lock()
	defer v.mut.Unlock()
	v.Items = make(map[int64]Vector)
	v.NextID = 0

}

// Mutex return mutex
func (v *VectorList) Mutex() *sync.Mutex {
	return &v.mut
}

// Add a impl Store.Add
func (v *VectorList) Add(content string, meta map[string]any, vec []float32) (int64, error) {
	var nomal = false
	normalAny, ok := meta["normal"]
	if ok {
		// try to convert to bool
		n, ok := normalAny.(bool)
		if ok {
			nomal = n
		}
	}
	var reason = ""
	reasonAny, ok := meta["reason"]
	if ok {
		// try to convert to string
		r, ok := reasonAny.(string)
		if ok {
			reason = r
		}
	}

	return v.Insert(&Vector{
		Vector: vec,
		Text:   content,
		Normal: nomal,
		Reason: reason,
	})
}

// Upsert
func (v *VectorList) Upsert(ctx context.Context, content string, meta map[string]any, vec []float32, threshold float32) (int64, error) {
	// search similar vectors
	candidates, err := v.SearchSimilar(ctx, vec, algo.CosineSimilarityCalculator{}, 1)
	if err != nil {
		return -1, err
	}

	if len(candidates) == 0 || candidates[0].Score > threshold {
		return v.Add(content, meta, vec)
	}
	// update vecotr
	id := candidates[0].ID
	record, err := v.Get(id)
	if err != nil {
		return 0, err
	}
	var nomal = false
	normalAny, ok := meta["normal"]
	if ok {
		// try to convert to bool
		n, ok := normalAny.(bool)
		if ok {
			nomal = n
		}
	}
	var reason = ""
	reasonAny, ok := meta["reason"]
	if ok {
		// try to convert to string
		r, ok := reasonAny.(string)
		if ok {
			reason = r
		}
	}
	record.Normal = nomal
	record.Reason = reason

	return v.Update(id, record)

}

// GetContent return doc with id
func (v *VectorList) GetContent(id int64) (string, error) {

	if record, ok := v.Items[id]; ok {
		return record.Text, nil
	}
	return "", fmt.Errorf("Try to get vector record but record is not exists")
}

// GetMeta return meta with id
func (v *VectorList) GetMeta(id int64) (map[string]any, error) {
	if record, ok := v.Items[id]; ok {
		return map[string]any{
			"normal": record.Normal,
			"reason": record.Reason,
		}, nil
	}
	return nil, fmt.Errorf("Try to get vector record but record is not exists")
}

// Insert insert vector record, returns inserted id
func (v *VectorList) Insert(record *Vector) (int64, error) {
	if record == nil {
		return -1, fmt.Errorf("Try to insert vector record but record is nil")
	}
	v.mut.Lock()
	defer v.mut.Unlock()

	// Add new record
	// check nextID is exists add nextid until not exists
	for {
		if _, ok := v.Items[v.NextID]; !ok {
			break
		}
		v.NextID++
	}
	insertID := v.NextID
	v.NextID++

	v.Items[insertID] = *record

	return insertID, nil
}

// Update update vector record, returns updated id
func (v *VectorList) Update(id int64, record *Vector) (int64, error) {
	if record == nil {
		return -1, fmt.Errorf("Try to update vector record but record is nil")
	}

	// Update record
	v.mut.Lock()
	v.Items[id] = *record
	v.mut.Unlock()

	return id, nil
}

// SetValue set value
func (v *VectorList) SetValue(id int64, normal bool, reason string) error {
	if _, ok := v.Items[id]; !ok {
		return fmt.Errorf("Try to set vector record value but record is not exists")
	}
	v.mut.Lock()
	vec := v.Items[id]
	vec.Normal = normal
	vec.Reason = reason
	v.Items[id] = vec
	v.mut.Unlock()
	return nil
}

// Delete
func (v *VectorList) Delete(id int64) error {
	if _, ok := v.Items[id]; !ok {
		return fmt.Errorf("Try to delete vector record but record is not exists")
	}
	v.mut.Lock()
	delete(v.Items, id)
	v.mut.Unlock()
	return nil
}

// Get
func (v *VectorList) Get(id int64) (*Vector, error) {
	if record, ok := v.Items[id]; ok {
		return &record, nil
	}
	return nil, fmt.Errorf("Try to get vector record but record is not exists")
}

// GetIDs
func (v *VectorList) GetIDs(ids []int64) ([]*Vector, error) {
	var records = make([]*Vector, 0)
	for _, id := range ids {
		if record, ok := v.Items[id]; ok {
			records = append(records, &record)
		}
	}
	return records, nil
}

type ByAscDistance []*VectorCandidate

func (a ByAscDistance) Len() int           { return len(a) }
func (a ByAscDistance) Swap(i, j int)      { a[i], a[j] = a[j], a[i] }
func (a ByAscDistance) Less(i, j int) bool { return a[i].Score < a[j].Score }

type ByDescDistance []*VectorCandidate

func (a ByDescDistance) Len() int           { return len(a) }
func (a ByDescDistance) Swap(i, j int)      { a[i], a[j] = a[j], a[i] }
func (a ByDescDistance) Less(i, j int) bool { return a[i].Score > a[j].Score }

// Search impl Store.Search
func (v *VectorList) Search(ctx context.Context, vec []float32, n int) ([]def.Document, error) {
	candidates, err := v.SearchSimilar(ctx, vec, algo.CosineSimilarityCalculator{}, n)
	if err != nil {
		return nil, err
	}
	docs := make([]def.Document, len(candidates))

	for i, c := range candidates {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()

		default:
			item, err := v.Get(c.ID)
			if err != nil {
				return nil, err
			}
			docs[i] = def.Document{
				Content: c.Text,
				Score:   c.Score,
				Metadata: map[string]any{
					"reason": item.Reason,
					"normal": item.Normal,
				},
			}
		}
	}
	return docs, nil
}

func normalize(vector []float32) []float32 {
	var norm float32 = 0.0
	for _, v := range vector {
		norm += v * v
	}
	norm = float32(math.Sqrt(float64(norm)))

	normalizedVector := make([]float32, len(vector))
	for i, v := range vector {
		normalizedVector[i] = v / norm
	}
	return normalizedVector
}

// SearchSimilar find similar report
func (v *VectorList) SearchSimilar(ctx context.Context, vec []float32, f algo.DistanceCalculator, count int) ([]*VectorCandidate, error) {
	if count <= 0 {
		return nil, fmt.Errorf("count should be greater than 0")
	}
	if count > len(v.Items) {
		count = len(v.Items)
	}
	var candidates = make([]*VectorCandidate, 0)

	// Create a channel to receive results from the goroutine
	resultChan := make(chan []*VectorCandidate)
	errChan := make(chan error)

	// Launch the search in a separate goroutine
	go func() {
		defer close(resultChan)
		for idx, item := range v.Items {
			// Check if the context is canceled
			select {
			case <-ctx.Done():
				errChan <- ctx.Err()
				return // Exit the goroutine
			default:
				// Continue with the search
			}

			distance := f.Calculate(vec, item.Vector)
			candidate := &VectorCandidate{
				ID:    idx,
				Score: distance,
				Text:  item.Text,
			}

			candidates = append(candidates, candidate)
		}
		errChan <- nil // Signal that the search completed successfully
	}()

	// Wait for either the search to complete or the context to be cancelled
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	case err := <-errChan:
		if err != nil {
			return nil, err
		}
	}

	// Sort the candidates by score and return the top 'count' candidates
	sort.Slice(candidates, func(i, j int) bool {
		return candidates[i].Score < candidates[j].Score
	})

	if len(candidates) > count {
		candidates = candidates[:count]
	}

	return candidates, nil
}

// Operations of LocalVectorRecords
// UpdateLocalVectorRecords update LocalVectorRecords
func UpdateLocalVectorRecords(record *Vector) error {
	if record == nil {
		return fmt.Errorf("Try to update vector record but record is nil")
	}
	// Search if the record exists
	return fmt.Errorf("not implemented")
}

// CalDistanceWithLocalVectors search milvus and get most similar vectors with specific vector (pgvector version)
func CalDistanceWithLocalVectors(ctx context.Context, vector []float32, count int) ([]*VectorCandidate, error) {
	return LocalVectors.SearchSimilar(ctx, vector, algo.CosineSimilarityCalculator{}, count)
}

// sortedMapKeys return sorted keys
func sortedMapKeys(m map[int64]Vector) []int64 {
	keys := make([]int64, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	sort.Slice(keys, func(i, j int) bool { return keys[i] < keys[j] })
	return keys
}
