import { App, Checkbox, Form, Input, Modal } from "antd";
import React, { useCallback, useEffect } from "react";
import PropTypes from "prop-types";
import { useSendCommand } from "../../../services/mutations";
import { handleCommandExecution } from "../../../utils/command-execution";
import {
  FORM_FIELDS,
  VALIDATION_RULES,
} from "../../../constants/network-settings";
import { generateCommand } from "../../../utils/generate-commands";

const NetworkSettingDialog = ({ data, onClose }) => {
  const { notification } = App.useApp();
  const sendCommand = useSendCommand();
  const [form] = Form.useForm();

  const handleSubmit = useCallback(
    (values) => {
      const command = generateCommand(
        data.mac,
        data.ipaddress,
        values,
        "network"
      );
      handleCommandExecution(command, sendCommand, notification);
      onClose();
    },
    [data.mac, data.ipaddress, sendCommand, notification, onClose]
  );

  useEffect(() => {
    form.setFieldsValue({
      [FORM_FIELDS.DHCP]: data.isdhcp,
      [FORM_FIELDS.IP_ADDRESS]: data.ipaddress,
      [FORM_FIELDS.NETMASK]: data.netmask,
      [FORM_FIELDS.GATEWAY]: data.gateway,
      [FORM_FIELDS.HOSTNAME]: data.hostname,
    });
  }, [form, data]);

  return (
    <Modal
      title="Network Setting"
      maskClosable={false}
      open
      destroyOnClose
      onCancel={onClose}
      okButtonProps={{ autoFocus: true, htmlType: "submit" }}
      style={{ top: 20 }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            handleSubmit(values);
            form.resetFields();
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form
        form={form}
        layout="vertical"
        name="network_setting_form"
        clearOnDestroy
      >
        <Form.Item name={FORM_FIELDS.DHCP} valuePropName="checked">
          <Checkbox>DHCP</Checkbox>
        </Form.Item>

        {Object.entries(VALIDATION_RULES).map(([field, rules]) => (
          <Form.Item
            key={field}
            name={field}
            label={field
              .split("_")
              .map(
                (word) =>
                  word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
              )
              .join(" ")}
            rules={rules}
          >
            <Input />
          </Form.Item>
        ))}
      </Form>
    </Modal>
  );
};

NetworkSettingDialog.propTypes = {
  data: PropTypes.shape({
    isdhcp: PropTypes.bool.isRequired,
    ipaddress: PropTypes.string.isRequired,
    netmask: PropTypes.string.isRequired,
    gateway: PropTypes.string.isRequired,
    hostname: PropTypes.string.isRequired,
    mac: PropTypes.string.isRequired,
  }).isRequired,
  onClose: PropTypes.func.isRequired,
};

export default React.memo(NetworkSettingDialog);
