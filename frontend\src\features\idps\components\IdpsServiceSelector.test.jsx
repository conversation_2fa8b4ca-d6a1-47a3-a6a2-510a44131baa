import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import { App } from "antd";
import IdpsServiceSelector from "./IdpsServiceSelector";

// Mock dayjs
vi.mock("dayjs", () => {
  const mockDayjs = vi.fn(() => ({
    format: vi.fn(() => "2024-01-01 12:00:00"),
  }));
  return { default: mockDayjs };
});

describe("IdpsServiceSelector", () => {
  const mockServices = ["sasi_idpsvc", "sasi_idpsvc1"];
  const mockSelectedServiceData = {
    start_time: "2024-01-01T12:00:00Z",
    rules: [],
    event: [],
  };

  const mockProps = {
    services: mockServices,
    selectedService: "sasi_idpsvc",
    onSelectService: vi.fn(),
    onRefresh: vi.fn(),
    onImportRules: vi.fn(),
    onAddRules: vi.fn(),
    selectedServiceData: mockSelectedServiceData,
    loading: false,
    isImporting: false,
    isAdding: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders service selector with services", () => {
    render(
      <App>
        <IdpsServiceSelector {...mockProps} />
      </App>
    );

    expect(screen.getByText("IDPS Service")).toBeInTheDocument();
    expect(screen.getByText("Active: sasi_idpsvc")).toBeInTheDocument();
    expect(screen.getByText("Refresh")).toBeInTheDocument();
    expect(screen.getByText("Add Rules")).toBeInTheDocument();
    expect(screen.getByText("Import Rules")).toBeInTheDocument();
  });

  it("shows start time when available", () => {
    render(
      <App>
        <IdpsServiceSelector {...mockProps} />
      </App>
    );

    expect(
      screen.getByText("Started: 2024-01-01 12:00:00")
    ).toBeInTheDocument();
  });

  it("opens add rules modal when add rules button is clicked", () => {
    render(
      <App>
        <IdpsServiceSelector {...mockProps} />
      </App>
    );

    const addButton = screen.getByRole("button", { name: /add rules/i });
    fireEvent.click(addButton);

    expect(screen.getByPlaceholderText("Enter rule name")).toBeInTheDocument();
    expect(
      screen.getByPlaceholderText("Enter rules content")
    ).toBeInTheDocument();
  });

  it("opens import modal when import button is clicked", () => {
    render(
      <App>
        <IdpsServiceSelector {...mockProps} />
      </App>
    );

    const importButton = screen.getByRole("button", { name: /import rules/i });
    fireEvent.click(importButton);

    expect(screen.getByPlaceholderText("Enter rules URL")).toBeInTheDocument();
  });

  it("calls onRefresh when refresh button is clicked", () => {
    render(
      <App>
        <IdpsServiceSelector {...mockProps} />
      </App>
    );

    const refreshButton = screen.getByText("Refresh");
    fireEvent.click(refreshButton);

    expect(mockProps.onRefresh).toHaveBeenCalledTimes(1);
  });

  it("calls onAddRules when add rules form is submitted", () => {
    render(
      <App>
        <IdpsServiceSelector {...mockProps} />
      </App>
    );

    // Open add rules modal
    const addButton = screen.getByRole("button", { name: /add rules/i });
    fireEvent.click(addButton);

    // Fill in the form
    const ruleNameInput = screen.getByPlaceholderText("Enter rule name");
    const rulesContentInput = screen.getByPlaceholderText(
      "Enter rules content"
    );

    fireEvent.change(ruleNameInput, { target: { value: "test-rule" } });
    fireEvent.change(rulesContentInput, {
      target: { value: "test rules content" },
    });

    // Submit the form
    const submitButton = screen.getByRole("button", { name: "OK" });
    fireEvent.click(submitButton);

    expect(mockProps.onAddRules).toHaveBeenCalledWith(
      "test-rule",
      "test rules content",
      "sasi_idpsvc"
    );
  });

  it("handles loading state", () => {
    render(
      <App>
        <IdpsServiceSelector {...mockProps} loading={true} />
      </App>
    );

    expect(screen.getByText("IDPS Service")).toBeInTheDocument();
  });

  it("handles adding state", () => {
    render(
      <App>
        <IdpsServiceSelector {...mockProps} isAdding={true} />
      </App>
    );

    expect(screen.getByText("IDPS Service")).toBeInTheDocument();
  });
});
