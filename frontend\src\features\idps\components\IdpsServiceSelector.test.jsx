import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import { App } from "antd";
import IdpsServiceSelector from "./IdpsServiceSelector";

// Mock dayjs
vi.mock("dayjs", () => {
  const mockDayjs = vi.fn(() => ({
    format: vi.fn(() => "2024-01-01 12:00:00"),
  }));
  return { default: mockDayjs };
});

describe("IdpsServiceSelector", () => {
  const mockServices = ["sasi_idpsvc", "sasi_idpsvc1"];
  const mockSelectedServiceData = {
    start_time: "2024-01-01T12:00:00Z",
    rules: [],
    event: [],
  };

  const mockProps = {
    services: mockServices,
    selectedService: "sasi_idpsvc",
    onSelectService: vi.fn(),
    onRefresh: vi.fn(),
    onImportRules: vi.fn(),
    selectedServiceData: mockSelectedServiceData,
    loading: false,
    isImporting: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders service selector with services", () => {
    render(
      <App>
        <IdpsServiceSelector {...mockProps} />
      </App>
    );

    expect(screen.getByText("IDPS Service")).toBeInTheDocument();
    expect(screen.getByText("Active: sasi_idpsvc")).toBeInTheDocument();
    expect(screen.getByText("Refresh")).toBeInTheDocument();
    expect(screen.getByText("Import Rules")).toBeInTheDocument();
  });

  it("shows start time when available", () => {
    render(
      <App>
        <IdpsServiceSelector {...mockProps} />
      </App>
    );

    expect(screen.getByText("Started: 2024-01-01 12:00:00")).toBeInTheDocument();
  });

  it("opens import modal when import button is clicked", () => {
    render(
      <App>
        <IdpsServiceSelector {...mockProps} />
      </App>
    );

    const importButton = screen.getByText("Import Rules");
    fireEvent.click(importButton);

    expect(screen.getByText("Import Rules")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Enter rules URL")).toBeInTheDocument();
  });

  it("calls onRefresh when refresh button is clicked", () => {
    render(
      <App>
        <IdpsServiceSelector {...mockProps} />
      </App>
    );

    const refreshButton = screen.getByText("Refresh");
    fireEvent.click(refreshButton);

    expect(mockProps.onRefresh).toHaveBeenCalledTimes(1);
  });

  it("handles loading state", () => {
    render(
      <App>
        <IdpsServiceSelector {...mockProps} loading={true} />
      </App>
    );

    expect(screen.getByText("IDPS Service")).toBeInTheDocument();
  });
});
