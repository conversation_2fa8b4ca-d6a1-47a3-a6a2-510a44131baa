import { useQuery } from "@tanstack/react-query";
import { api } from "./api";
import { filteredTopologyData } from "../utils/topologyUtils";

/**
 * Custom hook to check server status
 * @param {string} baseURL - Base URL of the server
 * @returns {UseQueryResult} Query result with server status
 */
export const useGetServerStatus = (baseURL) => {
  return useQuery({
    queryKey: ["server-status", baseURL],
    queryFn: () => api.getServerStatus(),
    retry: false,
    refetchOnWindowFocus: true,
    refetchInterval: 10000,
    refetchIntervalInBackground: false,
    staleTime: 5000,
  });
};

/**
 * Custom hook to fetch network devices
 * @param {"device" | "motor-ctrl-card"} invType - Device type to filter
 * @returns {UseQueryResult} Query result with filtered devices
 */
export const useGetDevices = (invType) => {
  return useQuery({
    queryKey: ["devices", invType],
    queryFn: () => api.getDevices(),
    select: (data) => filterDevices(data, invType),
    staleTime: 30000,
    cacheTime: 300000, // 5 minutes
  });
};

export const useGetPortInfo = (mac) => {
  return useQuery({
    queryKey: ["port-info", mac],
    queryFn: () => api.getPortInfo(mac),
    enabled: !!mac,
    retry: false,
    refetchOnWindowFocus: true,
    refetchInterval: 10000,
    refetchIntervalInBackground: false,
    staleTime: 5000,
  });
};

export const useGetTopologyData = (serviceType) => {
  return useQuery({
    queryKey: ["topology", serviceType],
    queryFn: () => api.getTopologyData(),
    select: (data) => {
      const services = [
        ...new Set(Object.values(data).map((device) => device.services)),
      ].sort();

      const topoData =
        serviceType === "All Network Services"
          ? Object.values(data)
          : Object.values(data).filter((item) => item.services === serviceType);

      return {
        services: ["All Network Services", ...services],
        ...filteredTopologyData(topoData, serviceType),
      };
    },
    staleTime: 30000,
    cacheTime: 300000, // 5 minutes
  });
};

/**
 * Custom hook to fetch all commands
 * @returns {UseQueryResult} Query result with all commands
 */
export const useGetAllCommands = () => {
  return useQuery({
    queryKey: ["command"],
    queryFn: () => api.getAllCommads(),
    select: (data) => Object.values(data),
    ...QUERY_CONFIG.DEVICES,
  });
};

/**
 * Custom hook to send command to a device
 * @param {Object} command - Command data
 * @param {boolean} [enabled=true] - Whether the query is enabled
 * @returns {UseMutationResult} Mutation result for sending command
 */
export const useGetCommandResult = (command) => {
  return useQuery({
    queryKey: ["command-result", command],
    queryFn: () => api.getCommandResult(command),
    select: (data) => Object.values(data)[0],
    enabled: !!command,
    retry: false,
    refetchOnWindowFocus: true,
    refetchInterval: (data) => {
      // Stop refetching if status is "ok" or starts with "error"
      if (!data || !data.state || !data.state.data) {
        return 5000; // Continue refetching every 5 seconds
      }
      const status = Object.values(data.state.data)[0]?.status;
      if (status === "ok" || (status && status.startsWith("error:"))) {
        return false; // Disable refetching
      }
      return 5000; // Continue refetching every 5 seconds
    },
    refetchIntervalInBackground: false,
    staleTime: 5000,
  });
};

/**
 * Custom hook to fetch all users
 * @returns {UseQueryResult} Query result with all users
 */
export const useGetAllUsers = () => {
  return useQuery({
    queryKey: ["users"],
    queryFn: () => api.getAllUsers(),
    select: (data) => data,
    ...QUERY_CONFIG.DEVICES,
  });
};

/**
 * Custom hook to fetch all users
 * @returns {UseQueryResult} Query result with all users
 */
export const useGetSyslogs = (params) => {
  return useQuery({
    queryKey: ["syslogs", params],
    queryFn: () => api.getSyslogs(params),
    select: (data) => data,

    ...QUERY_CONFIG.DEVICES,
  });
};

/**
 * Custom hook to fetch root information
 * @returns {UseQueryResult} Query result with root information
 */
export const useGetRootInfo = () => {
  return useQuery({
    queryKey: ["root-info"],
    queryFn: () => api.getRootInfo(),
    select: (data) => ({
      ...data,
      anomaly_license: data.license.featureAnomalyDetection,
      idps_license: data.license.featureIdps,
      licensePath: data.license.path,
      maxClients: data.license.numClients,
      maxDevices: data.license.numOfDevice,
    }),
    ...QUERY_CONFIG.DEVICES,
  });
};

/**
 * Custom hook to fetch cluster information
 * @returns {UseQueryResult} Query result with cluster information
 */
export const useGetClusterInfo = () => {
  return useQuery({
    queryKey: ["cluster-info"],
    queryFn: () => api.getClusterInfo(),
    select: (data) => data,
    ...QUERY_CONFIG.DEVICES,
  });
};

export const useGetSSHTunnels = () => {
  return useQuery({
    queryKey: ["ssh-tunnels"],
    queryFn: () => api.getSSHTunnels(),
    select: (data) => data,
    ...QUERY_CONFIG.DEVICES,
  });
};

/**
 * Custom hook to fetch KV Store data
 * @returns {UseQueryResult} Query result with KV Store data
 */
export const useGetKVStore = () => {
  return useQuery({
    queryKey: ["kv-store"],
    queryFn: () => api.exportKvStore(),
    select: (data) => data,
    ...QUERY_CONFIG.DEVICES,
  });
};

export const useGetSyslogAlert = () => {
  return useQuery({
    queryKey: ["syslog-alert"],
    queryFn: () => api.getSyslogAlert(),
    select: (data) => data,
    ...QUERY_CONFIG.DEVICES,
  });
};

/**
 * Filter devices based on type
 * @param {Object} data - Raw devices data
 * @param {string} invType - Device type to filter
 * @returns {Array} Filtered devices
 */
const filterDevices = (data, invType) => {
  const EXCLUDED_MAC = "11-22-33-44-55-66";
  const MOTOR_CTRL_TYPE = "motor-ctrl-card";

  return Object.values(data).filter((item) => {
    if (item.mac === EXCLUDED_MAC) return false;
    return invType === "device"
      ? item.type !== MOTOR_CTRL_TYPE
      : item.type === MOTOR_CTRL_TYPE;
  });
};

/**
 * Constants for query configuration
 */
export const QUERY_CONFIG = {
  SERVER_STATUS: {
    refetchInterval: 10000,
    staleTime: 5000,
  },
  DEVICES: {
    staleTime: 30000,
    cacheTime: 300000,
  },
};
