import React, { memo, useMemo, useState } from "react";
import {
  Card,
  Table,
  Tag,
  Space,
  Button,
  Typography,
  Tooltip,
  Modal,
  Input,
  DatePicker,
} from "antd";
import {
  ReloadOutlined,
  AlertOutlined,
  StopOutlined,
  FilterOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";

const { RangePicker } = DatePicker;

const { Text } = Typography;

/**
 * IDPS Events Table Component
 * Displays IDPS events in a table format
 * @param {Object} props Component props
 * @param {Array} props.data Events data
 * @param {boolean} props.loading Loading state
 * @param {Function} props.onRefresh Refresh callback
 * @param {Function} props.onFilterEvents Filter events callback
 * @param {string} props.selectedService Selected service name
 * @returns {JSX.Element} Events table component
 */
const IdpsEventsTable = ({
  data = [],
  loading = false,
  onRefresh,
  onFilterEvents,
  selectedService,
}) => {
  const [filterModalVisible, setFilterModalVisible] = useState(false);
  const [eventFilterFileName, setEventFilterFileName] = useState("");
  const [eventFilterDateRange, setEventFilterDateRange] = useState([
    dayjs(),
    dayjs(),
  ]); // Default to today
  const columns = useMemo(
    () => [
      {
        title: "ID",
        dataIndex: "id",
        key: "id",
        width: 80,
        sorter: (a, b) => a.id - b.id,
      },
      {
        title: "Timestamp",
        dataIndex: "timestamp",
        key: "timestamp",
        width: 160,
        render: (text) => <Text>{dayjs(text).format("MM-DD HH:mm:ss")}</Text>,
        sorter: (a, b) => dayjs(a.timestamp).unix() - dayjs(b.timestamp).unix(),
        defaultSortOrder: "descend",
      },
      {
        title: "Type",
        dataIndex: "type",
        key: "type",
        width: 80,
        render: (type) => {
          const config = {
            alert: { color: "orange", icon: <AlertOutlined /> },
            drop: { color: "red", icon: <StopOutlined /> },
            accept: { color: "green", icon: null },
          };
          const typeConfig = config[type] || { color: "default", icon: null };

          return (
            <Tag color={typeConfig.color} icon={typeConfig.icon}>
              {type?.toUpperCase()}
            </Tag>
          );
        },
        filters: [
          { text: "Alert", value: "alert" },
          { text: "Drop", value: "drop" },
          { text: "Accept", value: "accept" },
        ],
        onFilter: (value, record) => record.type === value,
      },
      {
        title: "Source",
        key: "source",
        width: 140,
        render: (_, record) => (
          <div>
            <Text style={{ fontSize: "12px" }}>
              {record.srcip}:{record.srcPort}
            </Text>
          </div>
        ),
      },
      {
        title: "Destination",
        key: "destination",
        width: 140,
        render: (_, record) => (
          <div>
            <Text style={{ fontSize: "12px" }}>
              {record.destip}:{record.destPort}
            </Text>
          </div>
        ),
      },
      {
        title: "Protocol",
        dataIndex: "protocol",
        key: "protocol",
        width: 80,
        render: (protocol) => <Tag color="blue">{protocol?.toUpperCase()}</Tag>,
        filters: [
          { text: "TCP", value: "TCP" },
          { text: "UDP", value: "UDP" },
          { text: "ICMP", value: "ICMP" },
        ],
        onFilter: (value, record) => record.protocol?.toUpperCase() === value,
      },
      {
        title: "Interface",
        dataIndex: "inInterface",
        key: "inInterface",
        width: 120,
        ellipsis: {
          showTitle: false,
        },
        render: (text) => (
          <Tooltip placement="topLeft" title={text}>
            <Text style={{ fontSize: "12px" }}>{text}</Text>
          </Tooltip>
        ),
      },
      {
        title: "Description",
        dataIndex: "description",
        key: "description",
        ellipsis: {
          showTitle: false,
        },
        render: (text) => (
          <Tooltip placement="topLeft" title={text}>
            <Text style={{ fontSize: "12px" }}>{text}</Text>
          </Tooltip>
        ),
      },
    ],
    []
  );

  const handleFilterEvents = () => {
    if (
      onFilterEvents &&
      eventFilterFileName &&
      eventFilterDateRange &&
      eventFilterDateRange[0] &&
      eventFilterDateRange[1]
    ) {
      const startDate = eventFilterDateRange[0].format("YYYY-MM-DD");
      const endDate = eventFilterDateRange[1].format("YYYY-MM-DD");
      onFilterEvents(eventFilterFileName, startDate, endDate, selectedService);
      setFilterModalVisible(false);
    }
  };

  const openFilterModal = () => {
    setFilterModalVisible(true);
  };

  return (
    <Card
      title={
        <Space>
          <Text strong>Events</Text>
          {selectedService && <Tag color="blue">{selectedService}</Tag>}
          <Tag color="green">{data.length} events</Tag>
        </Space>
      }
      extra={
        <Space>
          <Button
            icon={<FilterOutlined />}
            onClick={openFilterModal}
            size="small"
          >
            Filter Events
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={onRefresh}
            loading={loading}
            size="small"
          >
            Refresh
          </Button>
        </Space>
      }
      variant="borderless"
    >
      <Table
        columns={columns}
        dataSource={data}
        rowKey="id"
        loading={loading}
        size="small"
        pagination={{
          pageSize: 15,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} of ${total} events`,
        }}
        scroll={{ x: 1000 }}
      />

      <Modal
        title="Filter Events"
        open={filterModalVisible}
        onOk={handleFilterEvents}
        onCancel={() => setFilterModalVisible(false)}
        okText="Filter"
        okButtonProps={{
          disabled:
            !eventFilterFileName ||
            !eventFilterDateRange ||
            !eventFilterDateRange[0] ||
            !eventFilterDateRange[1],
        }}
      >
        <Space direction="vertical" style={{ width: "100%" }}>
          <div>
            <Text strong>File Name:</Text>
            <Input
              value={eventFilterFileName}
              onChange={(e) => setEventFilterFileName(e.target.value)}
              placeholder="Enter file name to filter"
              style={{ marginTop: "8px" }}
            />
          </div>

          <div>
            <Text strong>Date Range:</Text>
            <RangePicker
              value={eventFilterDateRange}
              onChange={setEventFilterDateRange}
              format="YYYY-MM-DD"
              style={{ width: "100%", marginTop: "8px" }}
            />
          </div>

          <div
            style={{
              marginTop: "16px",
              padding: "8px",
              backgroundColor: "#f5f5f5",
              borderRadius: "4px",
            }}
          >
            <Text type="secondary" style={{ fontSize: "12px" }}>
              Command:{" "}
              {eventFilterFileName &&
              eventFilterDateRange &&
              eventFilterDateRange[0] &&
              eventFilterDateRange[1]
                ? `idps records search -f ${eventFilterFileName} -st ${eventFilterDateRange[0].format("YYYY-MM-DD")}-00:00 -et ${eventFilterDateRange[1].format("YYYY-MM-DD")}-23:59`
                : "idps records search -f [filename] -st [start_date]-00:00 -et [end_date]-23:59"}
            </Text>
          </div>
        </Space>
      </Modal>
    </Card>
  );
};

export default memo(IdpsEventsTable);
