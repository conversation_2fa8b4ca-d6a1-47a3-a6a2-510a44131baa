import React, { memo, useMemo } from "react";
import { Card, Table, Tag, Space, Button, Typography, Tooltip } from "antd";
import { ReloadOutlined, AlertOutlined, StopOutlined } from "@ant-design/icons";
import dayjs from "dayjs";

const { Text } = Typography;

/**
 * IDPS Events Table Component
 * Displays IDPS events in a table format
 * @param {Object} props Component props
 * @param {Array} props.data Events data
 * @param {boolean} props.loading Loading state
 * @param {Function} props.onRefresh Refresh callback
 * @param {string} props.selectedService Selected service name
 * @returns {JSX.Element} Events table component
 */
const IdpsEventsTable = ({
  data = [],
  loading = false,
  onRefresh,
  selectedService,
}) => {
  const columns = useMemo(
    () => [
      {
        title: "ID",
        dataIndex: "id",
        key: "id",
        width: 80,
        sorter: (a, b) => a.id - b.id,
      },
      {
        title: "Timestamp",
        dataIndex: "timestamp",
        key: "timestamp",
        width: 160,
        render: (text) => (
          <Text>{dayjs(text).format("MM-DD HH:mm:ss")}</Text>
        ),
        sorter: (a, b) => dayjs(a.timestamp).unix() - dayjs(b.timestamp).unix(),
        defaultSortOrder: "descend",
      },
      {
        title: "Type",
        dataIndex: "type",
        key: "type",
        width: 80,
        render: (type) => {
          const config = {
            alert: { color: "orange", icon: <AlertOutlined /> },
            drop: { color: "red", icon: <StopOutlined /> },
            accept: { color: "green", icon: null },
          };
          const typeConfig = config[type] || { color: "default", icon: null };
          
          return (
            <Tag color={typeConfig.color} icon={typeConfig.icon}>
              {type?.toUpperCase()}
            </Tag>
          );
        },
        filters: [
          { text: "Alert", value: "alert" },
          { text: "Drop", value: "drop" },
          { text: "Accept", value: "accept" },
        ],
        onFilter: (value, record) => record.type === value,
      },
      {
        title: "Source",
        key: "source",
        width: 140,
        render: (_, record) => (
          <div>
            <Text style={{ fontSize: "12px" }}>
              {record.srcip}:{record.srcPort}
            </Text>
          </div>
        ),
      },
      {
        title: "Destination",
        key: "destination",
        width: 140,
        render: (_, record) => (
          <div>
            <Text style={{ fontSize: "12px" }}>
              {record.destip}:{record.destPort}
            </Text>
          </div>
        ),
      },
      {
        title: "Protocol",
        dataIndex: "protocol",
        key: "protocol",
        width: 80,
        render: (protocol) => (
          <Tag color="blue">{protocol?.toUpperCase()}</Tag>
        ),
        filters: [
          { text: "TCP", value: "TCP" },
          { text: "UDP", value: "UDP" },
          { text: "ICMP", value: "ICMP" },
        ],
        onFilter: (value, record) => record.protocol?.toUpperCase() === value,
      },
      {
        title: "Interface",
        dataIndex: "inInterface",
        key: "inInterface",
        width: 120,
        ellipsis: {
          showTitle: false,
        },
        render: (text) => (
          <Tooltip placement="topLeft" title={text}>
            <Text style={{ fontSize: "12px" }}>{text}</Text>
          </Tooltip>
        ),
      },
      {
        title: "Description",
        dataIndex: "description",
        key: "description",
        ellipsis: {
          showTitle: false,
        },
        render: (text) => (
          <Tooltip placement="topLeft" title={text}>
            <Text style={{ fontSize: "12px" }}>{text}</Text>
          </Tooltip>
        ),
      },
    ],
    []
  );

  return (
    <Card
      title={
        <Space>
          <Text strong>Events</Text>
          {selectedService && (
            <Tag color="blue">{selectedService}</Tag>
          )}
          <Tag color="green">{data.length} events</Tag>
        </Space>
      }
      extra={
        <Button
          icon={<ReloadOutlined />}
          onClick={onRefresh}
          loading={loading}
          size="small"
        >
          Refresh
        </Button>
      }
      variant="borderless"
    >
      <Table
        columns={columns}
        dataSource={data}
        rowKey="id"
        loading={loading}
        size="small"
        pagination={{
          pageSize: 15,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} of ${total} events`,
        }}
        scroll={{ x: 1000 }}
      />
    </Card>
  );
};

export default memo(IdpsEventsTable);
