package project

import (
	"context"
	"encoding/json"
	"mnms/aiagent/flow"
	"mnms/aiagent/node"
	"mnms/llm"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestingProject
func TestProject(t *testing.T) {
	llmsettings := llm.OllamaSettings("llama3.2:3b")
	proj, _ := NewProject(llmsettings, "test-project", "test project is used for testing")

	schemaText := `{
		"$schema": "https://json-schema.org/draft/2020-12/schema",
		"type": "object",
		"$id": "https://example.com/person.schema.json",
		"title": "document-reference-descriptions",
		"description": "match queries to the specific document",
		"properties": {
			"queries": {
				"description": "queries, descriptions that related to the specific document",
				"type": "array",
				"items": {
					"type": "string"	
				}
			}
		},
		"required": ["queries"]
	}`

	var _schema llm.JSONSchemaDefinition
	err := json.Unmarshal([]byte(schemaText), &_schema)
	if err != nil {
		t.Fatal(err)
	}

	n := node.Node{
		Name:         "test-structed-output",
		OutputFormat: "json",
		SystemPrompt: "You are a indexing expert, you're job is read the input and write some queries or descriptions that related to the input.Try using different descriptions to increase the likelihood of semantic searches being more accurate and relevant. ",
		JSONSchema:   _schema,
	}
	// proj.Nodes = append(proj.Nodes, n)
	// ret, err := proj.RunNode(context.Background(), "test-structed-output", "Alan is a 35 years old, he is good for coding, singing and dancing.")
	// if err != nil {
	// 	t.Fatal(err)
	// }
	// t.Log(ret)
	// Testing open-ai
	llmsettings = llm.GPTSettings()
	llmsettings.Model = "gpt-4o"
	proj, err = NewProject(llmsettings, "test-project", "test project is used for testing")
	if err != nil {
		t.Fatal(err)
	}

	f := flow.NewFlow("test-flow", "test flow")
	f.AddNode(n)

	proj.UpsertFlow(f)
	ret, err := proj.RunNodeWithNodeName(context.Background(), "test-flow", "test-structed-output",
		`
Puck is a modular, open-source visual editor for React.js. You can use Puck to build custom drag-and-drop experiences with your own application and React components.

Because Puck is just a React component, it plays well with all React.js environments, including Next.js. You own your data and there’s no vendor lock-in.

Puck is also licensed under MIT, making it suitable for both internal systems and commercial applications.
`)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(ret)
}

// TestNodeExportImport tests the node export and import
func TestNodeExportImport(t *testing.T) {
	n := node.Node{
		Name:         "test-export",
		OutputFormat: "text",
		SystemPrompt: "Translate the input text to english, if the input text is already in english, check the grammar and spelling.",
	}
	_, err := n.Export()
	assert.NoError(t, err)

	n2, err := node.ImportNode("test-export")
	assert.NoError(t, err)
	info2 := n2.Info()
	assert.Equal(t, info2.Name, n.Name)

}

//
