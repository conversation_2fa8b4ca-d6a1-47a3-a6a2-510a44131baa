package utils

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"mnms/llm"

	"mnms/anomaly_detection/statistic"
	"mnms/encrypt"

	"github.com/qeof/q"
)

// EmbeddingRequest send to "http://api.openai.com/v1/some_endpoint"

var OpenAIEmbeddingsURL = "https://api.openai.com/v1/embeddings"
var OpenAICompletionURL = "https://api.openai.com/v1/chat/completions"

// default api key

var demoAPIKeyCount = 0
var demoAPIKeyLimit = 50

type EmbeddingResponseData struct {
	Object    string    `json:"object"`
	Embedding []float32 `json:"embedding"`
	Index     int       `json:"index"`
}
type EmbeddingResponse struct {
	Object string `json:"object"`

	Data  []EmbeddingResponseData `json:"data"`
	Model string                  `json:"model"`
	Usage map[string]int          `json:"usage"`
}

func IsDemoAPIKey() bool {
	preference := statistic.LocalStatistic.Settings.OpenAISettings
	demokey, _ := encrypt.DecryptedKey(statistic.DemoAPIKey)

	if preference.APIKey == demokey {

		return true
	}
	return false
}

// EmbeddingRequest send to "http://api.openai.com/v1/some_endpoint"
func EmbeddingRequest(text string) (*EmbeddingResponse, error) {

	// Demo limit
	if IsDemoAPIKey() {
		demoAPIKeyCount++
		q.Q(demoAPIKeyCount, demoAPIKeyLimit)
		if demoAPIKeyCount > demoAPIKeyLimit {
			return nil, fmt.Errorf("You have exceeded the maximum number of allowed uses for our OpenAI demo key. Please use your own key instead. ")
		}
	}

	// request head
	client := &http.Client{}
	payload := bytes.NewBufferString(`{"model":"text-embedding-ada-002", "input":"` + text + `"}`)
	req, err := http.NewRequest("POST", OpenAIEmbeddingsURL, payload)
	if err != nil {
		return nil, err
	}
	preference := statistic.LocalStatistic.Settings.OpenAISettings
	req.Header.Add("Authorization", "Bearer "+preference.APIKey)
	req.Header.Add("Content-Type", "application/json")

	// request
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	// check response status code return body as error
	if resp.StatusCode != 200 {
		q.Q("response status code: ", resp.StatusCode)
		//dump body
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			q.Q("error: reading resp body ", err)
		}
		q.Q("response body: ", string(body))
		return nil, fmt.Errorf("response status code: %d reason: %s", resp.StatusCode, body)

	}

	// fmt.Println("response Status:", resp.Status)

	// response
	var embeddingResponse EmbeddingResponse
	err = json.NewDecoder(resp.Body).Decode(&embeddingResponse)
	if err != nil {
		return nil, err
	}
	return &embeddingResponse, nil

}

// CompletionMessage
type CompletionMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// CompletionUsage
type CompletionUsage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// CompletionChoice
type CompletionChoice struct {
	Index        int               `json:"index"`
	Message      CompletionMessage `json:"message"`
	FinishReason string            `json:"finish_reason"`
}

// CompletionObject
type CompletionObject struct {
	ID                string             `json:"id"`
	Object            string             `json:"object"`
	Model             string             `json:"model"`
	SystemFingerprint string             `json:"system_fingerprint"`
	Choices           []CompletionChoice `json:"choices"`
	Usage             CompletionUsage    `json:"usage"`
}

// CompletionRequest POST to https://api.openai.com/v1/chat/completions
func CompletionRequest(messages []CompletionMessage) (*CompletionObject, error) {
	// request head
	client := &http.Client{}
	type request struct {
		Model    string              `json:"model"`
		Messages []CompletionMessage `json:"messages"`
	}
	payload := request{
		Model:    "gpt-3.5-turbo",
		Messages: messages,
	}
	jsonBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}
	req, err := http.NewRequest("POST", OpenAICompletionURL, bytes.NewBuffer(jsonBytes))
	if err != nil {
		return nil, err
	}
	apikey := statistic.LocalStatistic.Settings.OpenAISettings.APIKey
	req.Header.Add("Authorization", "Bearer "+apikey)
	req.Header.Add("Content-Type", "application/json")

	// request
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	// check response status code return body as error
	if resp.StatusCode != 200 {
		q.Q("response status code: ", resp.StatusCode)
		//dump body
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			q.Q("error: reading resp body ", err)
		}
		q.Q("response body: ", string(body))
		return nil, fmt.Errorf("response status code: %s", string(body))

	}

	// fmt.Println("response Status:", resp.Status)

	// response
	var completionObject CompletionObject
	err = json.NewDecoder(resp.Body).Decode(&completionObject)
	if err != nil {
		return nil, err
	}
	return &completionObject, nil
}

// AnomalyChoice A JSON object with the result of the analysis like {"normal": true, "reason": "message is normal"}
type AnomalyChoice struct {
	Normal bool   `json:"normal"`
	Reason string `json:"reason"`
}

// ToAnamalyChoice get first choice content as JSON
func ToAnamalyChoice(jsonstr string) (*AnomalyChoice, error) {

	var anomalyChoice AnomalyChoice
	err := json.Unmarshal([]byte(jsonstr), &anomalyChoice)
	if err != nil {
		return nil, err
	}
	return &anomalyChoice, nil
}

// OpenAIDetect sends a syslog message to OpenAI for analysis,
// using a specific prompt to determine if the message indicates an anomaly.
func OpenAIDetect(log string) (*AnomalyChoice, error) {
	// make prompt
	prompt := []llm.Message{
		{
			Role:    "system",
			Content: "Consider a message to be an anomaly if it indicates unexpected system behavior,errors, or security concerns. , return a json with key 'normal' and 'reason'. 'normal` is a boolean, 'reason' is a string.",
		},
		{
			Role:    "user",
			Content: log,
		},
	}
	//please determine if it is normal or an anomaly based on the criteria provided.
	//Consider a message to be an anomaly if it indicates unexpected system behavior,
	// errors, or security concerns. Normal messages are routine logs that do not suggest any issues.
	llmSettings, err := statistic.GetLargeLanguageModelFromAnomalyStatistics()
	if err != nil {
		return nil, err
	}
	llm, err := llm.NewLLMClient(llmSettings)
	if err != nil {
		return nil, err
	}
	result, err := llm.Complete(context.Background(), prompt)
	if err != nil {
		return nil, err
	}

	return ToAnamalyChoice(result)
}
