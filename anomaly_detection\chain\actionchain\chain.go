package actionchain

import (
	"context"

	"fmt"
	"math/rand"
	"mnms/anomaly_detection/chain"
	"mnms/anomaly_detection/def"
	"mnms/anomaly_detection/retriever"
	"mnms/anomaly_detection/store"
	"mnms/llm"
	"strings"
)

type ActionsChain struct {
	m     llm.CompleteEmbedder
	r     def.Retriever
	s     def.Store
	debug bool
}

// GetCurrentDeviceInformation is a special option that represents the current device information.
// func GetCurrentDeviceInformation(input string) []map {

// SuspenseAction is a struct that represents a suspense action.
// Sometimes the system has multiple actions to perform and it is not clear which one to choose.
// The action chain will store these suspense actions and ask the user to clarify their intent.
type SuspenseAction struct {
	Input     string         `json:"input"`     // The user's input
	Documents []def.Document `json:"documents"` // The relevant documents
}

// AllOption is a special option that represents all possible options.
func (s SuspenseAction) AllOption() []string {
	var options []string
	for _, doc := range s.Documents {
		options = append(options, doc.Content)
	}
	return options
}

// firstLine returns first line of the input text.
func firstLine(text string) string {
	lines := strings.Split(text, "\n")
	if len(lines) > 0 {
		return lines[0]
	}
	return text
}

// debugPrint prints the input text if debug is enabled.
func (c *ActionsChain) debugPrint(format string, args ...interface{}) {
	if c.debug {
		fmt.Printf(format, args...)
	}
}

func (c *ActionsChain) InitKnowledge() error {
	// Initialize store...
	c.debugPrint("Initializing knowledge...%d tasks will add\n", len(supportedTasks))
	for _, doc := range supportedTasks {
		// msg is firs line of the doc
		msg := firstLine(doc)
		meta := map[string]any{
			"task": msg,
			"doc":  doc,
		}
		_, err := c.r.Add(context.Background(), msg, meta)
		if err != nil {
			return err
		}
	}
	return nil
}

// GetRelevantDocuments returns relevant documents for the input text.
func (c *ActionsChain) GetRelevantDocuments(ctx context.Context, input string, threshold float32) ([]def.Document, error) {
	cleanInput, err := ClarifyInput(c.m, input)
	if err != nil {
		return nil, err
	}
	c.debugPrint("Clarified input: %s\n", cleanInput)
	// get relevant docs
	docs, err := c.r.GetReleventDocuments(ctx, cleanInput, 5)
	if err != nil {
		return nil, err
	}
	// Print relevant docs summary, if debug is enabled
	docsSummary := def.SummarizeDocuments(docs)
	c.debugPrint("Relevant docs: \n%s\n", docsSummary)

	var docUnderThreshold []def.Document
	c.debugPrint("Documents which under threshold %f: \n", threshold)
	for idx, doc := range docs {
		if doc.Score < threshold {
			c.debugPrint("[%d] %s score:%f \n", idx, doc.Content, doc.Score)
			docUnderThreshold = append(docUnderThreshold, doc)
		}
	}

	return docUnderThreshold, nil
}

// GetExtraInfo returns extra information for the input text.
func (c *ActionsChain) GetExtraInfo(input string) (string, error) {
	extrainfo := ""
	// check task-type
	ret, err := GetDeviceCurrentSettings(c.m, input)
	if err != nil {
		return "", err
	}
	for _, r := range ret {
		temp, ok := r["task"]
		if !ok {
			continue
		}
		task, ok := temp.(string)
		if !ok {
			continue
		}
		if task == "get-device-info" {
			temp, ok := r["mac"]
			if !ok {
				continue
			}
			mac, ok := temp.(string)
			if !ok {
				continue
			}

			// Add fake device information for now
			r := rand.New(rand.NewSource(99))
			randIP := fmt.Sprintf("192.168.1.%d", r.Intn(255))
			extrainfo = fmt.Sprintf("{\"mac\": \"%s\",\"ipaddress\": \"%s\",\"netmask\":\"*************\",\"gateway\":\"***********\",\"hostname\": \"edgerouter-x\",\"kernel\": \"4.19.152\",\"ap\": \"MainAP\",\"scannedby\": \"agent\",\"isdhcp\":false}", mac, randIP)

		}

	}

	return extrainfo, nil
}

func (c *ActionsChain) Run(ctx context.Context, input string, opts ...chain.OptionAssigner) (string, error) {
	c.debugPrint("Running actions chain with input: %s\n", input)
	//Clarify input
	docs, err := c.GetRelevantDocuments(ctx, input, 0.2)
	if err != nil {
		return "", err
	}
	if len(docs) == 0 {
		// no relevant docs, system can't understand the input
		return "", fmt.Errorf("no relevant documents found")
	}

	// compare threshold to find the best match
	bestMatch := docs[0]

	// Prepare the prompt

	// Get meta["doc"]
	temp, ok := bestMatch.Metadata["doc"]
	if !ok {
		return "", fmt.Errorf("doc: %s's metadata does not have task field", bestMatch.Content)
	}
	task, ok := temp.(string)
	if !ok {
		return "", fmt.Errorf("doc: %s's metadata task field is not string", bestMatch.Content)
	}
	userInput := "Task instruction:\n" + task
	userInput += "\nInput: \n" + input

	// Get extra information if needed
	extraInfo, err := c.GetExtraInfo(input)
	if err == nil {

		userInput += "\nMore Input:\n" + extraInfo

	}

	prompt := NewActionPrompt(userInput)

	c.debugPrint("\n\nPrompt:\n%s\n", prompt.String())
	ret, err := c.m.Complete(ctx, prompt.Messages())
	if err != nil {
		return "", err
	}
	c.debugPrint("\n\nAnswer: %s\n", ret)
	return ret, nil

}

// NewChain creates a new actions chain.
func NewChain(m llm.LLMClient, debug bool) *ActionsChain {

	s := store.NewStandardStore()
	r := retriever.NewRetriever(s, m)

	return &ActionsChain{
		m:     m,
		r:     r,
		s:     s,
		debug: debug,
	}
}
