package llm

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"net/http"
)

// Ollama is an ollama server which support many LLM models
type Ollama struct {
	LargeLanguageModel
	Model string `json:"model" mapstructure:"model"`
}

// NewOllama creates a new Ollama instance
// default port is 11434
func NewOllama(host, model string, port int) *Ollama {
	return &Ollama{
		LargeLanguageModel: LargeLanguageModel{
			Type:               "ollama",
			Host:               host,
			Port:               port,
			CompletionEndPoint: "/api/chat",
			EmbeddingEndPoint:  "/api/embeddings",
		},
		Model: model,
	}
}

func (o *Ollama) Set(llm *LargeLanguageModel) {
	o.LargeLanguageModel = *llm
}

func (o *Ollama) Get() *LargeLanguageModel {
	return &o.LargeLanguageModel
}

// FormattedOutputWithSchema is a method that generates a formatted output based on the given messages and response.
// response must be a pointer to the object that will be unmarshaled from the response.
func (o *Ollama) FormattedOutputWithSchema(ctx context.Context, msgs []Message, desc string, schamaName string, schema JSONSchemaDefinition) (map[string]any, error) {

	type request struct {
		Model    string    `json:"model"`
		Messages []Message `json:"messages"`
		Stream   bool      `json:"stream"`
		Format   any       `json:"format"`
	}

	payload := request{
		Model:    o.Model,
		Messages: msgs,
		Stream:   false,
		Format:   schema,
	}

	jsonBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}
	ret, err := o.sendCompletionRequest(ctx, jsonBytes)
	if err != nil {
		return nil, err
	}
	var response map[string]any
	err = json.Unmarshal([]byte(ret.Message.Content), &response)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// FormattedOutput returns the formatted output of the Ollama
func (o *Ollama) FormattedOutput(ctx context.Context, msgs []Message, desc string, response any) (any, error) {

	if !isPointerToSpecificTypes(response) {
		return nil, fmt.Errorf("response must be a pointer to a struct, map, or slice")
	}
	s := GenerateSchema(response)
	type request struct {
		Model    string    `json:"model"`
		Messages []Message `json:"messages"`
		Stream   bool      `json:"stream"`
		Format   any       `json:"format"`
	}
	payload := request{
		Model:    o.Model,
		Messages: msgs,
		Stream:   false,
		Format:   s,
	}
	jsonBytes, err := json.Marshal(payload)
	if err != nil {
		return "", err
	}
	res, err := o.sendCompletionRequest(ctx, jsonBytes)
	if err != nil {
		return "", err
	}
	return res.Message.Content, nil
}

type OllamaCompleteAPIResponse struct {
	Message            Message `json:"message,omitempty"`
	Model              string  `json:"model"`
	CreatedAt          string  `json:"created_at"`
	TotalDuration      uint64  `json:"total_duration"`
	LoadDuration       uint64  `json:"load_duration"`
	PromptEvalDuration uint64  `json:"prompt_eval_duration"`
	EvalDuration       uint64  `json:"eval_duration"`
	EvalCount          uint64  `json:"eval_count"`
	Done               bool    `json:"done"`
}

// sendCompletionRequest sends a completion request to the Ollama server
func (o *Ollama) sendCompletionRequest(ctx context.Context, payload []byte) (*OllamaCompleteAPIResponse, error) {
	client := &http.Client{}
	url, err := o.LargeLanguageModel.GetAPIURL("completion")
	if err != nil {
		return nil, err
	}
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(payload))
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	// check response status code return body as error
	if resp.StatusCode != 200 {
		// return body as error
		// dump body
		body := new(bytes.Buffer)
		body.ReadFrom(resp.Body)

		errMsg := fmt.Sprintf("ollama response status code: %d body:%s", resp.StatusCode, string(body.Bytes()))
		return nil, fmt.Errorf("%s", errMsg)

	}

	var res OllamaCompleteAPIResponse
	err = json.NewDecoder(resp.Body).Decode(&res)
	if err != nil {
		return nil, err
	}
	return &res, nil
}

// Complete is a method that generates a completion string based on a given prompt
// using the Ollama transformer model.
func (o *Ollama) Complete(ctx context.Context, msgs []Message) (string, error) {

	type request struct {
		Model    string    `json:"model"`
		Messages []Message `json:"messages"`
		Stream   bool      `json:"stream"`
	}

	payload := request{
		Model:    o.Model,
		Messages: msgs,
		Stream:   false,
	}
	jsonBytes, err := json.Marshal(payload)
	if err != nil {
		return "", err
	}
	res, err := o.sendCompletionRequest(ctx, jsonBytes)
	if err != nil {
		return "", err
	}
	return res.Message.Content, nil

}

// Embedding is a method that generates an embedding based on a given prompt
// using the Ollama transformer model.
func (o *Ollama) Embedding(_ context.Context, prompt string) ([]float32, error) {
	apiURL, err := o.LargeLanguageModel.GetAPIURL("embedding")
	if err != nil {
		return nil, err
	}
	type request struct {
		Model  string `json:"model"`
		Prompt string `json:"prompt"`
	}
	payload := request{
		Model:  "mxbai-embed-large",
		Prompt: prompt,
	}
	jsonBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}
	req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(jsonBytes))
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	type response struct {
		Embedding []float32 `json:"embedding"`
	}
	var res response
	err = json.NewDecoder(resp.Body).Decode(&res)
	if err != nil {
		return nil, err
	}
	return res.Embedding, nil
}
