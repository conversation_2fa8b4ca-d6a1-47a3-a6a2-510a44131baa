package anomstore

import (
	"bytes"
	"encoding/csv"
	"fmt"
	"io"
	"regexp"
)

// SerializeToCSV A RAG io.Reader instance, RAG data remove vector only keep text, reason and normal
// It's CSV format with header: text,normal,reason
func SerializeToCSV(vecs *VectorList, filter string) (io.Reader, error) {

	var buffer bytes.Buffer

	if filter == "" {
		filter = ".*"
	}

	regex, err := regexp.Compile(filter)
	if err != nil {
		regex = nil
	}

	w := csv.NewWriter(&buffer)
	head := []string{"id", "text", "normal", "reason"}
	err = w.Write(head)
	if err != nil {
		return nil, fmt.Errorf("Failed to write header: %v", err)
	}

	keys := sortedMapKeys(vecs.Items)
	for _, id := range keys {
		v := vecs.Items[id]
		if regex != nil && !regex.MatchString(v.Text) {
			continue
		}
		idText := fmt.Sprint(id)
		record := []string{idText, v.Text, fmt.Sprint(v.Normal), v.Reason}
		err := w.Write(record)
		if err != nil {
			return nil, fmt.Errorf("Failed to write record: %v", err)
		}
	}

	w.Flush()
	return &buffer, nil
}

// DeserializeFromCSV write csv to vector list
func DeserializeFromCSV(reader io.Reader) (*VectorList, error) {
	vecs := &VectorList{
		Items:  make(map[int64]Vector),
		NextID: 0,
	}
	r := csv.NewReader(reader)

	records, err := r.ReadAll()
	if err != nil {
		return vecs, fmt.Errorf("Failed to read all records: %v", err)
	}
	for i, record := range records {
		if i == 0 {
			// drop head
			continue
		}
		normal := false
		if record[2] == "true" {
			normal = true
		}
		vec := &Vector{
			Vector: nil,
			Text:   record[1],
			Normal: normal,
			Reason: record[3],
		}
		err = vec.ReCalEmbeddings()
		if err != nil {
			return vecs, fmt.Errorf("Failed to recalculate embeddings: %v", err)
		}
		_, err := vecs.Insert(vec)
		if err != nil {
			return vecs, fmt.Errorf("Failed to insert vector: %v", err)
		}
	}
	return vecs, nil
}
