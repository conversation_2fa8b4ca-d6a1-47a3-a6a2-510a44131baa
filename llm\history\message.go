package history

import (
	"encoding/json"
	"slices"
	"strings"

	"mnms/llm"
)

// HistoryMessage implements the llm.Message interface for stored messages
type HistoryMessage struct {
	Role    string         `json:"role"`
	Content []ContentBlock `json:"content"`
}

func (m *HistoryMessage) GetRole() string {
	return m.Role
}

func ClearToolMessages(messages *[]HistoryMessage) {
	// delete tool_use and tool_result messages only if ToolUseID is matched
	// step 1. find all tool_use messages and store their ToolUseID
	// step 2. check all tool_result messages, if ToolUseID is in the list, remove it
	// step 3. remove all tool_use messages whose ToolUseID was matched in step 2

	toolUseIDs := make([]string, 0)

	for _, message := range *messages {
		for _, block := range message.Content {
			if block.Type == "tool_use" {
				toolUseIDs = append(toolUseIDs, block.ID)
			}
		}
	}
	finishedToolUseIDs := make([]string, 0)
	for _, message := range *messages {
		for _, block := range message.Content {
			if block.Type == "tool_result" {
				if slices.Contains(toolUseIDs, block.ToolUseID) {

					finishedToolUseIDs = append(finishedToolUseIDs, block.ToolUseID)
				}
			}
		}
	}

	// remove all tool_use messages whose ToolUseID was matched in step 2
	for _, message := range *messages {
		for i, block := range message.Content {
			if block.Type == "tool_use" && slices.Contains(finishedToolUseIDs, block.ID) {

				message.Content = slices.Delete(message.Content, i, i+1)
			}
			// also remove the tool_result message
			if block.Type == "tool_result" && slices.Contains(finishedToolUseIDs, block.ToolUseID) {

				message.Content = slices.Delete(message.Content, i, i+1)
			}
		}
	}
}

func (m *HistoryMessage) GetContent() string {
	// Concatenate all text content blocks
	var content string
	for _, block := range m.Content {
		if block.Type == "text" {
			content += block.Text + " "
		}
	}
	return strings.TrimSpace(content)
}

func (m *HistoryMessage) GetToolCalls() []llm.ToolCall {
	var calls []llm.ToolCall
	for _, block := range m.Content {
		if block.Type == "tool_use" {
			calls = append(calls, &HistoryToolCall{
				id:   block.ID,
				name: block.Name,
				args: block.Input,
			})
		}
	}
	return calls
}

func (m *HistoryMessage) IsToolResponse() bool {
	for _, block := range m.Content {
		if block.Type == "tool_result" {
			return true
		}
	}
	return false
}

func (m *HistoryMessage) GetToolResponseID() string {
	for _, block := range m.Content {
		if block.Type == "tool_result" {
			return block.ToolUseID
		}
	}
	return ""
}

func (m *HistoryMessage) GetUsage() (int, int) {
	return 0, 0 // History doesn't track usage
}

// HistoryToolCall implements llm.ToolCall for stored tool calls
type HistoryToolCall struct {
	id   string
	name string
	args json.RawMessage
}

func (t *HistoryToolCall) GetID() string {
	return t.id
}

func (t *HistoryToolCall) GetName() string {
	return t.name
}

func (t *HistoryToolCall) GetArguments() map[string]interface{} {
	var args map[string]interface{}
	if err := json.Unmarshal(t.args, &args); err != nil {
		return make(map[string]interface{})
	}
	return args
}

// ContentBlock represents a block of content in a message
type ContentBlock struct {
	Type      string          `json:"type"`
	Text      string          `json:"text,omitempty"`
	ID        string          `json:"id,omitempty"`
	ToolUseID string          `json:"tool_use_id,omitempty"`
	Name      string          `json:"name,omitempty"`
	Input     json.RawMessage `json:"input,omitempty"`
	Content   interface{}     `json:"content,omitempty"`
}
