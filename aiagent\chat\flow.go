package chat

// GoThoughDocuments is a function that goes through all the documents in a DocumentsProvider
// func (dps DocumentProviders) GoThoughDocuments(ctx context.Context, input string) (string, error) {
// 	if len(dps) == 0 {
// 		return "", fmt.<PERSON><PERSON><PERSON>("no documents in the provider")
// 	}
// 	var output []string
// 	var err error
// 	for _, doc := range dps {
// 		// check ctx
// 		select {
// 		case <-ctx.Done():
// 			return strings.Join(output, "\n"), fmt.Errorf("context is done")
// 		default:

// 			output, err = doc.Query(input)
// 			if err != nil {
// 				return "", fmt.Errorf("input: %s to %s got error: %s", input, doc.GetName(), err.Error())
// 			}
// 			Log(fmt.Sprintf("[%s] In: \n %s \n Out: %s", doc.GetName(), input, output))
// 			input = output
// 		}
// 	}
// 	return output, nil
// }

// func (dps DocumentProviders) GoParrallelDocuments(ctx context.Context, input string) (string, error) {
// 	if len(dps) == 0 {
// 		return "", fmt.Errorf("no documents in the provider")
// 	}
// 	var sharedErr error
// 	sharedErrLock := sync.Mutex{}
// 	ctx, cancel := context.WithCancelCause(ctx)
// 	defer cancel(nil)
// 	setSharedErr := func(err error) {
// 		sharedErrLock.Lock()
// 		defer sharedErrLock.Unlock()
// 		// Another goroutine might have already set the error.
// 		if sharedErr == nil {
// 			sharedErr = err
// 			// Cancel the operation for all other goroutines.
// 			cancel(sharedErr)
// 		}
// 	}
// 	var wg sync.WaitGroup
// 	semaphore := make(chan struct{}, runtime.NumCPU())
// 	outputChan := make(chan string, len(dps)) // Buffered channel to avoid blocking
// 	var outputLock sync.Mutex
// 	var output string

// 	for _, docProvider := range dps {
// 		wg.Add(1)
// 		go func(doc DocumentProvider) {
// 			defer wg.Done()
// 			defer func() {
// 				<-semaphore
// 			}()
// 			semaphore <- struct{}{}

// 			out, err := doc.Query(input)
// 			if err != nil {
// 				setSharedErr(fmt.Errorf("input: %s to %s got error: %s", input, doc.GetName(), err.Error()))
// 				return
// 			}
// 			Log(fmt.Sprintf("[%s] In: \n %s \n Out: %s", doc.GetName(), input, out))
// 			outputChan <- out
// 		}(docProvider)
// 	}

// 	go func() {
// 		wg.Wait()
// 		close(outputChan)
// 	}()

// 	for out := range outputChan {
// 		outputLock.Lock()
// 		output = output + "\n" + out
// 		outputLock.Unlock()
// 	}

// 	return output, sharedErr
// }
