import { memo } from "react";
import { Card, Descriptions, Input, Empty, Spin } from "antd";
import { useKVStore } from "../hooks/useKVStore";

/**
 * Component to display the list of key-value pairs
 * @returns {JSX.Element} KVStore list component
 */
const KVStoreList = () => {
  const { descriptionItems, isLoading, searchFilter, setSearchFilter } =
    useKVStore();

  const renderContent = () => {
    if (isLoading) {
      return <Spin tip="Loading..." />;
    }

    if (descriptionItems.length === 0) {
      return (
        <Empty
          description={
            searchFilter
              ? "No matching key-value pairs found"
              : "No key-value pairs available"
          }
        />
      );
    }

    return (
      <Descriptions items={descriptionItems} column={1} bordered size="small" />
    );
  };

  return (
    <Card
      title="Key-Value Pairs"
      extra={
        <Input.Search
          placeholder="Search keys or values"
          allowClear
          value={searchFilter}
          onChange={(e) => setSearchFilter(e.target.value)}
          style={{ width: 250 }}
        />
      }
    >
      {renderContent()}
    </Card>
  );
};

export default memo(KVStoreList);
