package llm

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"

	"net/http"

	"github.com/qeof/q"
)

var DemoAPIKey = "098827874f26ef21785f57488c92ae6f4f6b1beea190aad791669b94df1a273b71e41c28f767a726fe7b267b577a97e8d8b1ce4d6d382617e4bf60b111d99b54215d79ca50726cfe"

const DemoLimitation = 50

var DemoKeyUsage = 0

// OpenAI is GPT-3 transformer model
type OpenAI struct {
	LargeLanguageModel
	DemoKeyUsage int `json:"demo_key_usage" mapstructure:"demo_key_usage"`
}

// New
func NewOpenAI(apikey string, model string) *OpenAI {
	return &OpenAI{
		LargeLanguageModel: LargeLanguageModel{
			Type:               "open-ai",
			Host:               "https://api.openai.com",
			Port:               0,
			CompletionEndPoint: "/v1/chat/completions",
			EmbeddingEndPoint:  "/v1/embeddings",
			APIKey:             apikey,
			Model:              model,
		},
		DemoKeyUsage: 0,
	}
}

// Set
func (t *OpenAI) Set(llm *LargeLanguageModel) {
	t.LargeLanguageModel = *llm
}

// Get
func (t *OpenAI) Get() *LargeLanguageModel {
	return &t.LargeLanguageModel
}

// callCompleteAPI
func (t *OpenAI) callCompleteAPI(ctx context.Context, payload []byte) (string, error) {
	client := &http.Client{}
	url, err := t.LargeLanguageModel.GetAPIURL("completion")
	if err != nil {
		return "", err
	}
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(payload))
	if err != nil {
		return "", err
	}
	apikey := t.LargeLanguageModel.GetAPIKey()
	req.Header.Add("Authorization", "Bearer "+apikey)
	req.Header.Add("Content-Type", "application/json")
	req = req.WithContext(ctx)

	// request
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	// check response status code return body as error
	if resp.StatusCode != 200 {
		q.Q("response status code: ", resp.StatusCode)
		//dump body
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			q.Q("error: reading resp body ", err)
		}
		q.Q("response body: ", string(body))
		return string(body), fmt.Errorf("response status code: %s", string(body))

	}

	// response
	type response struct {
		ID                string `json:"id"`
		Object            string `json:"object"`
		Model             string `json:"model"`
		SystemFingerprint string `json:"system_fingerprint"`
		Choices           []struct {
			Index        int     `json:"index"`
			Message      Message `json:"message"`
			FinishReason string  `json:"finish_reason"`
		} `json:"choices"`
		Usage struct {
			PromptTokens     int `json:"prompt_tokens"`
			CompletionTokens int `json:"completion_tokens"`
			TotalTokens      int `json:"total_tokens"`
		} `json:"usage"`
	}

	var res response
	err = json.NewDecoder(resp.Body).Decode(&res)
	if err != nil {
		return "", err
	}

	choices := res.Choices
	if len(choices) == 0 {
		return "", fmt.Errorf("choice is empty")
	}

	return choices[0].Message.Content, nil
}

// Complete is a method that generates a completion string based on a given prompt
// using the GPT-3 transformer model.
func (t *OpenAI) Complete(ctx context.Context, msgs []Message) (string, error) {

	type request struct {
		Model    string    `json:"model"`
		Messages []Message `json:"messages"`
	}

	payload := request{
		Model:    t.LargeLanguageModel.Model,
		Messages: msgs,
	}
	jsonBytes, err := json.Marshal(payload)
	if err != nil {
		return "", err
	}
	return t.callCompleteAPI(ctx, jsonBytes)
}

// FormattedOutputWithSchema is a method that generates a formatted output based on the given messages and response.
// response must be a pointer to the object that will be unmarshaled from the response.
func (t *OpenAI) FormattedOutputWithSchema(ctx context.Context, msgs []Message, desc string, schamaName string, schema JSONSchemaDefinition) (map[string]any, error) {
	type responseFormat struct {
		Type       string `json:"type"`
		JsonSchema any    `json:"json_schema"`
	}
	type request struct {
		Model          string         `json:"model"`
		Messages       []Message      `json:"messages"`
		ResponseFormat responseFormat `json:"response_format"`
	}

	sche := &ResponseFormatJSONSchemaJSONSchemaParam{
		Name:        schamaName,
		Description: desc,
		Schema:      schema,
		Strict:      true,
	}

	payload := request{
		Model:    t.LargeLanguageModel.Model,
		Messages: msgs,
		ResponseFormat: responseFormat{
			Type:       "json_schema",
			JsonSchema: sche,
		},
	}
	jsonBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}
	ret, err := t.callCompleteAPI(ctx, jsonBytes)
	if err != nil {
		return nil, err
	}
	var response map[string]any
	err = json.Unmarshal([]byte(ret), &response)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// FormattedOutput is a method that generates a formatted output based on the given messages and response.
// response must be a pointer to the object that will be unmarshaled from the response.
func (t *OpenAI) FormattedOutput(ctx context.Context, msgs []Message, desc string, response any) (any, error) {
	type responseFormat struct {
		Type       string `json:"type"`
		JsonSchema any    `json:"json_schema"`
	}
	type request struct {
		Model          string         `json:"model"`
		Messages       []Message      `json:"messages"`
		ResponseFormat responseFormat `json:"response_format"`
	}

	s := GenOpenAIResponseFormatJSONSchema(desc, response)

	payload := request{
		Model:    t.LargeLanguageModel.Model,
		Messages: msgs,
		ResponseFormat: responseFormat{
			Type:       "json_schema",
			JsonSchema: s,
		},
	}
	jsonBytes, err := json.Marshal(payload)
	if err != nil {
		return "", err
	}
	ret, err := t.callCompleteAPI(ctx, jsonBytes)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal([]byte(ret), response)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// Embedding is a method that generates an embedding based on a given prompt
// using the GPT-3 transformer model.
func (t *OpenAI) Embedding(_ context.Context, prompt string) ([]float32, error) {

	// request head
	client := &http.Client{}
	payloadData := map[string]interface{}{
		"model": "text-embedding-ada-002",
		"input": prompt,
	}
	// Marshal the payloadData map into JSON
	payloadBytes, err := json.Marshal(payloadData)
	if err != nil {
		q.Q("error: marshaling payloadData ", err)
		return nil, err
	}

	apiurl, err := t.LargeLanguageModel.GetAPIURL("embedding")
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", apiurl, bytes.NewBuffer(payloadBytes))
	if err != nil {
		return nil, err
	}

	req.Header.Add("Authorization", "Bearer "+t.LargeLanguageModel.GetAPIKey())
	req.Header.Add("Content-Type", "application/json")

	// request
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	// check response status code return body as error
	if resp.StatusCode != 200 {
		q.Q("response status code: ", resp.StatusCode)
		//dump body
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			q.Q("error: reading resp body ", err)
		}
		q.Q("response body: ", string(body))
		return nil, fmt.Errorf("response status code: %d reason: %s", resp.StatusCode, body)

	}
	type response struct {
		Object string `json:"object"`
		Data   []struct {
			Object    string    `json:"object"`
			Embedding []float32 `json:"embedding"`
			Index     int       `json:"index"`
		} `json:"data"`
		Model string         `json:"model"`
		Usage map[string]int `json:"usage"`
	}
	var res response
	// fmt.Println("response Status:", resp.Status)
	err = json.NewDecoder(resp.Body).Decode(&res)
	if err != nil {
		return nil, err
	}
	if len(res.Data) == 0 {
		return nil, fmt.Errorf("data is empty")
	}
	return res.Data[0].Embedding, nil

}
