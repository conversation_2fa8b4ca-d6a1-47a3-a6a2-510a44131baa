package llm

import (
	"context"
	"encoding/json"
)

// Provider is an interface that defines the methods for a LLM provider
// NOTE!! We will migrate to use openai provider instead of LLMClient

// ToolCall represents a tool invocation
type ToolCall interface {
	// GetName returns the tool's name
	GetName() string

	// GetArguments returns the arguments passed to the tool
	GetArguments() map[string]interface{}

	// GetID returns the unique identifier for this tool call
	GetID() string
}

type ToolCallJSON struct {
	ID        string         `json:"id"`
	Name      string         `json:"name"`
	Arguments map[string]any `json:"arguments"`
}

// GetName returns the tool's name
func (t *ToolCallJSON) GetName() string {
	return t.Name
}

// GetArguments returns the arguments passed to the tool
func (t *ToolCallJSON) GetArguments() map[string]any {
	return t.Arguments
}

// GetID returns the unique identifier for this tool call
func (t *ToolCallJSON) GetID() string {
	return t.ID
}

// GetToolCallsJSON returns the JSON representation of the tool calls
func GetToolCallsJSON(toolCalls []ToolCall) ([]byte, error) {

	var toolCallsJSON []ToolCallJSON
	for _, toolCall := range toolCalls {
		toolCallsJSON = append(toolCallsJSON, ToolCallJSON{
			ID:        toolCall.GetID(),
			Name:      toolCall.GetName(),
			Arguments: toolCall.GetArguments(),
		})
	}
	return json.Marshal(toolCallsJSON)
}

// ToolCallsJSONToToolCalls converts a JSON representation of tool calls to a slice of ToolCall
func ToolCallsJSONToToolCalls(jsonBytes []byte) ([]ToolCallJSON, error) {
	var toolCallsJSON []ToolCallJSON
	if err := json.Unmarshal(jsonBytes, &toolCallsJSON); err != nil {
		return nil, err
	}

	return toolCallsJSON, nil
}

// Tool represents a tool definition
type Tool struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	InputSchema Schema `json:"input_schema"`
}

// Schema defines the input parameters for a tool
type Schema struct {
	Type       string                 `json:"type"`
	Properties map[string]interface{} `json:"properties"`
	Required   []string               `json:"required"`
}

type IMessage interface {
	GetRole() string
	GetContent() string
	// GetToolCalls returns any tool calls made in this message
	GetToolCalls() []ToolCall
	// IsToolResponse returns true if this message is a response from a tool
	IsToolResponse() bool
	// GetToolResponseID returns the ID of the tool call this message is responding to
	GetToolResponseID() string
	// GetUsage returns token usage statistics if available
	GetUsage() (input int, output int)
}

type Provider interface {
	// CreateMessage sends a message to the LLM and returns the response
	CreateMessage(ctx context.Context, prompt string, messages []IMessage, tools []Tool) (IMessage, error)
	// SetSystemPrompt sets the system prompt for the LLM
	SetSystemPrompt(systemPrompt string)

	// CreateToolResponse creates a message representing a tool response
	CreateToolResponse(toolCallID string, content interface{}) (IMessage, error)

	// SupportsTools returns whether this provider supports tool/function calling
	SupportsTools() bool

	// Name returns the provider's name
	Name() string
}
