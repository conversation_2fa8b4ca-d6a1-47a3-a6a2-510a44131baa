package main

import (
	"bytes"
	"context"
	"flag"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/google/generative-ai-go/genai"
	"github.com/qeof/q"
	"google.golang.org/api/iterator"
	"google.golang.org/api/option"
)

func writeResultToFile(filePath string, resp *genai.GenerateContentResponse) error {
	// print result
	var ret string
	for _, cand := range resp.Candidates {
		for _, part := range cand.Content.Parts {
			ret = ret + fmt.Sprintf("%v", part)
			fmt.Println(part)
		}
	}
	// create a file
	out, err := os.OpenFile(filePath, os.O_TRUNC|os.O_CREATE, 0755)
	if err != nil {
		return err
	}
	defer out.Close()
	out.Write([]byte(ret))
	return nil
}

func runCommand(name string, args ...string) (string, error) {
	cmd := exec.Command(name, args...)
	var out bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &out
	if err := cmd.Start(); err != nil {
		q.Q("cmd.Start:", err)
		return "", fmt.Errorf("cmd.Start: %v", err)
	}
	if err := cmd.Wait(); err != nil {
		if exiterr, ok := err.(*exec.ExitError); ok {
			if exiterr.ExitCode() != 1 {
				q.Q("Exit Status:", exiterr.ExitCode())
				return "", fmt.Errorf("cmd.Start: %v", err)
			}
		} else {
			q.Q("cmd.Wait:", err)
			return "", fmt.Errorf("cmd.Start: %v", err)
		}
	}
	return out.String(), nil
}

func goVetWithGemini(ctx context.Context, apiKey string, modelname string) error {
	// provide 'go vet' report
	exfilePath, err := os.Executable()
	if err != nil {
		return err
	}
	exPath := filepath.Dir(exfilePath)
	analyzePath := filepath.Join(exPath, "..", "...")
	q.Q(analyzePath)
	vetOutput, err := runCommand("go", "vet", analyzePath)
	if err != nil {
		return err
	}

	totalOutput := vetOutput
	if totalOutput == "" {
		// create a file
		analyzeResult := filepath.Join(exPath, "goVetResult-"+modelname+".md")
		out, err := os.OpenFile(analyzeResult, os.O_TRUNC|os.O_CREATE, 0755)
		if err != nil {
			return err
		}
		defer out.Close()
		out.Write([]byte("No error"))
		return nil
	}

	// Access your API key as an environment variable (see "Set up your API key" above)
	client, err := genai.NewClient(ctx, option.WithAPIKey(apiKey))
	if err != nil {
		return err
	}
	defer client.Close()

	// The Gemini 1.5 models are versatile and work with most use cases
	model := client.GenerativeModel(modelname)

	// input text
	prompt := []genai.Part{
		genai.Text(totalOutput),
		genai.Text("Please help me to analyze Golang vet messages and provide suggestions for correction."),
	}
	resp, err := model.GenerateContent(ctx, prompt...)
	if err != nil {
		return err
	}
	// print result
	resultFilePath := filepath.Join(exPath, "goVetResult-"+modelname+".md")
	err = writeResultToFile(resultFilePath, resp)
	if err != nil {
		return err
	}
	return nil
}

func analyzeFileWithGemini(ctx context.Context, apiKey string, modelname string) error {
	// Access your API key as an environment variable (see "Set up your API key" above)
	client, err := genai.NewClient(ctx, option.WithAPIKey(apiKey))
	if err != nil {
		return err
	}
	defer client.Close()

	// provide code
	exfilePath, err := os.Executable()
	if err != nil {
		return err
	}
	exPath := filepath.Dir(exfilePath)
	var prompt []genai.Part
	var FileLists []string

	MnmsFolder := filepath.Join(exPath, "..")
	files, err := os.ReadDir(MnmsFolder)
	for _, file := range files {
		if file.IsDir() {
			continue
		}
		if !strings.HasSuffix(file.Name(), ".go") {
			continue
		}
		FileLists = append(FileLists, file.Name())
	}
	q.Q(FileLists)

	for _, filename := range FileLists {
		filePath := filepath.Join(exPath, "..", filename)
		file, err := client.UploadFileFromPath(ctx, filePath, nil)
		if err != nil {
			return err
		}
		defer client.DeleteFile(ctx, file.Name)
		prompt = append(prompt, genai.FileData{URI: file.URI})
	}

	// The Gemini 1.5 models are versatile and work with most use cases
	model := client.GenerativeModel(modelname)
	// input text
	prompt = append(prompt, genai.Text("Please help me to analyze Golang codes and provide suggestions."))

	// generate content
	resp, err := model.GenerateContent(ctx, prompt...)
	if err != nil {
		return err
	}
	// print result
	resultFilePath := filepath.Join(exPath, "analyzeResult-"+modelname+".md")
	err = writeResultToFile(resultFilePath, resp)
	if err != nil {
		return err
	}
	return nil
}

func GeminiModelList(ctx context.Context, apiKey string) error {
	client, err := genai.NewClient(ctx, option.WithAPIKey(apiKey))
	if err != nil {
		return err
	}
	defer client.Close()
	iter := client.ListModels(ctx)
	for {
		m, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			return err
		}
		name := strings.Split(m.Name, "/")
		if len(name) == 2 {
			fmt.Println(name[1])
		} else {
			fmt.Println(m.Name)
		}
	}
	return nil
}

func validateGeminiModel(ctx context.Context, apiKey string, modelname string) (bool, error) {
	client, err := genai.NewClient(ctx, option.WithAPIKey(apiKey))
	if err != nil {
		return false, err
	}
	defer client.Close()
	iter := client.ListModels(ctx)
	for {
		m, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			return false, err
		}
		if strings.Contains(m.Name, modelname) {
			return true, nil
		}
	}
	return false, nil
}

func main() {
	q.O = "stderr"
	q.P = ""
	dp := flag.String("P", "", "debug log pattern string")
	geminikey := flag.String("geminikey", "", "gemini api key")
	debuglog := flag.Bool("debuglog", false, "enable debug log, this will override -P to .*")
	modellist := flag.Bool("modellist", false, "gemini model list")
	modelname := flag.String("model", "gemini-1.5-flash", "specify gemini model")
	flag.Parse()
	if *debuglog {
		*dp = ".*"
	}
	if *dp == "." {
		fmt.Fprintln(os.Stderr, "error: invalid debug pattern")
		return
	}
	_, err := regexp.Compile(*dp)
	if err != nil {
		fmt.Fprintf(os.Stderr, "error: invalid regular expression, %v\n", err)
		return
	}
	q.P = *dp
	var apiKey string
	if *geminikey == "" {
		apiKey = os.Getenv("GEMINI_API_KEY")
		if apiKey == "" {
			fmt.Fprintln(os.Stderr, "Please set/export Gemini api key to GEMINI_API_KEY, or add flag '-geminikey'")
			return
		}
	} else {
		apiKey = *geminikey
	}

	ctx := context.Background()

	if *modellist {
		err := GeminiModelList(ctx, apiKey)
		q.Q(err)
		fmt.Fprintf(os.Stderr, "%v\n", err)
		return
	} else {
		valid, err := validateGeminiModel(ctx, apiKey, *modelname)
		if err != nil {
			q.Q(err)
			fmt.Fprintf(os.Stderr, "%v\n", err)
			return
		}
		if !valid {
			fmt.Fprintf(os.Stderr, "error: invalid model name\n")
			return
		}
	}

	q.Q(*modelname)

	// Gemini analyze go vet reports
	err = goVetWithGemini(ctx, apiKey, *modelname)
	if err != nil {
		q.Q(err)
		fmt.Fprintf(os.Stderr, "%v\n", err)
		return
	}
	// Gemini analyze files
	err = analyzeFileWithGemini(ctx, apiKey, *modelname)
	if err != nil {
		q.Q(err)
		fmt.Fprintf(os.Stderr, "%v\n", err)
		return
	}
	return
}
