package prompt

import (
	"fmt"
	"mnms/llm"
	"os"

	"gopkg.in/yaml.v2"
)

type Prompt struct {
	SystemPrompt string `yaml:"system_prompt"`
	UserPrompt   string `yaml:"user_prompt"`
}

var DefaultSystemPrompt = `
You are a system admin reading a syslog message to find any anomalies.

Message can be considered anomalous if it can cause system instability,
indicate errors, symptomatic of a problem, or are indicative of a security issue.
Reports of statistics, notifications and other information are not considered anomalous.

Before producing a response, please read the following instructions carefully:
  1. The response should be in JSON format as described below
  2. The response containing the result of syslog input message
  3. The response should be complete and as concise and accurate as possible
  4. No other non-JSON response should be produced
  5. Avoid producing false positives by checking against the reference messages

Produce only JSON response contains the following fields:
  - message: The original syslog message
  - normal: A boolean indicating whether the message is normal or anomalous
  - reason: A reason for the anomaly

The messages similar to the non-anomalous reference messages should be absolutely 
prioritized as not anomalous and override any other issues or other considerations 
regarding anomaly detection.
Even if you think the message is anomalous, it should be prioritized as not anomalous 
if it is similar to a reference message.
`

var prompt = Prompt{
	SystemPrompt: DefaultSystemPrompt,
}

// GetPrompt returns a copy of the prompt
func GetPrompt() *Prompt {
	return &Prompt{
		SystemPrompt: prompt.SystemPrompt,
		UserPrompt:   prompt.UserPrompt,
	}
}

// SetUserPrompt adds a user prompt to the prompt
func (p *Prompt) SetUserPrompt(userPrompt string) {
	p.UserPrompt = userPrompt
}

// Messages returns the system prompt and user prompt as a slice of llm.Message
func (p *Prompt) Messages() []llm.Message {
	msgs := []llm.Message{}
	if len(p.SystemPrompt) > 0 {
		msgs = append(msgs, llm.Message{
			Role:    "system",
			Content: p.SystemPrompt,
		})
	}
	if len(p.UserPrompt) > 0 {
		msgs = append(msgs, llm.Message{
			Role:    "user",
			Content: p.UserPrompt,
		})
	}
	return msgs
}

// AddAnomalies adds anomalies reference messages to the prompt
func (p *Prompt) AddAnomalies(anomalies []string) {
	p.SystemPrompt += "\nThe messages similar to the following reference messages should be absolutely prioritized as anomalous:\n"
	for _, anomaly := range anomalies {
		p.SystemPrompt += fmt.Sprintf("- %s\n", anomaly)
	}
}

// AddNormalMessages adds normal reference messages to the prompt
func (p *Prompt) AddNormalMessages(messages []string) {
	p.SystemPrompt += "\nThe messages similar to the following reference messages should be absolutely prioritized as not anomalous:\n"
	for _, msg := range messages {
		p.SystemPrompt += fmt.Sprintf("- %s\n", msg)
	}
}

// CheckPromptFile checks if the prompt file exists the set the system and user prompt
func CheckPromptFile(filePath string) (*Prompt, error) {
	prompt := &Prompt{}
	yamlFile, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}
	err = yaml.Unmarshal(yamlFile, prompt)
	if err != nil {
		return nil, err
	}
	return prompt, nil
}

// Dump
func (p *Prompt) String() string {
	return fmt.Sprintf("System Prompt: \n%s\nUser Prompt: \n%s\n", p.SystemPrompt, p.UserPrompt)
}
