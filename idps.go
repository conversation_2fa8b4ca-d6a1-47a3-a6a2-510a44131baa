package mnms

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/qeof/q"
)

func NewIdpsRePortInfo() IdpsRePortInfo {
	return IdpsRePortInfo{}
}

type IdpsRePortInfo struct {
	StartTime        string             `json:"start_time"`
	Rules            []IdpsRule         `json:"rules"`
	Event            []EventMessage     `json:"event"`
	RecordList       []RecordList       `json:"recordlist"`
	RulePacketsTotal []RulePacketsTotal `json:"rulepackets"`
}

type IdpsRule struct {
	Time    string        `json:"created_time"`
	Name    string        `json:"name"`
	Content []RuleContent `json:"contents"`
}
type RuleContent struct {
	Sid   uint32 `json:"sid"`
	Value string `json:"value"`
}

type RulePacketsTotal struct {
	Sid       uint64 `json:"sid"`
	Name      string `json:"name"`
	Action    string `json:"action"`
	Counts    uint64 `json:"counts"`
	Timestamp string `json:"-"`
}

type EventMessage struct {
	Id          uint32 `json:"id"`
	Timestamp   string `json:"timestamp"`
	Type        string `json:"type"`
	InInterface string `json:"inInterface"`
	Srcip       string `json:"srcip"`
	SrcPort     uint16 `json:"srcPort"`
	Destip      string `json:"destip"`
	DestPort    uint16 `json:"destPort"`
	Protocol    string `json:"protocol"`
	Description string `json:"description"`
	Rulename    string `json:"rulename"`
}

type RecordList struct {
	Date  string     `json:"date,omitempty"`
	Files []FileInfo `json:"files,omitempty"`
}

type FileInfo struct {
	Name string `json:"name,omitempty"`
	Size int64  `json:"size,omitempty"`
}

func init() {
	QC.IdpsReport = make(map[string]IdpsRePortInfo)
}

// HandleIdpsReport accepts the idps report and return report
//
// POST /api/v1/idps/report?client=client1
//
// post specific client info
/*
{
    "rules": [
        {
            "created_time": "2024-02-19T11:22:35+08:00",
            "name": "selftest_icmp"
        },
        {
            "created_time": "2024-02-19T11:22:35+08:00",
            "name": "selftest_tcp"
        },
        {
            "created_time": "2024-02-19T11:22:35+08:00",
            "name": "selftest_tls"
        },
        {
            "created_time": "2024-02-19T11:22:35+08:00",
            "name": "dhcp-events"
        },
        {
            "created_time": "2024-02-19T11:22:35+08:00",
            "name": "dns-events"
        }
    ],
    "event": [
        {
            "id": 789,
            "timestamp": "2024-02-17T17:20:15+08:00",
            "type": "drop",
            "inInterface": "乙太網路",
            "srcip": "************",
            "srcPort": 0,
            "destip": "*******",
            "destPort": 0,
            "protocol": "icmp",
            "description": "icmpv4 selftest drop"
        }
    ],
    "protocol": [
        "modbus",
        "tls",
        "dns",
        "ntp",
        "dhcp",
        "sip",
        "icmp",
        "http",
        "ftp",
        "http2",
        "imap",
        "smb",
        "smtp",
        "snmp",
        "ssh",
        "tftp",
        "sip",
        "enip",
        "nfs",
        "ntp",
        "alert",
        "drop"
    ]
}
*/
//
// GET /api/v1/idps/report?client=client5
//
//	retrieve report of specific client
//
//	exapmle:
/*
{
    "rules": [
        {
            "created_time": "2024-02-19T11:22:35+08:00",
            "name": "selftest_icmp"
        },
        {

            "created_time": "2024-02-19T11:22:35+08:00",
            "name": "selftest_tcp"
        },
        {
            "created_time": "2024-02-19T11:22:35+08:00",
            "name": "selftest_tls"
        },
        {
            "created_time": "2024-02-19T11:22:35+08:00",
            "name": "dhcp-events"
        },
        {
            "created_time": "2024-02-19T11:22:35+08:00",
            "name": "dns-events"
        }
    ],
    "event": [
        {
            "id": 789,
            "timestamp": "2024-02-17T17:20:15+08:00",
            "type": "drop",
            "inInterface": "乙太網路",
            "srcip": "************",
            "srcPort": 0,
            "destip": "*******",
            "destPort": 0,
            "protocol": "icmp",
            "description": "icmpv4 selftest drop"
        }
    ],
    "protocol": [
        "modbus",
        "tls",
        "dns",
        "ntp",
        "dhcp",
        "sip",
        "icmp",
        "http",
        "ftp",
        "http2",
        "imap",
        "smb",
        "smtp",
        "snmp",
        "ssh",
        "tftp",
        "sip",
        "enip",
        "nfs",
        "ntp",
        "alert",
        "drop"
    ]
}
*/
// GET /api/v1/idps/report
//
//	retrieve repost of all client
//
//	exapmle:
/*
{
    "client5": {
        "rules": [
            {
                "created_time": "2024-02-19T11:22:35+08:00",
                "name": "selftest_icmp"
            },
            {
                "created_time": "2024-02-19T11:22:35+08:00",
                "name": "selftest_tcp"
            },
            {
                "created_time": "2024-02-19T11:22:35+08:00",
                "name": "selftest_tls"
            },
            {
                "created_time": "2024-02-19T11:22:35+08:00",
                "name": "dhcp-events"
            },
            {
                "created_time": "2024-02-19T11:22:35+08:00",
                "name": "dns-events"
            }
        ],
        "event": [
            {
                "id": 789,
                "timestamp": "2024-02-17T17:20:15+08:00",
                "type": "drop",
                "inInterface": "乙太網路",
                "srcip": "************",
                "srcPort": 0,
                "destip": "*******",
                "destPort": 0,
                "protocol": "icmp",
                "description": "icmpv4 selftest drop"
            }
        ],
        "protocol": [
            "modbus",
            "tls",
            "dns",
            "ntp",
            "dhcp",
            "sip",
            "icmp",
            "http",
            "ftp",
            "http2",
            "imap",
            "smb",
            "smtp",
            "snmp",
            "ssh",
            "tftp",
            "sip",
            "enip",
            "nfs",
            "ntp",
            "alert",
            "drop"
        ]
    },
    "client6": {
        "rules": null,
        "event": null,
        "protocol": [
            "modbus",
            "tls",
            "dns",
            "ntp",
            "dhcp",
            "sip",
            "icmp",
            "http",
            "ftp",
            "http2",
            "imap",
            "smb",
            "smtp",
            "snmp",
            "ssh",
            "tftp",
            "sip",
            "enip",
            "nfs",
            "ntp",
            "alert",
            "drop"
        ]
    }
}
*/
func HandleIdpsReport(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {
		bodyText, err := io.ReadAll(r.Body)
		if err != nil {
			q.Q(err)
			RespondWithError(w, err)
			return
		}
		defer r.Body.Close()
		data := NewIdpsRePortInfo()
		err = json.Unmarshal(bodyText, &data)
		if err != nil {
			q.Q(err)
			RespondWithError(w, err)
			return
		}
		client := r.URL.Query().Get("client")
		if len(client) == 0 {
			err = errors.New("client: is empty")
			q.Q(err)
			RespondWithError(w, err)
			return
		}
		QC.idpsMutex.Lock()
		defer QC.idpsMutex.Unlock()
		c := NewIdpsRePortInfo()
		if v, ok := QC.IdpsReport[client]; ok {
			c = v
		}
		if len(data.StartTime) != 0 {
			c.StartTime = data.StartTime
		}
		if data.Event != nil {
			c.Event = data.Event
		}

		if data.RecordList != nil {
			c.RecordList = data.RecordList
		}
		if data.Rules != nil {
			c.Rules = data.Rules
		}
		if data.RulePacketsTotal != nil {
			c.RulePacketsTotal = data.RulePacketsTotal
		}
		QC.IdpsReport[client] = c
		return
	}
	client := r.URL.Query().Get("client")
	if len(client) == 0 {
		QC.idpsMutex.Lock()
		defer QC.idpsMutex.Unlock()
		if len(QC.IdpsReport) == 0 {
			err := errors.New("no client existed")
			q.Q(err)
			RespondWithError(w, err)
			return
		}
		jsonBytes, err := json.Marshal(QC.IdpsReport)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		_, err = w.Write(jsonBytes)
		if err != nil {
			q.Q(err)
		}
		return
	}
	QC.idpsMutex.Lock()
	v, ok := QC.IdpsReport[client]
	QC.idpsMutex.Unlock()
	if !ok {
		err := errors.New("no client existed")
		q.Q(err)
		RespondWithError(w, err)
		return
	}
	jsonBytes, err := json.Marshal(v)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}
func IdpsMcpTool() []MCPToolMetadata {
	tools := []MCPToolMetadata{
		{
			Name:        "idps_import_rules",
			Description: "To import rules from Domain Name by user input.The rules are downloaded and imported",
			Parameters: mcp.ToolInputSchema{
				Properties: map[string]any{
					"client": map[string]any{
						"type":        "string",
						"description": "service name or client name",
					},
					"url": map[string]any{
						"type":        "string",
						"description": "Domain Name by user input",
					},
				},
				Required: []string{"client", "url"},
				Type:     "object",
			},
		},
		{
			Name:        "idps_add_rule",
			Description: "To Add rules from a text by user input.",
			Parameters: mcp.ToolInputSchema{
				Properties: map[string]any{
					"client": map[string]any{
						"type":        "string",
						"description": "service name or client name",
					},
					"action": map[string]any{
						"type":        "string",
						"description": "action to take on the packet",
					},
					"sourceip": map[string]any{
						"type":        "string",
						"description": "source ip address",
					},
					"sourceport": map[string]any{
						"type":        "string",
						"description": "source port",
					},
					"protocol": map[string]any{
						"type":        "string",
						"description": "network protocol",
					},
					"destinationip": map[string]any{
						"type":        "string",
						"description": "destination ip address",
					},
					"destinationport": map[string]any{
						"type":        "string",
						"description": "destination port",
					},
					"msg": map[string]any{
						"type":        "string",
						"description": "the description of the rule",
					},
					"sid": map[string]any{
						"type":        "string",
						"description": "the sid of the rule",
					},
					"category": map[string]any{
						"type":        "string",
						"description": "the category name of the rule",
					},
				},
				Required: []string{"client", "action", "protocol", "msg", "sid"},
				Type:     "object",
			},
		},
		{
			Name:        "idps_delete_rules",
			Description: "To delete rules with category name",
			Parameters: mcp.ToolInputSchema{
				Properties: map[string]any{
					"client": map[string]any{
						"type":        "string",
						"description": "service name or client name",
					},
					"category": map[string]any{
						"type":        "string",
						"description": "category name of rule ",
					},
				},
				Required: []string{"client", "category"},
				Type:     "object",
			},
		},
	}
	return tools
}

func handleAddRuleTool(cmdinfo *CmdInfo, args map[string]interface{}) (string, error) {
	s, err := retrieveIdpsToolClientName(args)
	if err != nil {
		return "", err
	}
	cmdinfo.Client = s
	var act, srcip, srcport, proto, dstip, dstport, msg, sid, category string
	if v, ok := args["action"]; ok {
		act = v.(string)
	} else {
		return "", errors.New("please describe action")
	}
	if v, ok := args["sourceip"]; ok {
		srcip = v.(string)
	} else {
		srcip = "any"
	}
	if v, ok := args["sourceport"]; ok {
		srcport = v.(string)
	} else {
		srcport = "any"
	}
	if v, ok := args["destinationip"]; ok {
		dstip = v.(string)
	} else {
		dstip = "any"
	}
	if v, ok := args["destinationport"]; ok {
		dstport = v.(string)
	} else {
		dstport = "any"
	}
	if v, ok := args["protocol"]; ok {
		proto = v.(string)
	} else {
		return "", errors.New("please describe protocol")
	}
	if v, ok := args["msg"]; ok {
		msg = v.(string)
	} else {
		return "", errors.New("please describe msg")
	}
	if v, ok := args["sid"]; ok {
		sid = v.(string)
	} else {
		return "", errors.New("please describe sid")
	}
	if v, ok := args["category"]; ok {
		category = v.(string)
	} else {
		category = "aiAssistant"
	}

	return fmt.Sprintf(`idps rules add %v %v %v %v %v -> %v %v (msg:"%v";sid:%v;)`, category, act, proto, srcip, srcport, dstip, dstport, msg, sid), nil
}

func handleImportRuleTool(cmdinfo *CmdInfo, args map[string]interface{}) (string, error) {
	var url string
	s, err := retrieveIdpsToolClientName(args)
	if err != nil {
		return "", err
	}
	cmdinfo.Client = s
	if v, ok := args["url"]; ok {
		url = v.(string)
	} else {
		return "", fmt.Errorf("please describe the url")
	}
	cmd := fmt.Sprintf("idps rules import %s", url)
	return cmd, nil
}

func handleDeleteRuleTool(cmdinfo *CmdInfo, args map[string]interface{}) (string, error) {
	var categy string
	s, err := retrieveIdpsToolClientName(args)
	if err != nil {
		return "", err
	}
	cmdinfo.Client = s
	if v, ok := args["category"]; ok {
		categy = v.(string)
	} else {
		return "", fmt.Errorf("please describe the category")
	}
	cmd := fmt.Sprintf("idps rules delete %s", categy)
	return cmd, nil
}
func retrieveIdpsToolClientName(args map[string]interface{}) (string, error) {
	if v, ok := args["client"]; ok {
		name := v.(string)
		return name, nil
	}
	return "", fmt.Errorf("please describe the name")
}
