import React, { memo, useState, useMemo } from "react";
import { Input, Tag, Space, Tooltip } from "antd";
import {
  SettingOutlined,
  ThunderboltOutlined,
  DollarOutlined,
  RocketOutlined,
} from "@ant-design/icons";
import { useTheme } from "antd-style";

// Model categories and metadata
const MODEL_CATEGORIES = {
  openai: {
    label: "OpenAI",
    models: [
      {
        value: "gpt-4o",
        label: "GPT-4o",
        description: "Most capable model, best for complex tasks",
        icon: <RocketOutlined />,
        color: "purple",
        tier: "premium",
      },
      {
        value: "gpt-4o-mini",
        label: "GPT-4o Mini",
        description: "Fast and efficient, good for most tasks",
        icon: <ThunderboltOutlined />,
        color: "blue",
        tier: "standard",
      },
      {
        value: "gpt-3.5-turbo",
        label: "GPT-3.5 Turbo",
        description: "Cost-effective, good for simple tasks",
        icon: <DollarOutlined />,
        color: "green",
        tier: "basic",
      },
    ],
  },
  anthropic: {
    label: "Anthropic",
    models: [
      {
        value: "claude-3-sonnet",
        label: "Claude 3 Sonnet",
        description: "Balanced performance and speed",
        icon: <RocketOutlined />,
        color: "orange",
        tier: "premium",
      },
      {
        value: "claude-3-haiku",
        label: "Claude 3 Haiku",
        description: "Fast and lightweight",
        icon: <ThunderboltOutlined />,
        color: "cyan",
        tier: "standard",
      },
    ],
  },
  ollama: {
    label: "Ollama (Local)",
    models: [
      {
        value: "ollama/llama3",
        label: "Llama 3",
        description: "Open source, runs locally",
        icon: <SettingOutlined />,
        color: "volcano",
        tier: "local",
      },
      {
        value: "ollama/mistral",
        label: "Mistral",
        description: "Efficient local model",
        icon: <SettingOutlined />,
        color: "magenta",
        tier: "local",
      },
    ],
  },
};

const ModelSelector = memo(({ value, onChange, disabled, size = "small" }) => {
  const theme = useTheme();

  // Get current model info
  const getCurrentModelInfo = () => {
    for (const category of Object.values(MODEL_CATEGORIES)) {
      const model = category.models.find((m) => m.value === value);
      if (model) return model;
    }
    return null;
  };

  const currentModel = getCurrentModelInfo();

  // All available models flattened into a single array
  const allModels = useMemo(() => {
    return Object.values(MODEL_CATEGORIES).flatMap(
      (category) => category.models
    );
  }, []);

  return (
    <Space>
      {/* Current Model Tag */}
      {currentModel && (
        <Tooltip title={currentModel.description}>
          <Tag
            color={currentModel.color}
            icon={currentModel.icon}
            style={{ fontSize: 11, cursor: "help" }}
          >
            {currentModel.label}
          </Tag>
        </Tooltip>
      )}

      {/* Model Input */}
      <Input
        value={value}
        onChange={(e) => onChange(e.target.value)}
        size={size}
        style={{ minWidth: 140 }}
        placeholder="Enter model name"
        disabled={disabled}
      />
    </Space>
  );
});

ModelSelector.displayName = "ModelSelector";

export default ModelSelector;
