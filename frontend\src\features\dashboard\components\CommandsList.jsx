import React, { memo } from "react";
import { Card, Table, Tag, Spin, Typography } from "antd";
import dayjs from "dayjs";

const { Text } = Typography;

/**
 * Component to display recent command details
 * @param {Object} props Component props
 * @param {Array} props.data Command data to display
 * @param {boolean} props.loading Loading state
 * @returns {JSX.Element} CommandsList component
 */
const CommandsList = ({ data = [], loading = false }) => {
  const columns = [
    {
      title: "Command",
      dataIndex: "command",
      key: "command",
      ellipsis: true,
      render: (text) => <Text strong>{text}</Text>,
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      width: 200,
      render: (status) => {
        let color = "default";
        if (status === "ok") color = "success";
        if (status === "error") color = "error";
        if (status === "running") color = "processing";

        return <Tag color={color}>{status || "pending"}</Tag>;
      },
    },
    {
      title: "Service",
      dataIndex: "client",
      key: "client",
      width: 150,
    },
    {
      title: "Time",
      dataIndex: "timestamp",
      key: "timestamp",
      width: 180,
      render: (timestamp) => {
        if (!timestamp) return "-";
        return dayjs(timestamp).format("YYYY/MM/DD HH:mm:ss");
      },
    },
  ];

  return (
    <Card
      title="Recent Commands"
      variant="borderless"
      extra={<Text type="secondary">Last {data.length} commands</Text>}
    >
      <Spin spinning={loading}>
        <Table
          columns={columns}
          dataSource={data}
          rowKey={(record) => record.command + record.timestamp}
          pagination={false}
          size="small"
          scroll={{ y: 240 }}
        />
      </Spin>
    </Card>
  );
};

export default memo(CommandsList);
