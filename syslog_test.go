package mnms

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"os/signal"
	"path"
	"path/filepath"
	"runtime"
	"slices"
	"strings"
	"testing"
	"time"

	"github.com/gorilla/websocket"
	"github.com/influxdata/go-syslog/v3"
	"github.com/qeof/q"
)

func killNmsctlProcesses() {
	var cmd *exec.Cmd
	q.Q("killing nmnsctl processes")
	// XXX terrible dangerous killing of all mnmsctl
	// root service
	if runtime.GOOS == "windows" {
		cmd = exec.Command("taskkill", "/f", "/im", "bbnmssvc.exe")
	} else {
		cmd = exec.Command("killall", "bbnmssvc")
	}
	err := cmd.Run()
	if err != nil {
		q.Q(err)
	}
	// network service
	if runtime.GOOS == "windows" {
		cmd = exec.Command("taskkill", "/f", "/im", "bbrootsvc.exe")
	} else {
		cmd = exec.Command("killall", "bbrootsvc")
	}
	err = cmd.Run()
	if err != nil {
		q.Q(err)
	}
}

func TestSyslog(t *testing.T) {
	//RFC 3164 Page 10, Facility=20 and Severity=5 would have Priority value of 165
	syslogMsg := []byte("<165>Nov 11 12:34:56 myhost mytag: this is a syslog message")

	facility, severity, err := SyslogParsePriority(string(syslogMsg))
	if err != nil {
		t.Fatal(err)
	}
	q.Q(facility, severity)
	if facility != 20 {
		t.Fatal("facility is wrong")
	}
	if severity != 5 {
		t.Fatal("severity is wrong")
	}

	//logger -d -s -n localhost -P 5514 --rfc3164 -p local3.alert local3 alert syslog test
	//<153>Feb 12 02:23:21 cs-186432676255-default bob_bae: local3 alert syslog test

	QC.RemoteSyslogServerAddr = "localhost:5514"
	err = SendSyslog(LOG_NOTICE, "testsyslog", "this is a test alert syslog message")
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		killNmsctlProcesses()
	}()
	go func() {
		cmd := exec.Command("./bbrootsvc/bbrootsvc", "-n", "root", "-O", "root.log")
		err := cmd.Run()
		if err != nil {
			q.Q(err)
		}
	}()
	err = waitForRoot()
	if err != nil {
		q.Q(err)
		t.Fatal(err)
	}
	go func() {
		for {
			time.Sleep(1 * time.Second)
			q.Q("sending syslog alert for websock")
			err = SendSyslog(LOG_NOTICE, "syslog_test", "websock test alert syslog message")
			if err != nil {
				q.Q("Fatal", err)
				break
			}
			q.Q("sent syslog alert for websock")
		}
	}()
	connectWebsock()
}

func connectWebsock() {
	interrupt := make(chan os.Signal, 1)
	signal.Notify(interrupt, os.Interrupt)
	u := url.URL{Scheme: "ws", Host: "localhost:27182", Path: "/api/v1/ws"}
	q.Q(u.String())
	c, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		q.Q(err)
		return
	}
	defer c.Close()
	done := make(chan struct{})
	go func() {
		defer close(done)
		for {
			_, message, err := c.ReadMessage()
			if err != nil {
				q.Q(err)
				return
			}
			q.Q("read websock", string(message))
		}
	}()
	ticker := time.NewTicker(time.Second)
	count := 1
	defer ticker.Stop()
	for {
		select {
		case <-done:
			return
		case t := <-ticker.C:
			msg := fmt.Sprintf(`{ "kind": "syslog_test", "level": 3, "message": "ticker %v" }`, t)
			count++
			if count > 3 {
				return
			}
			err := c.WriteMessage(websocket.TextMessage, []byte(msg))
			if err != nil {
				q.Q(err)
				return
			}
			q.Q("wrote to websock", msg)
		case <-interrupt:
			q.Q("interrupt")
			err := c.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, "closing at interrupt"))
			if err != nil {
				q.Q(err)
				return
			}
			select {
			case <-done:
			case <-time.After(time.Second):
			}
			return
		}
	}
}

func TestSyslogFromRealDevice(t *testing.T) {
	defer func() {
		killNmsctlProcesses()
	}()
	go func() {
		var cmd *exec.Cmd
		if runtime.GOOS == "windows" {
			cmd = exec.Command("./bbrootsvc/bbrootsvc.exe", "-n", "root")
		} else {
			cmd = exec.Command("./bbrootsvc/bbrootsvc", "-n", "root")
		}
		err := cmd.Run()
		if err != nil {
			q.Q(err)
		}
	}()
	// wait root
	time.Sleep(3 * time.Second)
	// network service
	go func() {
		var cmd *exec.Cmd
		if runtime.GOOS == "windows" {
			cmd = exec.Command("./bbnmssvc/bbnmssvc.exe", "-n", "client", "-r", "http://localhost:27182", "-rs", "localhost:5514")
		} else {
			cmd = exec.Command("./bbnmssvc/bbnmssvc", "-n", "client", "-r", "http://localhost:27182", "-rs", "localhost:5514")
		}
		err := cmd.Run()
		if err != nil {
			q.Q(err)
		}
	}()
	// wait root
	time.Sleep(10 * time.Second)

	// start
	RootURL := "http://localhost:27182"

	ifaceaddrs, err := IfnetAddresses()
	if err != nil {
		t.Fatalf("IfnetAddresses %v", err)
	}

	//login
	loginurl := RootURL + "/api/v1/login"
	jsonBytes, err := json.Marshal(map[string]string{
		"user":     "admin",
		"password": "default",
	})
	if err != nil {
		t.Fatalf("error: %v\n", err)
		return
	}
	resp, err := http.Post(loginurl, "application/json", bytes.NewBuffer(jsonBytes))
	if err != nil {
		t.Fatalf("error: %v\n", err)
		return
	}
	if resp.StatusCode != http.StatusOK {
		resText, _ := io.ReadAll(resp.Body)
		t.Fatalf("error: %v\n", string(resText))
		return
	}
	if resp == nil {
		t.Fatalf("error: %v\n", "response should not be nil")
		return
	}
	defer resp.Body.Close()

	//get token
	var respBody map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&respBody)
	if err != nil {
		t.Fatalf("error: %v\n", err)
		return
	}
	token, ok := respBody["token"].(string)
	if !ok {
		t.Fatalf("error: %v\n", "token is not string")
		return
	}

	// get device list
	devicesurl := RootURL + "/api/v1/devices"
	devcieResp, err := GetWithToken(devicesurl, token)
	if err != nil {
		t.Fatalf("error: %v\n", err)
		return
	}
	if devcieResp.StatusCode != 200 {
		deviceRespText, _ := io.ReadAll(devcieResp.Body)
		t.Fatalf("error: %v\n", string(deviceRespText))
		return
	}
	if devcieResp == nil {
		t.Fatalf("error: %v\n", "response should not be nil")
		return
	}
	defer devcieResp.Body.Close()

	var devcieRespBody map[string]DevInfo
	err = json.NewDecoder(devcieResp.Body).Decode(&devcieRespBody)
	if err != nil {
		t.Fatalf("error: %v\n", err)
		return
	}
	var deviceNumber int
	for k, _ := range devcieRespBody {
		if k != "11-22-33-44-55-66" {
			deviceNumber = deviceNumber + 1
			//t.Logf("%-18s %-18s %-18s %-18s\n", v.IPAddress, v.Mac, v.ModelName, v.Hostname)
		}
	}
	//t.Logf("Device number : %d\n", deviceNumber)

	// test syslog
	//SyslogServerArray := strings.Split(SyslogServer, ":")
	//SyslogServerIP := SyslogServerArray[0]
	//SyslogServerPort := SyslogServerArray[1]
	SyslogServerIP := ""
	SyslogServerPort := "5514"
	TestDeviceIP := ""
	TestDeviceMac := ""

	// select syslog
	select_device := false
	for k, v := range devcieRespBody {
		if k != "11-22-33-44-55-66" {
			if strings.Contains(v.ModelName, "EHG7508") {
				for _, strip := range ifaceaddrs {
					ip := net.ParseIP(v.IPAddress)
					_, ipNet, _ := net.ParseCIDR(strip + "/24")
					if ipNet.Contains(ip) {
						// v.IPAddres in SyslogServerIP/24
						select_device = true
						TestDeviceMac = v.Mac
						TestDeviceIP = v.IPAddress
						SyslogServerIP = strip
						break
					}
				}
				if select_device {
					break
				}
			}
		}
	}
	if !select_device {
		t.Logf("%s\n", "Pass: not found EHG7508 device, pass syslog test.")
		return
	}

	//t.Log("\nTest device :\n")
	//t.Logf("%-18s : %-18s\n", "IP address", TestDeviceIP)
	//t.Logf("%-18s : %-18s\n", "Mac address", TestDeviceMac)
	//t.Logf("%-18s : %-18s\n", "Syslog server IP", SyslogServerIP)
	//t.Logf("%-18s : %-18s\n", "Syslog server Mac", SyslogServerPort)
	//t.Logf("\n")
	// enable snmp
	//t.Logf("%s\n", "Process : enable snmp")
	PostCmd(RootURL, token, "snmp enable %s")
	time.Sleep(3 * time.Second)

	cmd := fmt.Sprintf("snmp update community %s public private", TestDeviceMac)
	PostCmd(RootURL, token, cmd)
	time.Sleep(5 * time.Second)

	// set syslog
	//t.Logf("%s\n", "Process : set syslog")
	//config syslog set [MAC Address] [Enable] [Server IP] [Server Port] [Log Level] [Log to Flash]
	cmd = fmt.Sprintf("config syslog set %s 1 %s %s 1 1", TestDeviceMac, SyslogServerIP, SyslogServerPort)
	PostCmd(RootURL, token, cmd)
	time.Sleep(3 * time.Second)

	// reset device and device will tranfer syslog
	//t.Logf("%s\n", "Process : reset device")
	cmd = fmt.Sprintf("reset %s", TestDeviceMac)
	PostCmd(RootURL, token, cmd)
	time.Sleep(3 * time.Second)

	//t.Logf("%s\n", "Process : Wait 20 seconds")
	time.Sleep(20 * time.Second)
	// get syslog
	//t.Logf("%s\n", "Process : get syslog")
	syslogURL := RootURL + "/api/v1/syslogs?number=10"
	syslogResp, err := GetWithToken(syslogURL, token)
	if err != nil {
		t.Fatalf("error: %v\n", err)
		return
	}
	if syslogResp.StatusCode != 200 {
		syslogRespText, _ := io.ReadAll(syslogResp.Body)
		t.Fatalf("error: %v\n", string(syslogRespText))
		return
	}
	if syslogResp == nil {
		t.Fatalf("error: %v\n", "response should not be nil")
		return
	}
	defer syslogResp.Body.Close()

	var syslogRespBody []syslog.Base
	err = json.NewDecoder(syslogResp.Body).Decode(&syslogRespBody)
	if err != nil {
		t.Fatalf("error: %v\n", err)
		return
	}
	success := false
	for _, v := range syslogRespBody {
		if strings.Contains(*v.Message, "syslog") && !strings.Contains(*v.Message, "command") && strings.Contains(*v.Message, TestDeviceIP) {
			t.Logf("Message : %v\n", *v.Message)
			success = true
			break
		}
	}
	if success {
		t.Logf("Result : Device can transfer syslog to root.\n")
	} else {
		t.Logf("Result : Device can not transfer syslog to root.\n")
	}
	// Restore syslog
	//t.Logf("\n%s\n", "Process : Restore syslog")
	cmd = fmt.Sprintf("config syslog set %s 2 0.0.0.0 514 3 2", TestDeviceMac)
	PostCmd(RootURL, token, cmd)
}

func Test_SyslogList(t *testing.T) {
	fileName := filepath.Base(QC.SyslogLocalPath)
	fileNamePrefix := strings.TrimSuffix(fileName, filepath.Ext(fileName))
	fileNamePostfix := strings.TrimPrefix(fileName, fileNamePrefix)

	// add fake syslog file prefix-2024-02-22T11-52-48.492.posfix
	genFiles := []string{
		fmt.Sprintf("%s-2024-01-22T11-52-48.492%s", fileNamePrefix, fileNamePostfix),
		fmt.Sprintf("%s-2024-02-22T11-52-48.492%s", fileNamePrefix, fileNamePostfix),
		fmt.Sprintf("%s-2024-03-22T11-52-48.492%s.gz", fileNamePrefix, fileNamePostfix),
	}

	for _, f := range genFiles {
		err := os.WriteFile(f, []byte("test"), 0644)
		if err != nil {
			t.Fatal(err)
		}
		defer os.Remove(f)
	}

	cmdinfo := CmdInfo{
		Kind:    "syslog",
		Command: "syslog list",
	}

	ret := SyslogListLogFilesCmd(&cmdinfo)
	q.Q(ret)

	// ret.Result should be a list of syslog files in json format, like: ["syslog-2024-01-22T11-52-48.492", "syslog-2024-02-22T11-52-48.492", "syslog-2024-03-22T11-52-48.492"]
	// check if the list contains the generated files
	files := []string{}
	err := json.Unmarshal([]byte(ret.Result), &files)
	if err != nil {
		t.Fatal(err)
	}

	// check syslog list
	found := false
	for _, f := range files {
		// if gz file, t.fatal
		if strings.HasSuffix(f, ".gz") {
			t.Fatalf("file %s should not be in syslog list", f)
		}

		for _, g := range genFiles {
			if f == g {
				found = true
				continue
			}
		}
		if !found {
			t.Fatalf("file %s not found in syslog list", f)
		}
	}
}

func Test_SyslogGetUrl(t *testing.T) {
	QC.NmsServiceURL = "http://localhost:27192"
	oldPath := QC.SyslogLocalPath
	QC.SyslogLocalPath = "testsyslog.log"
	defer func() {
		QC.SyslogLocalPath = oldPath
	}()

	// create a file with some fake syslog content
	c := []string{}
	for i := 0; i < 100; i++ {
		c = append(c, fmt.Sprintf("<1>Jan  3 16:01:53 host GoTest: new line: %d", i))
	}
	err := os.WriteFile(QC.SyslogLocalPath, []byte(strings.Join(c, "\n")), 0644)
	if err != nil {
		t.Fatal(err)
	}
	defer os.Remove(QC.SyslogLocalPath)

	cmdinfo := CmdInfo{
		Kind:    "syslog",
		Command: "syslog geturl testsyslog.log 10",
	}

	ret := SyslogGetUrlCmd(&cmdinfo)
	retUrl := ret.Result
	q.Q(retUrl)
	// get filename from url
	u, err := url.Parse(retUrl)
	if err != nil {
		t.Fatal(err)
	}
	filename := filepath.Base(u.Path)

	path, err := EnsureStaticFilesFolderExist()
	if err != nil {
		t.Fatal(err)
	}
	path = filepath.Join(path, "syslogs", filename)

	// check file content
	b, err := os.ReadFile(path)
	if err != nil {
		t.Fatal(err)
	}
	lines := strings.Split(string(b), "\n")
	if len(lines) != 10 {
		t.Fatalf("expected 10 lines, got %d", len(lines))
	}
}

func Test_GetSyslogWithParameters(t *testing.T) {
	// create a file with some fake syslog content
	oldPath := QC.SyslogLocalPath
	QC.SyslogLocalPath = "testsyslog.log"
	defer func() {
		QC.SyslogLocalPath = oldPath
	}()

	// create old file first
	c_prevDay := []string{}
	for i := 0; i < 60; i++ {
		c_prevDay = append(c_prevDay, fmt.Sprintf("<1>Jan  2 16:%02d:53 host old: new line: %d", i, i))
	}
	err := os.WriteFile("testsyslog-2024-01-02T11-52-48.492.log", []byte(strings.Join(c_prevDay, "\n")), 0644)
	if err != nil {
		t.Fatal(err)
	}
	defer os.Remove("testsyslog-2024-01-02T11-52-48.492.log")

	// create new file
	c := []string{}
	for i := 0; i < 60; i++ {
		c = append(c, fmt.Sprintf("<1>Jan  3 16:%02d:53 host new: new line: %d", i, i))
	}
	err = os.WriteFile(QC.SyslogLocalPath, []byte(strings.Join(c, "\n")), 0644)
	if err != nil {
		t.Fatal(err)
	}
	defer os.Remove(QC.SyslogLocalPath)

	// getSyslogWithTime
	// get this year
	year := time.Now().Year()
	start := fmt.Sprintf("%d/01/03 16:00:00", year)
	end := fmt.Sprintf("%d/01/03 16:10:00", year)
	logs, err := getSyslogsWithTime([]string{"testsyslog.log"}, start, end, 5)
	if err != nil {
		t.Fatal(err)
	}
	if len(logs) != 5 {
		t.Fatalf("expected 5 lines, got %d", len(logs))
	}

	// getSyslogWithLast 5
	logs, err = getSyslogsWithLast([]string{"testsyslog.log"}, 5)
	if err != nil {
		t.Fatal(err)
	}
	if len(logs) != 5 {
		t.Fatalf("expected 5 lines, got %d", len(logs))
	}
	for i, log := range logs {
		msg := *log.Message
		idx := 55 + i
		if msg != "new line: "+fmt.Sprint(idx) {
			t.Fatalf("expected new line: %d, got %s", idx, msg)
		}
	}

	// getSyslogWithLast 2 files 100
	logs, err = getSyslogsWithLast([]string{"testsyslog.log", "testsyslog-2024-01-02T11-52-48.492.log"}, 100)
	if err != nil {
		t.Fatal(err)
	}
	if len(logs) != 100 {
		t.Fatalf("expected 100 lines, got %d", len(logs))
	}
	// should get 20-59 old and 0-59 new
	for i, log := range logs {
		if i < 40 {
			msg := *log.Message
			idx := 20 + i
			if msg != "new line: "+fmt.Sprint(idx) {
				t.Fatalf("expected new line: %d, got %s", idx, msg)
			}
		} else {
			msg := *log.Message
			idx := i - 40
			if msg != "new line: "+fmt.Sprint(idx) {
				t.Fatalf("expected new line: %d, got %s", idx, msg)
			}
		}
	}
}

func Test_SyslogGeneratedList(t *testing.T) {
	// add fake file in /files/syslogs

	fileDir, err := EnsureStaticFilesFolderExist()
	if err != nil {
		q.Q(err)
		return
	}
	fileDir = path.Join(fileDir, "syslogs")
	// mkdir if not exist
	err = os.MkdirAll(fileDir, 0755)
	if err != nil {
		q.Q(err)
		return
	}

	for i := 0; i < 10; i++ {
		filename := fmt.Sprintf("syslog_test_%d.log", i)
		err := os.WriteFile(path.Join(fileDir, filename), []byte("test"), 0644)
		if err != nil {
			t.Fatal(err)
		}
		defer os.Remove(path.Join(fileDir, filename))
	}

	QC.Kind = "syslog"
	cmdinfo := CmdInfo{Command: "syslog files list"}
	cmdinfo = *RunCmd(&cmdinfo)

	// ret.Result should be a list of syslog files in json format, like: ["syslog-2024-01-22T11-52-48.492", "syslog-2024-02-22T11-52-48.492", "syslog-2024-03-22T11-52-48.492"]
	// check if the list contains the generated files
	files := []string{}
	err = json.Unmarshal([]byte(cmdinfo.Result), &files)
	if err != nil {
		t.Fatal(err)
	}
	for i := 0; i < 10; i++ {
		filename := fmt.Sprintf("syslog_test_%d.log", i)
		if !slices.Contains(files, filename) {
			t.Fatalf("file %s not found in syslog list", filename)
		}
	}
}

func Test_SyslogGeneratedRemove(t *testing.T) {
	// add fake file in /files/syslogs
	fileDir, err := EnsureStaticFilesFolderExist()
	if err != nil {
		q.Q(err)
		return
	}
	fileDir = path.Join(fileDir, "syslogs")
	// mkdir if not exist
	err = os.MkdirAll(fileDir, 0755)
	if err != nil {
		q.Q(err)
		return
	}

	filename := "syslog_test_10.log"
	err = os.WriteFile(path.Join(fileDir, filename), []byte("test"), 0644)
	if err != nil {
		t.Fatal(err)
	}

	QC.Kind = "syslog"
	cmdinfo := CmdInfo{Command: "syslog file rm syslog_test_10.log"}
	cmdinfo = *RunCmd(&cmdinfo)

	t.Log(cmdinfo)

	// check if the file is removed
	if _, err := os.Stat(path.Join(fileDir, filename)); !os.IsNotExist(err) {
		defer os.Remove(path.Join(fileDir, filename))
		t.Fatalf("file %s should be removed", filename)
	}
}

func Test_SyslogSeverityForward(t *testing.T) {
	oldSeverityRangeForward := QC.SyslogSeverityRngFwd
	QC.SyslogSeverityRngFwd.MinSeverity = -1
	QC.SyslogSeverityRngFwd.MaxSeverity = -1

	message := "<165>Feb 12 02:23:21 cs-186432676255-default bob_bae: local3 alert syslog test"
	_, severity, err := SyslogParsePriority(message)
	if err != nil {
		t.Fatal(err)
	}

	inRange := checkSeverityRange(severity)
	if inRange {
		t.Fatal("severity should not be in range")
	}

	QC.SyslogSeverityRngFwd.MinSeverity = 0
	QC.SyslogSeverityRngFwd.MaxSeverity = 7
	inRange = checkSeverityRange(severity)
	if !inRange {
		t.Fatal("severity should be in range")
	}

	QC.SyslogSeverityRngFwd = oldSeverityRangeForward
}
