import { describe, it, expect, vi, beforeEach } from "vitest";
import { screen } from "@testing-library/react";
import { renderWithProviders } from "../../tests/test-utils";
import { DevicesComponent } from "./devices";
import { useDeviceFilters } from "../../features/devices/hooks/useDeviceFilters";
import { useContextMenu } from "react-contexify";
import { useAppStore } from "../../store/store";
import { useSettingStore } from "../../store/setting-store";

// Mock dependencies
vi.mock("@tanstack/react-router", () => ({
  createFileRoute: vi.fn(() => () => ({})),
}));

// Mock the hooks
vi.mock("../../features/devices/hooks/useDeviceFilters", () => ({
  useDeviceFilters: vi.fn(),
}));

vi.mock("react-contexify", () => ({
  useContextMenu: vi.fn(),
}));

vi.mock("../../store/store", () => ({
  useAppStore: vi.fn(),
}));

vi.mock("../../store/setting-store", () => ({
  useSettingStore: vi.fn(),
}));

// Mock the components
vi.mock("../../features/devices", () => ({
  DeviceTable: () => (
    <div data-testid="device-table">Device Table Component</div>
  ),
  DeviceContextMenu: () => (
    <div data-testid="device-context-menu">Device Context Menu Component</div>
  ),
}));

describe("DevicesComponent", () => {
  beforeEach(() => {
    // Setup mock return values
    useDeviceFilters.mockReturnValue({
      filteredData: [],
      isFetching: false,
      refetch: vi.fn(),
      inputSearch: "",
      setInputSearch: vi.fn(),
    });

    useContextMenu.mockReturnValue({
      show: vi.fn(),
      hideAll: vi.fn(),
    });

    useAppStore.mockReturnValue({
      selectedRowKeys: [],
      setSelectedRowKeys: vi.fn(),
      openDialogs: vi.fn(),
    });

    useSettingStore.mockReturnValue({
      inventoryType: "device",
      changeInventoryType: vi.fn(),
    });
  });

  it("renders the devices page with table and context menu", () => {
    renderWithProviders(<DevicesComponent />);

    // Check if the device table is rendered
    expect(screen.getByTestId("device-table")).toBeInTheDocument();

    // Check if the device context menu is rendered
    expect(screen.getByTestId("device-context-menu")).toBeInTheDocument();
  });
});
