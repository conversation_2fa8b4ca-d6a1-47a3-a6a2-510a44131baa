import React, { useCallback } from "react";
import { generateCommand } from "../../../utils/generate-commands";
import { App, Form, Input, Modal } from "antd";
import { useSendCommand } from "../../../services/mutations";
import { handleCommandExecution } from "../../../utils/command-execution";

const FirmwareUploadDialog = ({ data, onClose }) => {
  const { notification } = App.useApp();
  const sendCommand = useSendCommand();
  const [form] = Form.useForm();

  const handleSubmit = useCallback(
    (values) => {
      let commands = [];
      if (Array.isArray(data)) {
        data.map((item) => {
          const command = generateCommand(item, "", values, "firmware");
          commands.push(command);
        });
        commands = commands.flat();
      } else {
        commands = generateCommand(data.mac, "", values, "firmware");
      }
      handleCommandExecution(commands, sendCommand, notification);
      onClose();
    },
    [data, data?.mac, sendCommand, notification, onClose]
  );
  return (
    <Modal
      title="Firmware Upload"
      maskClosable={false}
      open
      destroyOnClose
      onCancel={onClose}
      okButtonProps={{ autoFocus: true, htmlType: "submit" }}
      style={{ top: 20 }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            handleSubmit(values);
            form.resetFields();
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form form={form} layout="vertical" name="fw_upload_form" clearOnDestroy>
        <Form.Item
          name="fwUrl"
          label="Firmware Url"
          required
          rules={[
            {
              required: true,
              message: "Please input the f/w url !",
            },
            {
              type: "url",
              message: "input type should be url!",
            },
          ]}
        >
          <Input.TextArea />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default React.memo(FirmwareUploadDialog);
