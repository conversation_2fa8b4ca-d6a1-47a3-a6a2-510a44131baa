package llm

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"mnms/llm/envconfig"
	"os"
	"slices"
	"strconv"
	"strings"
	"time"

	"net/http"
	"net/url"
)

// Message is a struct that represents a message object that can be sent to LLM service.
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type Completer interface {
	// Completer is an interface that provides a method for generating
	// a completion string based on a given prompt. The Complete method
	// takes a prompt as input and returns a completed string and any
	// error that occurred during the completion process.
	Complete(ctx context.Context, msgs []Message) (string, error)
	// FormattedOutput is an interface that provides a method for generating a formatted output
	// the output is formatted based on the given messages and response. it will return a object which
	// type is same as the response type. desc is the response data description
	FormattedOutput(ctx context.Context, msgs []Message, desc string, response any) (any, error)
	FormattedOutputWithSchema(ctx context.Context, msgs []Message, desc string, schamaName string, schema JSONSchemaDefinition) (map[string]any, error)
}

// Embedder is an interface that provides methods for embedding data.
type Embedder interface {
	Embedding(ctx context.Context, prompt string) ([]float32, error)
}

// CompleteEmbedder Completer + Embedder
type CompleteEmbedder interface {
	Completer
	Embedder
}

// LLMConfigurator is an interface that provides methods for setting and getting a LargeLanguageModel
type LLMConfigurator interface {
	Set(llm *LargeLanguageModel)
	Get() *LargeLanguageModel
}

// LLMClient is an interface that provides methods for a large language model
type LLMClient interface {
	CompleteEmbedder
	LLMConfigurator
}

// LargeLanguageModel is an interface that represents a large-scale language model.
// It provides methods for generating text based on given prompts.
type LargeLanguageModel struct {
	Type               string `json:"type" mapstructure:"type"` // open-ai or ollama
	Host               string `json:"host" mapstructure:"host"`
	Port               int    `json:"port" mapstructure:"port"`
	CompletionEndPoint string `json:"completion_ep" mapstructure:"completion_ep"` // completion endpoint
	EmbeddingEndPoint  string `json:"embedding_ep" mapstructure:"embedding_ep"`   // embedding endpoint
	Model              string `json:"model" mapstructure:"model"`
	APIKey             string `json:"api_key" mapstructure:"api_key"`
}

// GetKeyFromEnv is a method that returns the API key from the environment
func (m *LargeLanguageModel) GetAPIKey() string {
	if m.APIKey != "" {
		return m.APIKey
	}

	key := os.Getenv("NIMBL_LLM_API_KEY")
	if key != "" {
		return key
	}
	return os.Getenv("OPENAI_API_KEY")
}

// String returns the string representation of the LargeLanguageModel
func (m *LargeLanguageModel) String() string {
	jsonBytes, _ := json.Marshal(m)
	return string(jsonBytes)
}

// Serialize returns the serialized representation of the LargeLanguageModel
func (m *LargeLanguageModel) Serialize() ([]byte, error) {
	return json.Marshal(m)
}

func hasPort(rawURL string) bool {
	u, err := url.Parse(rawURL)
	if err != nil {
		// on parse error, we treat it as “no port”
		return false
	}
	return u.Port() != ""
}

// GetAPIURL is a method that returns the complete URL for accessing the specified API
func (m *LargeLanguageModel) GetAPIURL(api string) (string, error) {
	var hostURL string

	hostURL = m.Host

	if m.Port != 0 && !hasPort(hostURL) {
		hostURL = fmt.Sprintf("%s:%d", hostURL, m.Port)
	}

	switch api {
	case "completion", "chat":
		u := fmt.Sprintf("%s%s", hostURL, m.CompletionEndPoint)
		_, err := url.Parse(u)
		return u, err
	case "embedding":
		u := fmt.Sprintf("%s%s", hostURL, m.EmbeddingEndPoint)
		_, err := url.Parse(u)
		return u, err
	default:
		return "", fmt.Errorf("unknown api %s", api)
	}

}

// DeserializeLargeLanguageModel deserializes the LargeLanguageModel from the given byte slice
func DeserializeLargeLanguageModel(data []byte) (*LargeLanguageModel, error) {
	var llmModel LargeLanguageModel
	err := json.Unmarshal(data, &llmModel)
	if err != nil {
		return nil, err
	}
	return &llmModel, nil
}

var supportedOpenAIModels = []string{"gpt-4.1", "gpt-4o", "o4-mini", "o3-mini"}

// CheckOllamaModel fetches the currently running model from Ollama's API.
// If it matches modelName, that name is returned; otherwise the actual model is returned.
func CheckOllamaModel(modelName string) (string, error) {
	// 1. Load configuration
	host, err := envconfig.LLMHost()
	if err != nil {
		return "", fmt.Errorf("failed to get Ollama host: %w", err)
	}
	token, err := envconfig.LLMToken()
	if err != nil {
		// proceed without token if not set
		token = ""
	}

	// 2. Prepare HTTP GET to /api/ps
	url := fmt.Sprintf("%s/api/ps", strings.TrimRight(host, "/"))
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}
	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("The ollama server %s is not running: %w", host, err)
	}
	defer resp.Body.Close()

	// 3. Decode JSON response
	type ModelInfo struct {
		Name string `json:"name"`
	}
	type PSResponse struct {
		Models []ModelInfo `json:"models"`
	}
	var response PSResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("failed to parse JSON: %w", err)
	}
	if len(response.Models) == 0 {
		return "", errors.New("no running models found")
	}

	// 4. Compare and return
	if response.Models[0].Name == modelName {
		return modelName, nil
	}
	return response.Models[0].Name, nil
}

// CreateLargeLanguageModelWithModel
func CreateLargeLanguageModelWithModel(llm, model string) (*LargeLanguageModel, error) {
	// if model is in supportedOpenAIModels, return GPTSettings()
	if llm == "openai" {
		if slices.Contains(supportedOpenAIModels, model) {
			// check api key
			_, err := envconfig.LLMAPIKey()
			if err != nil {
				fmt.Fprintf(os.Stderr, "WARNING: Missing NIMBL_LLM_API_KEY environment variable, the AI assistant will not work (model: %s)\n", model)
			}
			return GPTSettings(), nil
		} else {
			return nil, fmt.Errorf("NIMBL does not support OpenAI model %s, or the model is not OpenAI's model", model)
		}
	}

	if llm == "ollama" {
		runningModel, err := CheckOllamaModel(model)
		if err != nil {
			return nil, err
		}
		if runningModel != model {
			fmt.Fprintf(os.Stderr, "WARNING: Ollama is running model %s, but the requested model is %s NIMBL will use the running model %s\n", runningModel, model, runningModel)
		}
		// ollama
		llmsettings := OllamaSettings(model)
		defaulturl := llmsettings.Host + ":" + strconv.Itoa(llmsettings.Port)
		// check host
		host, err := envconfig.LLMHost()
		if err != nil {
			fmt.Fprintf(os.Stderr, "WARNING: Missing NIMBL_LLM_HOST environment variable, use default host %s instead (model: %s)\n", defaulturl, model)
		} else {
			fmt.Fprintf(os.Stderr, "ollama URL: %s \n", host)
			llmsettings.Host = host
		}
		return llmsettings, nil
	}
	return nil, fmt.Errorf("unknown LLM vendor %s", llm)
}

// GPTSettings is a struct that contains the settings for the GPT-3 model
func GPTSettings() *LargeLanguageModel {

	return &LargeLanguageModel{
		Type:               "open-ai",
		Host:               "https://api.openai.com",
		Port:               0,
		CompletionEndPoint: "/v1/chat/completions",
		EmbeddingEndPoint:  "/v1/embeddings",
		Model:              "gpt-3.5-turbo",
		APIKey:             "",
	}
}

// OllamaSettings is a struct that contains the settings for the Ollama model
func OllamaSettings(model string) *LargeLanguageModel {
	return &LargeLanguageModel{
		Type:               "ollama",
		Host:               "http://localhost",
		Port:               11434,
		CompletionEndPoint: "/complete",
		EmbeddingEndPoint:  "/embed",
		Model:              model,
		APIKey:             "",
	}
}

// Valid 	ckeck LargeLanguageModel is valid
func (m *LargeLanguageModel) Valid() error {
	// type should be either open-ai or ollama
	if m.Type != "open-ai" && m.Type != "ollama" {
		return fmt.Errorf("unknown LLM server name (support open-ai | ollama )%s", m.Type)
	}
	llmClient, err := NewLLMClient(m)
	if err != nil {
		return fmt.Errorf("failed to create LLM client: %w", err)
	}
	err = ValidateLLMConnection(llmClient)
	if err != nil {
		return fmt.Errorf("failed to validate LLM connection: %w", err)
	}
	return nil
}
