import React, { memo } from "react";
import PropTypes from "prop-types";
import { useAppStore } from "../../store/store";
import { DIALOG_COMPONENTS } from "./dialog-mapping";
import { DIALOG_TYPES } from "../../constants/dialog-types";

const Dialog = memo(({ dialog }) => {
  const { closeDialogs } = useAppStore();
  const { id, data } = dialog;

  const handleClose = () => {
    closeDialogs(id);
  };

  const DialogComponent = DIALOG_COMPONENTS[id];

  if (!DialogComponent) {
    console.error(`Dialog component not found for id: ${id}`);
    return null;
  }

  return <DialogComponent data={data} onClose={handleClose} />;
});

Dialog.propTypes = {
  dialog: PropTypes.shape({
    id: PropTypes.oneOf(Object.values(DIALOG_TYPES)).isRequired,
    data: PropTypes.object,
  }).isRequired,
};

const Dialogs = () => {
  const { dialogs } = useAppStore();

  if (!dialogs?.length) return null;

  return dialogs.map((dialog) => <Dialog key={dialog.id} dialog={dialog} />);
};

export default memo(Dialogs);
