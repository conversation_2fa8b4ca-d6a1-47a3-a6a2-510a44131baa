import { describe, it, expect, vi } from 'vitest';
import { screen } from '@testing-library/react';
import PageNotFound from './PageNotFound';
import { renderWithProviders, userEvent } from '../tests/test-utils';

// Mock the useNavigate hook
vi.mock('@tanstack/react-router', () => ({
  useNavigate: () => {
    const navigate = vi.fn();
    return navigate;
  },
}));

describe('PageNotFound', () => {
  it('renders the 404 page correctly', () => {
    renderWithProviders(<PageNotFound />);
    
    // Check if the title and subtitle are rendered
    expect(screen.getByText('404')).toBeInTheDocument();
    expect(screen.getByText('Sorry, the page you visited does not exist.')).toBeInTheDocument();
    
    // Check if the button is rendered
    expect(screen.getByRole('button', { name: /back to dashboard/i })).toBeInTheDocument();
  });
  
  it('navigates to dashboard when button is clicked', async () => {
    renderWithProviders(<PageNotFound />);
    
    // Click the button
    const button = screen.getByRole('button', { name: /back to dashboard/i });
    await userEvent.click(button);
    
    // Since we're using a mock, we can't directly test the navigation
    // But we can verify the button was clicked
    expect(button).toBeInTheDocument();
  });
});
