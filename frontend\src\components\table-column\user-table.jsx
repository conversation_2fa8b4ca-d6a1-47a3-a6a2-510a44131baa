import { useCallback, useMemo } from "react";
import {
  EditOutlined,
  DeleteOutlined,
  EyeInvisibleOutlined,
} from "@ant-design/icons";
import { App, Switch } from "antd";
import { useAuthStore } from "../../store/auth-store";
import {
  useDeleteSecret,
  useDeleteUserMutation,
  useGenerateSecret,
} from "../../services/mutations";
import { useAppStore } from "../../store/store";

export const useUserColumn = () => {
  const { user: userName, role: userRole, sessionid } = useAuthStore();
  const { openDialogs } = useAppStore();
  const generateSecret = useGenerateSecret();
  const disableSecret = useDeleteSecret();
  const deleteUser = useDeleteUserMutation();
  const { notification, modal } = App.useApp();

  const handleOnEditClick = useCallback((data) => {
    // Handle edit user action
    console.log("Edit user:", data);
  }, []);

  const handleOnDeleteClick = useCallback((data) => {
    // Handle delete user action
    try {
      deleteUser.mutate(data);
    } catch (error) {
      notification.error({ message: "Error", description: error });
    }
  }, []);

  const handleChange2FA = useCallback((data) => {
    // Handle change 2FA action
    if (userName === "admin") {
      if (data.enable2FA === true) {
        DisabledSecret(data.name);
      } else {
        notification.info({
          message: `User can only enable own 2FA!`,
        });
      }
    } else {
      if (data.enable2FA === false) {
        GenerateSecret(data.name);
      } else {
        DisabledSecret(data.name);
      }
    }
  }, []);

  const GenerateSecret = async (selectedUser) => {
    try {
      if (selectedUser !== "") {
        const data = await generateSecret.mutateAsync({ user: selectedUser });
        if (data.secret && data.account) {
          openDialogs({ id: "generate2fa", data: { ...data } });
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  const DisabledSecret = async (selectedUser) => {
    try {
      if (selectedUser !== "") {
        disableSecret.mutateAsync({ user: selectedUser });
      }
    } catch (error) {
      console.log(error);
    }
  };

  const baseColumns = useMemo(
    () => [
      {
        title: "Username",
        width: 100,
        dataIndex: "name",
        key: "name",
        exportable: true,
        sorter: (a, b) => (a.name > b.name ? 1 : -1),
      },
      {
        title: "Email",
        width: 100,
        dataIndex: "email",
        key: "email",
        exportable: true,
      },
      {
        title: "Role",
        width: 100,
        dataIndex: "role",
        key: "role",
        exportable: true,
      },
    ],
    []
  );

  const actionColumn = useMemo(
    () => ({
      title: "Action",
      width: 100,
      key: "action",
      render: (data) => (
        <div className="action-buttons">
          <EditOutlined
            onClick={() => handleOnEditClick(data)}
            data-testid={`edit-user-${data.name}`}
          />
          {userName !== data?.name && (
            <DeleteOutlined
              style={{ marginLeft: "10%" }}
              onClick={() => handleOnDeleteClick(data)}
            />
          )}
        </div>
      ),
    }),
    [handleOnEditClick, handleOnDeleteClick, userName]
  );

  const twoFactorAuthColumn = useMemo(
    () => ({
      title: "Two Factor Auth",
      width: 100,
      key: "enable2FA",
      render: (data) => {
        const isAdmin = data.name === "admin";
        const isCurrentUser = userName === data.name;
        const isAdminViewingOthers = userName === "admin" && !isAdmin;

        if (isAdmin) {
          return <EyeInvisibleOutlined />;
        }

        if (isCurrentUser || isAdminViewingOthers) {
          return (
            <Switch
              size="small"
              checked={data.enable2FA}
              onChange={() => handleChange2FA(data)}
              data-testid={
                isCurrentUser ? `toggle-2fa-${data.name}` : undefined
              }
            />
          );
        }

        return <EyeInvisibleOutlined />;
      },
    }),
    [handleChange2FA, userName]
  );

  return useMemo(
    () =>
      userRole === "admin"
        ? [...baseColumns, actionColumn, twoFactorAuthColumn]
        : [...baseColumns, twoFactorAuthColumn],
    [baseColumns, actionColumn, twoFactorAuthColumn, userRole]
  );
};
