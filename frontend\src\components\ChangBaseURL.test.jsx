import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import ChangBaseURL from './ChangBaseURL';
import { renderWithProviders, userEvent } from '../tests/test-utils';
import { useSettingStore } from '../store/setting-store';

// Mock the setting store
vi.mock('../store/setting-store', () => ({
  useSettingStore: vi.fn(),
}));

describe('ChangBaseURL', () => {
  const mockChangeBaseURL = vi.fn();
  
  beforeEach(() => {
    mockChangeBaseURL.mockClear();
    useSettingStore.mockReturnValue({
      baseURL: 'http://localhost:27182',
      changeBaseURL: mockChangeBaseURL,
    });
  });
  
  it('renders with the current base URL', () => {
    renderWithProviders(<ChangBaseURL />);
    
    // Check if the input has the current base URL
    const input = screen.getByTestId('base-url-input');
    expect(input).toHaveValue('http://localhost:27182');
  });
  
  it('updates the input value when typing', async () => {
    renderWithProviders(<ChangBaseURL />);
    
    // Get the input and type a new URL
    const input = screen.getByTestId('base-url-input');
    await userEvent.clear(input);
    await userEvent.type(input, 'http://new-server:8080');
    
    // Check if the input value was updated
    expect(input).toHaveValue('http://new-server:8080');
  });
  
  it('shows an error when trying to save an empty URL', async () => {
    renderWithProviders(<ChangBaseURL />);
    
    // Clear the input and click save
    const input = screen.getByTestId('base-url-input');
    await userEvent.clear(input);
    
    const saveButton = screen.getByTestId('save-base-url-button');
    await userEvent.click(saveButton);
    
    // Check if the error message is displayed
    const errorMessage = screen.getByTestId('base-url-error');
    expect(errorMessage).toHaveTextContent('Base URL cannot be empty');
    
    // Check that changeBaseURL was not called
    expect(mockChangeBaseURL).not.toHaveBeenCalled();
  });
  
  it('shows an error when trying to save an invalid URL', async () => {
    renderWithProviders(<ChangBaseURL />);
    
    // Type an invalid URL and click save
    const input = screen.getByTestId('base-url-input');
    await userEvent.clear(input);
    await userEvent.type(input, 'invalid-url');
    
    const saveButton = screen.getByTestId('save-base-url-button');
    await userEvent.click(saveButton);
    
    // Check if the error message is displayed
    const errorMessage = screen.getByTestId('base-url-error');
    expect(errorMessage).toHaveTextContent('Please enter a valid URL');
    
    // Check that changeBaseURL was not called
    expect(mockChangeBaseURL).not.toHaveBeenCalled();
  });
  
  it('calls changeBaseURL when saving a valid URL', async () => {
    renderWithProviders(<ChangBaseURL />);
    
    // Type a valid URL and click save
    const input = screen.getByTestId('base-url-input');
    await userEvent.clear(input);
    await userEvent.type(input, 'http://new-server:8080');
    
    const saveButton = screen.getByTestId('save-base-url-button');
    await userEvent.click(saveButton);
    
    // Check that changeBaseURL was called with the new URL
    expect(mockChangeBaseURL).toHaveBeenCalledWith('http://new-server:8080');
    
    // Check that no error message is displayed
    expect(screen.queryByTestId('base-url-error')).not.toBeInTheDocument();
  });
});
