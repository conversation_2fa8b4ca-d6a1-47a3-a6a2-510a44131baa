import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import { renderWithProviders, userEvent } from '../../../tests/test-utils';
import DeviceTable from './DeviceTable';
import { useDeviceFilters } from '../hooks/useDeviceFilters';
import { useContextMenu } from 'react-contexify';
import { useAppStore } from '../../../store/store';
import { useSettingStore } from '../../../store/setting-store';
import { useDeviceColumns } from '../../../components/table-column/device-table';

// Mock the hooks
vi.mock('../hooks/useDeviceFilters', () => ({
  useDeviceFilters: vi.fn(),
}));

vi.mock('react-contexify', () => ({
  useContextMenu: vi.fn(),
}));

vi.mock('../../../store/store', () => ({
  useAppStore: vi.fn(),
}));

vi.mock('../../../store/setting-store', () => ({
  useSettingStore: vi.fn(),
}));

vi.mock('../../../components/table-column/device-table', () => ({
  useDeviceColumns: vi.fn(),
}));

// Mock the components
vi.mock('@ant-design/pro-table', () => ({
  ProTable: ({ columns, dataSource, loading, rowSelection, onRow }) => (
    <div data-testid="pro-table">
      <div>Columns: {columns.length}</div>
      <div>Data: {dataSource.length}</div>
      <div>Loading: {loading.toString()}</div>
      <div>Selected: {rowSelection.selectedRowKeys.length}</div>
      <button 
        data-testid="row-context-menu" 
        onClick={() => onRow({ ipaddress: '***********' }).onContextMenu({ preventDefault: vi.fn() })}
      >
        Trigger Context Menu
      </button>
    </div>
  ),
}));

vi.mock('../../../components/export-data/export-data', () => ({
  default: () => <div data-testid="export-data">Export Data</div>,
}));

describe('DeviceTable', () => {
  const mockColumns = [
    { title: 'IP Address', dataIndex: 'ipaddress' },
    { title: 'Model', dataIndex: 'modelname' },
  ];
  
  const mockDevices = [
    { mac: '00:11:22:33:44:55', ipaddress: '***********', modelname: 'Model A' },
    { mac: '66:77:88:99:AA:BB', ipaddress: '***********', modelname: 'Model B' },
  ];
  
  const mockShowContextMenu = vi.fn();
  const mockHideContextMenu = vi.fn();
  const mockSetSelectedRowKeys = vi.fn();
  const mockOpenDialogs = vi.fn();
  const mockChangeInventoryType = vi.fn();
  const mockRefetch = vi.fn();
  const mockSetInputSearch = vi.fn();
  
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup mock return values
    useDeviceFilters.mockReturnValue({
      filteredData: mockDevices,
      isFetching: false,
      refetch: mockRefetch,
      inputSearch: '',
      setInputSearch: mockSetInputSearch,
    });
    
    useContextMenu.mockReturnValue({
      show: mockShowContextMenu,
      hideAll: mockHideContextMenu,
    });
    
    useAppStore.mockReturnValue({
      selectedRowKeys: [],
      setSelectedRowKeys: mockSetSelectedRowKeys,
      openDialogs: mockOpenDialogs,
    });
    
    useSettingStore.mockReturnValue({
      inventoryType: 'device',
      changeInventoryType: mockChangeInventoryType,
    });
    
    useDeviceColumns.mockReturnValue(mockColumns);
  });
  
  it('renders the device table with correct data', () => {
    renderWithProviders(<DeviceTable />);
    
    // Check if the table is rendered with the correct data
    expect(screen.getByTestId('pro-table')).toBeInTheDocument();
    expect(screen.getByText('Columns: 2')).toBeInTheDocument();
    expect(screen.getByText('Data: 2')).toBeInTheDocument();
    expect(screen.getByText('Loading: false')).toBeInTheDocument();
    expect(screen.getByText('Selected: 0')).toBeInTheDocument();
  });
  
  it('shows context menu when right-clicking a row', async () => {
    renderWithProviders(<DeviceTable />);
    
    // Trigger the context menu
    const contextMenuButton = screen.getByTestId('row-context-menu');
    await userEvent.click(contextMenuButton);
    
    // Check if the context menu show function was called
    expect(mockShowContextMenu).toHaveBeenCalled();
  });
  
  it('shows loading state when fetching data', () => {
    // Update the mock to indicate loading
    useDeviceFilters.mockReturnValue({
      filteredData: [],
      isFetching: true,
      refetch: mockRefetch,
      inputSearch: '',
      setInputSearch: mockSetInputSearch,
    });
    
    renderWithProviders(<DeviceTable />);
    
    // Check if the loading state is displayed
    expect(screen.getByText('Loading: true')).toBeInTheDocument();
  });
});
