package main

import (
	"fmt"
	"os"
	"strings"
)

var Usage = func() {
	fmt.Printf("\n")
	fmt.Fprintf(os.<PERSON>, "Usage of %s:\n", os.Args[0])
	fmt.Printf("\n")
	fmt.Fprintf(os.<PERSON>, "%s\n", HelpCmd())
}

func HelpCmd() string {
	return `
Usage: wgclient [flags] [command] [args], please run as root and see the following commands:

Flags:
	  -config string : wgclient config file

Commands:
	  start : start wireguard with user and pass from mnms config
	  stop : stop wireguard from mnms config
	  clean : stop wireguard and remove wireguard config file

Config:
	  a config file is required, see wgclient.json.example
	  required: 
		root_url : the url of mnms
		root_name : the name of the mnms root
		client_name : the name of the mnms client you want to connect to
	  optional:
	    name : a name for nms to identify this client
		interface : the wireguard interface name
		address : wgclient interface address
		dns : wgclient interface dns, should be an array
		endpoints : wireguard server
		persistent_keepalive : wg network keep alive
		pre_up : command to run before wireguard up
		post_up : command to run after wireguard up
		pre_down : command to run before wireguard down
		post_down : command to run after wireguard down

Examples:
	  wgclient -config wgclient.json start
	  wgclient -config wgclient.json stop
	`
}

func CheckArgs(args []string) {
	if len(args) == 0 {
		Usage()
		os.Exit(1)
	}
	cmd := strings.Join(args, " ")
	if strings.HasPrefix(cmd, "help") {
		fmt.Fprintf(os.Stderr, "%s\n", HelpCmd())
		os.Exit(1)
	}
}
