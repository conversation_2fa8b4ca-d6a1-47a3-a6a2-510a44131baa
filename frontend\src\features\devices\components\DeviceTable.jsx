import React from "react";
import { ProTable } from "@ant-design/pro-table";
import { Button, List, Segmented, Table, theme, Tooltip } from "antd";
import { useContextMenu } from "react-contexify";
import { useDeviceColumns } from "../../../components/table-column/device-table";
import { useSettingStore } from "../../../store/setting-store";
import { useAppStore } from "../../../store/store";
import { useDeviceFilters } from "../hooks/useDeviceFilters";
import ExportData from "../../../components/export-data/export-data";
import { PlusOutlined } from "@ant-design/icons";

const CONTEXT_MENU_TYPES = {
  DEVICE: "device-menu",
  MASS: "mass-menu",
  MDR: "mdr-menu",
};

const DeviceTable = () => {
  const { token } = theme.useToken();
  const { selectedRowKeys, setSelectedRowKeys, openDialogs } = useAppStore();
  const { show, hideAll } = useContextMenu();
  const { changeInventoryType, inventoryType } = useSettingStore();
  const { filteredData, isFetching, refetch, setInputSearch } =
    useDeviceFilters();
  const columns = useDeviceColumns(token);

  // Table configurations
  const tableConfig = {
    expandable: {
      expandedRowRender: (record) => (
        <List
          size="small"
          header={<div>Device Errors</div>}
          bordered
          dataSource={record.device_errors?.slice(0, 5) || []}
          renderItem={(item) => <List.Item>{item}</List.Item>}
        />
      ),
      rowExpandable: (record) => record.device_errors !== null,
    },
    toolbar: {
      search: {
        allowClear: true,
        onSearch: setInputSearch,
        onClear: () => setInputSearch(""),
      },
      actions: [
        <Segmented
          key="inventory-type"
          options={["device", "mdr"]}
          value={inventoryType}
          onChange={changeInventoryType}
        />,
        <ExportData
          Columns={columns}
          DataSource={filteredData}
          title="Inventory_Device_List"
        />,
        <Tooltip title="add device by IP CIDR">
          <Button
            icon={<PlusOutlined />}
            onClick={() => openDialogs({ id: "scanCIDR", data: null })}
          />
        </Tooltip>,
      ],
    },
  };

  return (
    <ProTable
      columns={columns}
      bordered={false}
      dataSource={filteredData}
      rowKey="mac"
      defaultSize="small"
      loading={isFetching}
      options={{ reload: refetch, fullScreen: false }}
      search={false}
      toolbar={tableConfig.toolbar}
      expandable={tableConfig.expandable}
      scroll={{ x: 1100 }}
      pagination={{
        position: ["bottomCenter"],
        showQuickJumper: true,
        size: "default",
        total: filteredData.length,
        defaultPageSize: 10,
        pageSizeOptions: [10, 15, 20, 25],
        showTotal: (total, range) =>
          `${range[0]}-${range[1]} of ${total} items`,
      }}
      rowSelection={{
        selectedRowKeys,
        onChange: (keys) => {
          hideAll();
          setSelectedRowKeys(keys);
        },
        selections: [Table.SELECTION_ALL, Table.SELECTION_NONE],
      }}
      cardProps={{ style: { boxShadow: token?.Card?.boxShadow } }}
      dateFormatter="string"
      columnsState={{
        persistenceKey: "nms-device-table",
        persistenceType: "localStorage",
      }}
      onRow={(record) => ({
        onContextMenu: (event) => {
          if (!record) return;
          const menuId =
            inventoryType === "device"
              ? selectedRowKeys.length === 0
                ? CONTEXT_MENU_TYPES.DEVICE
                : CONTEXT_MENU_TYPES.MASS
              : CONTEXT_MENU_TYPES.MDR;
          show({ id: menuId, event, props: { record, selectedRowKeys } });
        },
      })}
    />
  );
};

export default DeviceTable;
