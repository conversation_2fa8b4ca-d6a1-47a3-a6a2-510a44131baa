import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";
import { persist } from "zustand/middleware";

/**
 * Initial state for the Topology store
 * @type {Object}
 */
const initialState = {
  // Selected network service filter
  selectedServices: "All Network Services",

  // Node positions by service type
  nodePositions: {},
};

/**
 * Storage key for persisting node positions in localStorage
 * @type {string}
 */
const TOPOLOGY_POSITIONS_KEY = "nimbl-topologyPositions";

/**
 * Load saved node positions from localStorage
 * @returns {Object} Saved node positions or empty object
 */
const loadSavedPositions = () => {
  try {
    const savedPositions = localStorage.getItem(TOPOLOGY_POSITIONS_KEY);
    return savedPositions ? JSON.parse(savedPositions) : {};
  } catch (error) {
    console.error("Error loading saved topology positions:", error);
    return {};
  }
};

/**
 * Topology store for managing network topology visualization state
 * @type {import("zustand").UseBoundStore}
 */
export const useTopologyStore = create(
  devtools(
    immer((set, get) => ({
      // Initial state with loaded positions
      ...initialState,
      nodePositions: loadSavedPositions(),

      /**
       * Set selected service filter
       * @param {string} service - Service type to filter by
       */
      setSelectedServices: (service) =>
        set((state) => {
          state.selectedServices = service;
        }),

      /**
       * Save node positions for the current service
       * @param {Array} nodes - Array of nodes with positions
       */
      saveNodePositions: (nodes) =>
        set((state) => {
          // Update state
          state.nodePositions = {
            ...state.nodePositions,
            [state.selectedServices]: nodes,
          };

          // Also save to localStorage for persistence
          localStorage.setItem(
            TOPOLOGY_POSITIONS_KEY,
            JSON.stringify({
              ...state.nodePositions,
              [state.selectedServices]: nodes,
            })
          );
        }),

      /**
       * Get node positions for the current service
       * @returns {Array|null} Nodes with positions or null
       */
      getNodePositions: () => {
        const state = get();
        return state.nodePositions[state.selectedServices] || null;
      },

      /**
       * Clear all saved positions
       */
      clearPositions: () =>
        set((state) => {
          state.nodePositions = {};
          localStorage.removeItem(TOPOLOGY_POSITIONS_KEY);
        }),
    }))
  ),
  {
    name: "topology-store",
    enabled: process.env.NODE_ENV === "development",
  }
);
