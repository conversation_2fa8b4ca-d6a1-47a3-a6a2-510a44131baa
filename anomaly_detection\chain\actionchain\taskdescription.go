package actionchain

/*
This file contains documents for LLM. Each task can be done by a set of actions.
Action is a concept that can be executed by <PERSON><PERSON>. It can be a command or an API call.

A action format: The action's format is similar with YAML list.
 - Command : { real command, replace arguments with placeholders }
   - description: description is optional, well description will help user understand what the task doing.
	 - Replace {argument} with the real value and other instructions.

A API action format:
 - API:
   - description: description is optional, well description will help user understand what the task doing.
	 - endpoint: API endpoint
	 - method: HTTP method
	 - query-params: query parameters
	 - body: body of the request
*/

// key use to make embedding, value is a relvant document
var supportedTasks = []string{
	// Task configure device's network settings
	`Task: Configure device's network settings including IP address, subnet mask, default gateway, hostname, and DHCP setting 
Task Type: device-config

Actions: 
 
- Command: config network set {mac} {ipaddress} {NEW IP} {netmask} {gateway} {hostname} {isdhcp} 
  - description: Set the device's network settings
  - Replace {mac} with the MAC address of the device 
  - Replace {ipaddress} with the current IP address of the device, usually in the more input.
  - Replace {NEW IP} with the new IP address user wants to set, if missing, use {ipaddress} instead
  - Replace {netmask} with the subnet mask 
  - Replace {gateway} with the default gateway 
  - Replace {hostname} with the hostname 
  - Replace {isdhcp} enable or disable DHCP  (0 for disabled, 1 for enabled)

- Command: agent devinfo send {mac}
  - description: Update device's information to the root
  - Replace {mac} with the MAC address of the device 

Validation :
- description: check all settings were set correctlly
- action:
  - type: API 
	- description: Get last device's information
  - endpoint: /api/vi/devices
  - method: GET
  - query-params: dev={mac}
- expects:
    - ipaddress:{NEW IP}
    - netmask: {netmask}
		- gateway: {gateway}
    - hostname: {hostname}
    - isdhcp: {isdhcp}
		  - Replace {isdhcp} with 0 for disabled, 1 for enabled
Necessary Inputs:
{mac},{ipaddress},{NEW IP},{netmask},{gateway},{hostname},{isdhcp}`,
	// Task enable or disable SNMP function on the device
	`Task: Enable or disable SNMP function on the device
Task Type: device-config
Actions:
- Command: agent snmp enable {mac} {0 or 1}
	- description: Enable or disable SNMP function on the device
	- Replace {mac} with the MAC address of the device
	- Replace {0 or 1} with 0 to disable SNMP or 1 to enable SNMP
- Command: agent devinfo send {mac}
	- description: Update device's information to the root
	- Replace {mac} with the MAC address of the device

Validation:
- description: check all settings were set correctlly
- action:
  - type: API 
	- description: Get last device's information
  - endpoint: /api/vi/devices
  - method: GET
  - query-params: dev={mac}
- expects:
    - snmpEnabled:{0 or 1}
		- Replace {0 or 1} with 0 to disable SNMP or 1 to enable SNMP
Necessary Inputs:
{mac}

`,
	// Enable or disable GPS function on the device
	`Task: Enable or disable GPS function on the device
Task Type: device-config
Actions:
- Command: agent config gps {mac} {0 or 1}
	- description: Enable or disable GPS function on the device
	- Replace {mac} with the MAC address of the device
	- Replace {0 or 1} with 0 to disable GPS or 1 to enable GPS
- Command: agent devinfo send {mac}
	- description: Update device's information to the root
	- Replace {mac} with the MAC address of the device

Validation:
- description: check device's GPS settings was set correctlly
- action:
	- type: API
	- description: Get last device's information
	- endpoint: /api/vi/devices
	- method: GET
	- query-params: dev={mac}
- expects:
	- gpsInfo: { "enabled": {0 or 1} }
	- The value of "enabled" is what user set in the action

`,
}

// input sample
/*
{
 "old-ip": "***********",
 "new-ip": "***********",
 "mask": "*************",
 "gateway": "*************",
 "hostname": "test-switch",
 "dhcp": 1,
 "mac": "00-12-34-56-78-9a"
}
*/
