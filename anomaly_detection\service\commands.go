package service

// commands.go provides anomaly detection related commands' handler

import (
	"bufio"
	"bytes"
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"path"
	"regexp"
	"strconv"
	"strings"

	"mnms/anomaly_detection/loganalyse"
	"mnms/anomaly_detection/store/anomstore"
	"mnms/llm"
	m "mnms/llm"

	"mnms/anomaly_detection/report"
	"mnms/anomaly_detection/retriever"
	"mnms/anomaly_detection/statistic"
	"mnms/anomaly_detection/utils"
	"net/url"
	"os"

	"github.com/qeof/q"
	"github.com/vmihailenco/msgpack/v5"
)

func failWithError(err error) (CommandResult, error) {
	return CommandResult{
		Status: "fail",
		Result: err.Error(),
	}, err
}

// GetLinesFromNIMBLFileServer get file with link
func GetLinesFromNIMBLFileServer(link string) ([]string, error) {
	// token,err := mnms.GetToken("admin")
	token := statistic.GetToken()
	bearer := "Bearer " + token
	req, err := http.NewRequest("GET", link, nil)
	if err != nil {
		q.Q(err)
		return nil, err
	}
	req.Header.Add("Authorization", bearer)
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		q.Q(err)
		return nil, err
	}

	defer resp.Body.Close()
	var logs []string
	// read body line by line to logs
	scanner := bufio.NewScanner(resp.Body)
	for scanner.Scan() {
		logs = append(logs, scanner.Text())
	}

	return logs, nil
}

// ConfigurateOpeanAICmd config openai
func ConfigurateOpeanAICmd(cmd []string) error {
	// anomaly config openai [what] [...]
	q.Q("run config openai command: ", cmd)
	if len(cmd) < 4 {
		return fmt.Errorf("invalid config openai command")
	}
	switch cmd[3] {
	case "apikey":
		q.Q("config openai apikey: ", cmd[4])
		if len(cmd) < 5 {
			return fmt.Errorf("invalid config openai apikey [key], missing key")
		}
		statistic.LocalStatistic.Settings.OpenAISettings.APIKey = cmd[4]
	default:
		return fmt.Errorf("unknown config openai command with %s", cmd[3])
	}
	return nil
}

// parseDuration takes a string input and returns the corresponding time.Duration
func parseDuration(input string) (int, error) {
	// Regular expression to match the duration string
	re := regexp.MustCompile(`(?i)^(\d+)(m|mins?|h|hours?)$`)
	matches := re.FindStringSubmatch(input)

	if len(matches) != 3 {
		return 0, fmt.Errorf("invalid duration format")
	}

	// Extract the value and unit from the matched groups
	value, err := strconv.Atoi(matches[1])
	if err != nil {
		return 0, fmt.Errorf("invalid number in duration")
	}

	unit := strings.ToLower(matches[2])

	// Convert the value to the corresponding time.Duration based on the unit
	switch unit {
	case "m", "min", "mins":
		return value, nil
	case "h", "hour", "hours":
		return value * 60, nil
	default:
		return 0, fmt.Errorf("invalid duration unit")
	}
}

// ConfigurateLLMCmd config llm
// anomaly config llm open-ai {api-key}
// anomaly config llm ollama {host} {port} {model}
// anomaly config llm connect {open-ai|ollama}
func ConfigurateLLMCmd(cmd []string) error {
	q.Q("run config llm command: ", cmd)
	if len(cmd) < 5 {
		return fmt.Errorf("invalid config llm command")
	}
	switch cmd[3] {
	case "open-ai":
		statistic.LocalStatistic.Settings.OpenAISettings.APIKey = cmd[4]
	case "ollama":
		if len(cmd) < 7 {
			return fmt.Errorf("invalid config llm ollama command, missing host, port, model")
		}
		statistic.LocalStatistic.Settings.OllamaSettings.Host = cmd[4]
		port, err := strconv.Atoi(cmd[5])
		if err != nil {
			return err
		}
		statistic.LocalStatistic.Settings.OllamaSettings.Port = port
		statistic.LocalStatistic.Settings.OllamaSettings.Model = cmd[6]
	case "connect":
		if cmd[4] != "open-ai" && cmd[4] != "ollama" {
			return fmt.Errorf("invalid config llm connect command, unknown model %s", cmd[4])
		}

		// validate
		previouModel := statistic.LocalStatistic.Settings.Model
		if statistic.LocalStatistic.Settings.Model != cmd[4] {
			statistic.LocalStatistic.Settings.Model = cmd[4]
			llmSettings, err := statistic.GetLargeLanguageModelFromAnomalyStatistics()
			if err != nil {
				statistic.LocalStatistic.Settings.Model = previouModel
				return err
			}

			llmServer, err := llm.NewLLMClient(llmSettings)
			if err != nil {
				statistic.LocalStatistic.Settings.Model = previouModel
				return err
			}
			err = m.ValidateLLMConnection(llmServer)
			if err != nil {
				statistic.LocalStatistic.Settings.Model = previouModel
				return err
			}
			// clear RAG
			anomstore.LocalVectors.Clear()
		}

	default:
		return fmt.Errorf("unknown config llm command with %s", cmd[3])
	}
	return nil
}

// ConfigurateRealTimeDetection config real time detection
// anomaly config realtime enable
// anomaly config realtime disable
// anomaly config realtime buffer-size 10
func ConfigurateRealTimeDetection(cmd []string) error {
	// anomaly config realtime [what] [...]
	q.Q("run config realtime command: ", cmd)
	if len(cmd) < 4 {
		return fmt.Errorf("invalid config realtime command")
	}
	switch cmd[3] {
	case "enable":
		q.Q("config realtime enable")
		statistic.LocalStatistic.Settings.RealTimeDetection.Enabled = true
		statistic.LocalStatistic.Settings.RealTimeDetection.Changed <- struct{}{}
	case "disable":
		q.Q("config realtime disable")
		statistic.LocalStatistic.Settings.RealTimeDetection.Enabled = false
		statistic.LocalStatistic.Settings.RealTimeDetection.Changed <- struct{}{}

	case "buffer-size":
		q.Q("config realtime buffer-size: ", cmd[4])
		if len(cmd) < 5 {
			return fmt.Errorf("invalid config realtime buffer-size [number], missing number")
		}
		messagesPerMin, err := strconv.Atoi(cmd[4])
		if err != nil {
			return err
		}
		statistic.LocalStatistic.Settings.RealTimeDetection.BufferSize = messagesPerMin
		statistic.LocalStatistic.Settings.RealTimeDetection.Changed <- struct{}{}

	default:
		return fmt.Errorf("unknown config realtime command with %s", cmd[3])
	}
	return nil
}

// ConfigurateDetectBehaviour
// anomaly config detect enable
// anomaly config detect disable
// anomaly config detect interval 5h
// anomaly config detect distance 5m (5 minutes)
// anomaly config detetct logsvc {clientname}
func ConfigurateDetectBehaviour(cmd []string) error {
	// anomaly config detect [what] [...]
	q.Q("run config detect command: ", cmd)
	if len(cmd) < 4 {
		return fmt.Errorf("invalid config detect command")
	}
	switch cmd[3] {
	case "enable":
		q.Q("config detect enable")
		statistic.LocalStatistic.Settings.Detect = true
	case "disable":
		q.Q("config detect disable")
		statistic.LocalStatistic.Settings.Detect = false
	case "interval":
		q.Q("config detect interval: ", cmd[4])
		if len(cmd) < 5 {
			return fmt.Errorf("invalid config detect interval [time], missing time")
		}
		duration, err := parseDuration(cmd[4])
		if err != nil {
			return err
		}
		statistic.LocalStatistic.Settings.PullIntervalMins = duration
		// check duration 5h, 5m, 5mins, 5hours etc
	case "distance":
		q.Q("config detect distance: ", cmd[4])
		if len(cmd) < 5 {
			return fmt.Errorf("invalid config detect distance [distance], missing distance")
		}
		distance, err := strconv.ParseFloat(cmd[4], 32)
		if err != nil {
			return err
		}
		statistic.LocalStatistic.Settings.Score = float32(distance)
	case "logsvc":
		q.Q("config detect logsvc: ", cmd[4])
		if len(cmd) < 5 {
			return fmt.Errorf("invalid config detect logsvc [clientname], missing clientname")
		}
		statistic.LocalStatistic.Settings.LogSvc = cmd[4]

	default:
		return fmt.Errorf("unknown config detect command with %s", cmd[3])
	}
	return nil
}

// ConfigurateCmd config anomaly detection
// anomaly config model
func ConfigurateCmd(cmd []string) error {
	// anomaly config [category] [...]
	q.Q("run config command: ", cmd)
	if len(cmd) < 3 {
		return fmt.Errorf("invalid config command")
	}
	switch cmd[2] {
	case "openai":
		return ConfigurateOpeanAICmd(cmd)
	case "detect":
		return ConfigurateDetectBehaviour(cmd)
	case "llm":
		return ConfigurateLLMCmd(cmd)
	case "realtime":
		return ConfigurateRealTimeDetection(cmd)

	default:
		return fmt.Errorf("unknown config command with %s", cmd[2])
	}

}

func readFileOnURL(targetURL string) (*bytes.Buffer, error) {
	// Get file from targetURL
	parsedURL, err := url.ParseRequestURI(targetURL)
	if err != nil {
		return nil, fmt.Errorf("read file from %s fail: %s", targetURL, err)
	}
	var buffer bytes.Buffer

	if parsedURL.Scheme == "file" {
		// file scheme
		// get file from urlstring
		path := parsedURL.Path
		if os.PathSeparator == '\\' && len(path) > 0 && path[0] == '/' {
			path = path[1:]
		}
		// read file and put into logs
		file, err := os.Open(path)
		if err != nil {
			return nil, fmt.Errorf("read file from %s fail: %s", targetURL, err)
		}
		defer file.Close()
		_, err = io.Copy(&buffer, file)
		if err != nil {
			return nil, fmt.Errorf("read file from %s fail: %s", targetURL, err)
		}

	} else if parsedURL.Scheme == "http" || parsedURL.Scheme == "https" {
		// token,err := mnms.GetToken("admin")
		token := statistic.GetToken()
		bearer := "Bearer " + token
		req, err := http.NewRequest("GET", targetURL, nil)
		if err != nil {
			q.Q(err)
			return nil, err
		}
		req.Header.Add("Authorization", bearer)
		req.Header.Set("Content-Type", "application/json")
		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			q.Q(err)
			return nil, err
		}

		defer resp.Body.Close()
		_, err = io.Copy(&buffer, resp.Body)

	}
	return &buffer, nil

}

// AnalyseURL
// command: To analyse url with score
func AnalyseURL(urlstring string, score float32) *report.Report {

	clientName := statistic.GetClientName()

	parsedURL, err := url.ParseRequestURI(urlstring)
	if err != nil {
		return report.NewErrorReport(clientName, urlstring, err)
	}
	var logs []string

	if parsedURL.Scheme == "file" {
		// file scheme
		// get file from urlstring
		path := parsedURL.Path
		if os.PathSeparator == '\\' && len(path) > 0 && path[0] == '/' {
			path = path[1:]
		}
		// read file and put into logs
		file, err := os.Open(path)
		if err != nil {
			return report.NewErrorReport(clientName, urlstring, err)
		}
		defer file.Close()
		scanner := bufio.NewScanner(file)
		for scanner.Scan() {
			logs = append(logs, scanner.Text())
		}

	} else if parsedURL.Scheme == "http" || parsedURL.Scheme == "https" {
		// get file from NIMBL /files
		q.Q("get file from NIMBL server: ", urlstring)
		logs, err = GetLinesFromNIMBLFileServer(urlstring)
		if err != nil {
			return report.NewErrorReport(clientName, urlstring, err)
		}
	}

	if len(logs) == 0 {
		return report.NewErrorReport(clientName, urlstring, fmt.Errorf("empty file"))
	}

	// create report
	anReport, err := loganalyse.GenerateAnalyseReport(logs, urlstring, statistic.GetClientName(), score)
	if err != nil {
		return report.NewErrorReport(clientName, urlstring, err)
	}

	return anReport
}

// StateCmd get state
func StateCmd() ([]byte, error) {
	type apikeyState struct {
		IsDemoAPIKey bool `json:"isDemoAPIKey"`
		IsWork       bool `json:"isWork"`
	}
	var state apikeyState
	state.IsDemoAPIKey = utils.IsDemoAPIKey()
	state.IsWork = IsOpenAIKeyWork()
	jsonByte, err := json.Marshal(state)
	if err != nil {
		return nil, err
	}
	return jsonByte, nil
}

func QueryRelevantDocuments(input string, topK int) ([]string, error) {
	q.Q("query relevant documents  topK: ", topK)
	llmSettings, err := statistic.GetLargeLanguageModelFromAnomalyStatistics()
	if err != nil {
		return nil, err
	}
	mod, err := llm.NewLLMClient(llmSettings)
	if err != nil {
		return nil, err
	}
	ret := retriever.NewRetriever(&anomstore.LocalVectors, mod)
	docs, err := ret.GetReleventDocuments(context.Background(), input, topK)
	if err != nil {
		return nil, err
	}
	var result []string
	for _, doc := range docs {
		result = append(result, fmt.Sprintf("%s: %f", doc.Content, doc.Score))
	}
	return result, nil

}

// AnomalyDetectCmd
// anomaly detect {url} {score}
// anomaly detect {msg}
func AnomalyDetectCmd(cmd []string) (string, error) {
	// anomaly detect {url} {score}
	// anomaly detect {msg}
	q.Q("run detect command: ", cmd)
	if len(cmd) < 3 {
		return "", fmt.Errorf("invalid detect command, missing url")
	}

	// check if it is a url
	isUrl := false
	if strings.HasPrefix(cmd[2], "http") || strings.HasPrefix(cmd[2], "file") || strings.HasPrefix(cmd[2], "ftp") || strings.HasPrefix(cmd[2], "https") {
		isUrl = true
	}

	if !isUrl {
		q.Q("detect msg: ", cmd)
		// it is a message
		llmSettings, err := statistic.GetLargeLanguageModelFromAnomalyStatistics()
		if err != nil {
			return "", err
		}
		mod, err := llm.NewLLMClient(llmSettings)
		if err != nil {
			return "", err
		}
		var score = statistic.LocalStatistic.Settings.Score
		input := strings.Join(cmd[2:], " ")
		result, err := loganalyse.LogAnomalyDetect(mod, input, float32(score))
		if err != nil {
			return "", err
		}
		normal := "normal"
		if !result.Normal {
			normal = "anomaly"
		}
		msg := fmt.Sprintf("%s is %s, : %s", input, normal, result.Reason)
		return msg, nil

	} else {
		q.Q("detect url: ", cmd)
		return AnalyseURLCmd(cmd)

	}

}

// AnalyseURLCmd
// anomaly detect {url} {score}
// analyse url with score
// example:
// anomaly detect http://nimbl.io/files/2021/09/20210914T150000.log 0.4
// anomaly detect file:///tmp/20210914T150000.log 0.4
func AnalyseURLCmd(cmd []string) (string, error) {
	// anamaly analyse {url} {score}
	q.Q("run analyse command: ", cmd)
	if len(cmd) < 3 {
		return "", fmt.Errorf("invalid detect command, missing url")
	}

	// validate url
	var err error

	var score float64
	if len(cmd) >= 4 {
		// convert cmd[3] to float
		score, err = strconv.ParseFloat(cmd[3], 32)
		if err != nil {
			return "", err
		}
	} else {
		score = float64(statistic.LocalStatistic.Settings.Score)
	}
	q.Q("score: ", score)

	urlstring := cmd[2]

	analyseReport := AnalyseURL(urlstring, float32(score))

	q.Q("analyse report: ", analyseReport)

	err = UploadAnomalyReport(strings.Join(cmd, " "), analyseReport)
	if err != nil {
		q.Q("upload report fail: ", err)
	}
	anomalyCount := analyseReport.TotalAnomaly
	errorCount := analyseReport.TotalError

	anomalyPercent := float64((anomalyCount + errorCount)) / float64(analyseReport.TotalCount) * 100
	msg := fmt.Sprintf("detect %s , %d logs, %f%% anomalies", analyseReport.Source, analyseReport.TotalCount, anomalyPercent)

	return msg, err
}

type CommandResult struct {
	Status string `json:"status"`
	Result string `json:"result"`
}

// GetRAG get rag data
// anomaly rag get count
func GetRAG(filter string) (string, error) {

	vec, err := anomstore.SerializeToCSV(&anomstore.LocalVectors, filter)
	if err != nil {
		return "", err
	}
	result, err := io.ReadAll(vec)
	if err != nil {
		return "", err
	}
	return string(result), nil
}

// writeToURL write data to url
func writeToURL(targetURL string, filename string, data []byte) error {
	parsedURL, err := url.ParseRequestURI(targetURL)
	if err != nil {
		return fmt.Errorf("read file from %s fail: %s", targetURL, err)

	}
	if parsedURL.Scheme == "file" {
		// write to local machine
		filepath := parsedURL.Path
		if os.PathSeparator == '\\' && len(filepath) > 0 && filepath[0] == '/' {
			filepath = filepath[1:]
		}
		// write file and put into logs
		filepath = path.Join(filepath, filename)
		file, err := os.Create(filepath)
		if err != nil {
			return fmt.Errorf("write file to %s fail: %s", targetURL, err)
		}
		defer file.Close()
		_, err = file.Write(data)
		if err != nil {
			return fmt.Errorf("write file to %s fail: %s", targetURL, err)
		}
		return nil
	}
	var requestBody bytes.Buffer
	multiPartWriter := multipart.NewWriter(&requestBody)

	fileWriter, err := multiPartWriter.CreateFormFile("file", filename)
	if err != nil {
		return err
	}
	q.Q("export rag: ", filename)

	// Copy
	_, err = io.Copy(fileWriter, bytes.NewReader(data))

	multiPartWriter.Close()
	request, err := http.NewRequest("POST", targetURL, &requestBody)
	if err != nil {
		return err
	}

	request.Header.Set("Content-Type", multiPartWriter.FormDataContentType())
	client := &http.Client{}
	resp, err := client.Do(request)
	if err != nil {
		return err
	}

	defer resp.Body.Close()
	if resp.StatusCode != 200 {
		return fmt.Errorf("upload rag file fail: %d", resp.StatusCode)
	}
	bodytext, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	q.Q("upload rag file done: ", string(bodytext))
	return nil
}

// UploadRAGFile
// upload rag file to server
func UploadRAGFile(targetURL string, filename string) (string, error) {
	if filename == "" {
		return "", fmt.Errorf("upload rag file fail: filename is empty")
	}

	vec, err := anomstore.SerializeToCSV(&anomstore.LocalVectors, "")
	if err != nil {
		return "", err
	}
	var data []byte
	data, err = io.ReadAll(vec)
	if err != nil {
		return "", err
	}
	err = writeToURL(targetURL, filename, data)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("Upload %s to %s", filename, targetURL), nil
}

// SettingsCmd settings command
func SettingsCmd(cmd []string) error {
	// anomaly settings refresh
	q.Q("run settings command: ", cmd)
	if len(cmd) < 3 {
		return fmt.Errorf("invalid settings command")
	}
	switch cmd[2] {
	case "refresh":
		return RefreshSettings()
	default:
		return fmt.Errorf("unknown settings command with %s", cmd[2])
	}

}

// RefreshSettings refresh settings
func RefreshSettings() error {
	endpoing := "/api/v1/anomaly/settings"
	rooturlText := statistic.LocalStatistic.Settings.Root + endpoing
	rooturl, err := url.Parse(rooturlText)
	if err != nil {
		return err
	}

	qry := rooturl.Query()
	qry.Set("client", statistic.LocalStatistic.ClientName)
	rooturl.RawQuery = qry.Encode()
	q.Q("refresh settings: ", rooturl)
	req, err := http.NewRequest("GET", rooturl.String(), nil)
	if err != nil {
		return err
	}
	token := statistic.LocalStatistic.Settings.Token
	bearer := "Bearer " + token
	req.Header.Add("Authorization", bearer)
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	if resp.StatusCode != 200 {
		return fmt.Errorf("refresh settings fail: %d", resp.StatusCode)
	}

	var settings statistic.Settings
	err = json.NewDecoder(resp.Body).Decode(&settings)
	if err != nil {
		return err
	}
	statistic.StatisticMutex.Lock()
	statistic.LocalStatistic.Settings = settings
	statistic.StatisticMutex.Unlock()

	// upload statistic to server
	err = UploadAnomalyStatistic()
	if err != nil {
		return err
	}
	return nil

}

// readfile read file from url

// ImportRAG import rag file
func ImportRAG(targetURL string) (string, error) {
	buf, err := readFileOnURL(targetURL)
	if err != nil {
		return "", err
	}
	r := csv.NewReader(buf)
	records, err := r.ReadAll()
	if err != nil {
		return "", fmt.Errorf("Failed to read all records: %v", err)
	}
	llmSettings, err := statistic.GetLargeLanguageModelFromAnomalyStatistics()
	if err != nil {
		return "", err
	}
	mod, err := llm.NewLLMClient(llmSettings)
	if err != nil {
		return "", err
	}
	retriever := retriever.NewRetriever(&anomstore.LocalVectors, mod)
	ctx := context.Background()
	for i, record := range records {
		if i == 0 {
			// drop head
			continue
		}
		normal := false
		if record[2] == "true" {
			normal = true
		}
		replacedLog := loganalyse.NERClassify(record[1])
		_, err = retriever.Add(ctx, replacedLog, map[string]any{
			"normal": normal,
			"reason": record[3],
		})
		if err != nil {
			return "", fmt.Errorf("Import RAG fail: %s", err)
		}

	}
	// anomstore.LocalVectors, err = anomstore.WriteCSVToVectorList(buf)
	// if err != nil {
	// 	return "", fmt.Errorf("Import RAG fail: %s", err)
	// }
	result := fmt.Sprintf("Import %d vectors from %s", len(anomstore.LocalVectors.Items), targetURL)
	return result, nil
}

// RagCmd rag command
func RagCmd(cmd []string) (string, error) {
	// anomaly rag export [target] [filename]
	// anomaly rag import [target]
	// anomaly rag update [id] [normal:true|false] [reason]
	// anomaly rag get [filter| regex to match raw log ]
	q.Q("run rag command: ", cmd)
	if len(cmd) < 4 {
		return "", fmt.Errorf("invalid anomaly rag command")
	}

	switch cmd[2] {

	case "export":
		target := cmd[3]
		// check target is valid url
		_, err := url.ParseRequestURI(target)
		if err != nil {
			return "", err
		}
		if len(cmd) < 5 {
			return "", fmt.Errorf("invalid rag export command, missing filename")
		}
		filename := cmd[4]
		return UploadRAGFile(target, filename)

	case "import":
		target := cmd[3]
		// check target is valid url
		_, err := url.ParseRequestURI(target)
		if err != nil {
			return "", err
		}
		return ImportRAG(target)

	case "get":
		// get rag
		filter := cmd[3]
		return GetRAG(filter)

	case "update":
		if len(cmd) < 6 {
			return "", fmt.Errorf("invalid rag update command, command should be: anomaly rag update [log] [normal:true|false] [reason]")
		}
		normal := false
		if cmd[4] == "true" {
			normal = true
		}
		var id int64
		id, err := strconv.ParseInt(cmd[3], 10, 64)
		if err != nil {
			return "", err
		}
		reason := strings.Join(cmd[5:], " ")
		return "RAG updated", anomstore.LocalVectors.SetValue(id, normal, reason)

	default:
		return "", fmt.Errorf("unknown rag command with %s", cmd[2])
	}
}

// KnowledgeCmd knowledge command
func KnowledgeCmd(cmd []string) (string, error) {
	// anomaly knowledge import [file]
	// anomaly knowledge export [file] [filename]

	if len(cmd) < 4 {
		return "", fmt.Errorf("invalid anomaly knowledge command")
	}
	operaion := cmd[2]
	switch operaion {
	case "restore":
		// anomaly knowledge import [URL]
		if len(cmd) < 4 {
			return "", fmt.Errorf("invalid anomaly knowledge import command, missing URL")
		}
		// read file from url
		buf, err := readFileOnURL(cmd[3])
		if err != nil {
			return "", err
		}
		err = msgpack.Unmarshal(buf.Bytes(), &anomstore.LocalVectors)
		if err != nil {
			return "", err
		}

		return fmt.Sprintf("Import %d vectors", len(anomstore.LocalVectors.Items)), nil
	case "backup":
		if len(cmd) < 5 {
			return "", fmt.Errorf("invalid anomaly knowledge export command, missing filename")
		}
		b, err := msgpack.Marshal(anomstore.LocalVectors)
		if err != nil {
			return "", err
		}
		// write to file
		err = writeToURL(cmd[3], cmd[4], b)
		if err != nil {
			return "", err
		}
		return fmt.Sprintf("Export %d vectors to %s", len(anomstore.LocalVectors.Items), cmd[3]), nil

	default:
		return "", fmt.Errorf("unknown knowledge command with %s", cmd[2])
	}

}

// RealtimeCmd realtime command
func RealtimeCmd(cmd []string) (string, error) {
	if len(cmd) < 3 {
		return "", fmt.Errorf("invalid anomaly realtime command")
	}
	operation := cmd[2]
	switch operation {
	case "clear":
		realtimeReport := report.GetRealtimeReport()
		realtimeReport.Reset()
		return "clear realtime report", nil
	case "update":
		err := UploadRealtimeReport()
		if err != nil {
			return "", err
		}
		return "upload realtime report", nil
	default:
		return "", fmt.Errorf("unknown realtime command with %s", operation)
	}

}

// MessageCmd syslog message command
func MessageCmd(cmd []string, result string) (string, error) {
	q.Q("run message command: ", cmd, result)
	// anomaly anomalies import [file]
	// anomaly normals import [file]

	t := cmd[1]
	act := cmd[2]

	normal := false
	switch t {
	case "anomalies":
		normal = false
	case "normals":
		normal = true
	default:
		return "", fmt.Errorf("unknown anomaly %s %s", t, act)
	}
	switch act {
	case "import":
		if len(cmd) < 4 {
			return "", fmt.Errorf("invalid anomaly anomalies|normals command")
		}
		return ImportSyslogMsgsCmd(cmd[3], normal)
	case "add":
		if len(cmd) < 3 {
			return "", fmt.Errorf("invalid anomaly anomalies|normals command")
		}
		return AddSyslogMsgsCmd(result, normal)
	default:
		return "", fmt.Errorf("unknown anomaly %s %s", t, act)
	}

}

// AddSyslogMsgsCmd add syslog messages
func AddSyslogMsgsCmd(msgsJSON string, normal bool) (string, error) {
	q.Q("run add syslog messages command: ", msgsJSON, normal)
	var msgs []string
	err := json.Unmarshal([]byte(msgsJSON), &msgs)
	if err != nil {
		return "", err
	}
	llmSettings, err := statistic.GetLargeLanguageModelFromAnomalyStatistics()
	if err != nil {
		return "", err
	}
	mod, err := llm.NewLLMClient(llmSettings)
	if err != nil {
		return "", err
	}
	retriever := retriever.NewRetriever(&anomstore.LocalVectors, mod)
	ctx := context.Background()
	for _, msg := range msgs {
		replacedLog := loganalyse.NERClassify(msg)
		_, err = retriever.Add(ctx, replacedLog, map[string]any{
			"normal": normal,
			"reason": "user defined",
		})
		if err != nil {
			return "", err
		}
	}
	return fmt.Sprintf("Add %d messages", len(msgs)), nil
}

// ImportSyslogMsgsCmd import syslog messages from file
func ImportSyslogMsgsCmd(fileUrl string, normal bool) (string, error) {
	buf, err := readFileOnURL(fileUrl)
	if err != nil {
		return "", err
	}
	var result string
	scanner := bufio.NewScanner(buf)
	llmSettings, err := statistic.GetLargeLanguageModelFromAnomalyStatistics()
	if err != nil {
		return "", err
	}
	mod, err := llm.NewLLMClient(llmSettings)
	if err != nil {
		return "", err
	}
	retriever := retriever.NewRetriever(&anomstore.LocalVectors, mod)
	ctx := context.Background()

	for scanner.Scan() {
		line := scanner.Text()
		if len(line) == 0 {
			//skip empty line
			continue
		}
		replacedLog := loganalyse.NERClassify(line)
		_, err = retriever.Add(ctx, replacedLog, map[string]any{
			"normal": normal,
			"reason": "user defined",
		})
	}

	return result, nil
}
