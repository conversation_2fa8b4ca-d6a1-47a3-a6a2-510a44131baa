import "@testing-library/jest-dom";
import { afterEach, vi } from "vitest";
import { cleanup } from "@testing-library/react";

// Mock the setting store
vi.mock("../store/setting-store", () => ({
  useSettingStore: vi.fn().mockReturnValue({
    mode: "dark",
    colorPrimary: "#13c2c2",
    baseURL: "http://localhost:27182",
    wsURL: "ws://localhost:27182",
    inventoryType: "device",
    changeMode: vi.fn(),
    changePrimaryColor: vi.fn(),
    changeBaseURL: vi.fn(),
    changeWsURL: vi.fn(),
    changeInventoryType: vi.fn(),
  }),
}));

// Automatically cleanup after each test
afterEach(() => {
  cleanup();
});

// Mock matchMedia for components that use media queries
Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Suppress console errors during tests
const originalConsoleError = console.error;
console.error = (...args) => {
  if (
    typeof args[0] === "string" &&
    (args[0].includes("Warning: ReactDOM.render is no longer supported") ||
      args[0].includes("Warning: React does not recognize the") ||
      args[0].includes("Warning: Invalid DOM property"))
  ) {
    return;
  }
  originalConsoleError(...args);
};
