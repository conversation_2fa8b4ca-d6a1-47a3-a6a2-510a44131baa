import React, { memo, useRef, useEffect, useState } from "react";
import {
  Input,
  Button,
  Typography,
  Avatar,
  Spin,
  Alert,
  Dropdown,
  Flex,
} from "antd";
import {
  SendOutlined,
  UserOutlined,
  RobotOutlined,
  MoreOutlined,
  ClearOutlined,
  InfoCircleOutlined,
  CloseOutlined,
  ExpandOutlined,
  CompressOutlined,
} from "@ant-design/icons";
import { useTheme } from "antd-style";
import dayjs from "dayjs";
import { useChatStore } from "../../../store/chat-store";
import { useSendChatMessage } from "../../../services/mutations";
import ModelSelector from "./model-selector";

const { TextArea } = Input;
const { Text, Paragraph } = Typography;

const ChatMessage = memo(({ message }) => {
  const theme = useTheme();
  const isUser = message.role === "user";

  return (
    <div
      style={{
        display: "flex",
        flexDirection: isUser ? "row-reverse" : "row",
        marginBottom: 16,
        alignItems: "flex-start",
      }}
    >
      <Avatar
        icon={isUser ? <UserOutlined /> : <RobotOutlined />}
        style={{
          backgroundColor: isUser ? theme.colorPrimary : theme.colorSuccess,
          margin: isUser ? "0 0 0 8px" : "0 8px 0 0",
        }}
      />
      <div
        style={{
          maxWidth: "70%",
          padding: "8px 12px",
          borderRadius: 8,
          backgroundColor: isUser
            ? theme.colorPrimaryBg
            : theme.colorBgContainer,
          border: `1px solid ${theme.colorBorder}`,
        }}
      >
        <Paragraph
          style={{
            margin: 0,
            fontSize: 14,
            whiteSpace: "pre-wrap",
          }}
        >
          {message.isLoading ? (
            <Spin size="small" style={{ marginRight: 8 }} />
          ) : null}
          {message.content}
        </Paragraph>
        <Text
          type="secondary"
          style={{
            fontSize: 12,
            marginTop: 4,
            display: "block",
          }}
        >
          {dayjs(message.timestamp).format("HH:mm")}
        </Text>
        {message.error && (
          <Alert
            message={message.error}
            type="error"
            size="small"
            style={{ marginTop: 4 }}
          />
        )}
      </div>
    </div>
  );
});

ChatMessage.displayName = "ChatMessage";

const ChatInterface = memo(({ handleToggleExpand, isExpanded, toggleChat }) => {
  const theme = useTheme();
  const messagesEndRef = useRef(null);
  const [inputValue, setInputValue] = useState("");

  const {
    messages,
    isLoading,
    error,
    selectedModel,
    availableModels,
    addMessage,
    updateMessage,
    clearMessages,
    setLoading,
    setError,
    setSelectedModel,
    generateMessageId,
  } = useChatStore();

  // Handle model change with notification
  const handleModelChange = (newModel) => {
    setSelectedModel(newModel);

    // Add a system message about model change
    const systemMessage = {
      id: generateMessageId(),
      content: `Switched to ${newModel}. This model may have different capabilities and response styles.`,
      role: "assistant",
      timestamp: new Date(),
      isLoading: false,
    };
    addMessage(systemMessage);
  };

  // Chat actions menu
  const chatActions = [
    {
      key: "clear",
      label: "Clear Chat",
      icon: <ClearOutlined />,
      onClick: () => {
        clearMessages();
        // Add welcome message back
        const welcomeMessage = {
          id: "welcome_msg_new",
          content: "Chat cleared! How can I help you today?",
          role: "assistant",
          timestamp: new Date(),
          isLoading: false,
        };
        addMessage(welcomeMessage);
      },
    },
    {
      key: "info",
      label: "Model Info",
      icon: <InfoCircleOutlined />,
      onClick: () => {
        const infoMessage = {
          id: generateMessageId(),
          content: `Current model: ${selectedModel}\n\nThis AI assistant can help you with:\n• Network device management\n• Command execution\n• Troubleshooting\n• Configuration assistance\n• System monitoring`,
          role: "assistant",
          timestamp: new Date(),
          isLoading: false,
        };
        addMessage(infoMessage);
      },
    },
  ];

  const sendChatMessage = useSendChatMessage();

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "auto" });
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage = {
      id: generateMessageId(),
      content: inputValue.trim(),
      role: "user",
      timestamp: new Date(),
      isLoading: false,
    };

    const assistantMessageId = generateMessageId();
    const assistantMessage = {
      id: assistantMessageId,
      content: "Thinking...",
      role: "assistant",
      timestamp: new Date(),
      isLoading: true,
    };

    // Add user message and placeholder assistant message
    addMessage(userMessage);
    addMessage(assistantMessage);
    setInputValue("");
    setLoading(true);
    setError(null);

    try {
      const response = await sendChatMessage.mutateAsync({
        query: userMessage.content,
        model: selectedModel, // Include selected model in request
      });

      // Update assistant message with response
      let responseContent = "No response received";

      if (response) {
        if (typeof response === "string") {
          responseContent = response;
        } else if (response.message) {
          responseContent = response.message;
        } else if (response.content) {
          responseContent = response.content;
        } else if (response.actions && Array.isArray(response.actions)) {
          // Handle structured response with actions
          responseContent = response.message || "I found some actions for you:";
          if (response.actions.length > 0) {
            responseContent +=
              "\n\n" +
              response.actions
                .map(
                  (action) =>
                    `• ${action.type}: ${action.cmd || action.uri || "No command"}`
                )
                .join("\n");
          }
        }
      }

      updateMessage(assistantMessageId, responseContent);
    } catch (error) {
      console.error("Chat error:", error);
      updateMessage(
        assistantMessageId,
        "Sorry, I encountered an error. Please try again."
      );
      setError(error.message || "Failed to send message");
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
    // Escape key to clear input
    if (e.key === "Escape") {
      setInputValue("");
    }
  };

  return (
    <div
      style={{
        height: "100%",
        display: "flex",
        flexDirection: "column",
        backgroundColor: theme.colorBgLayout,
      }}
    >
      {/* Chat Header */}
      <div
        style={{
          padding: "12px 16px",
          borderBottom: `1px solid ${theme.colorBorder}`,
          backgroundColor: theme.colorBgContainer,
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            flexDirection: "column",
          }}
        >
          <Flex vertical>
            <Flex justify="space-between">
              <Text strong>NIMBL Assistant</Text>
              <Flex gap={4}>
                <Dropdown
                  menu={{
                    items: chatActions,
                    onClick: ({ key }) => {
                      const action = chatActions.find((a) => a.key === key);
                      action?.onClick();
                    },
                  }}
                  trigger={["click"]}
                  placement="bottomRight"
                >
                  <Button
                    type="text"
                    size="small"
                    icon={<MoreOutlined />}
                    disabled={isLoading}
                    title="Chat actions"
                  />
                </Dropdown>
                <Button
                  type="text"
                  size="small"
                  icon={isExpanded ? <CompressOutlined /> : <ExpandOutlined />}
                  onClick={handleToggleExpand}
                  title={isExpanded ? "Minimize" : "Expand"}
                />
                <Button
                  type="text"
                  size="small"
                  icon={<CloseOutlined />}
                  onClick={toggleChat}
                  title="Close Chat"
                />
              </Flex>
            </Flex>

            <Text type="secondary" style={{ fontSize: 12 }}>
              Ask me anything about your network
            </Text>
          </Flex>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <ModelSelector
              value={selectedModel}
              onChange={handleModelChange}
              disabled={isLoading}
              size="small"
            />
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <div
        style={{
          flex: 1,
          padding: "16px",
          overflowY: "auto",
          minHeight: 0,
        }}
      >
        {messages.map((message) => (
          <ChatMessage key={message.id} message={message} />
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Error Display */}
      {error && (
        <div style={{ padding: "0 16px" }}>
          <Alert
            message={error}
            type="error"
            closable
            onClose={() => setError(null)}
            style={{ marginBottom: 8 }}
          />
        </div>
      )}

      {/* Input Area */}
      <div
        style={{
          padding: "12px 16px",
          borderTop: `1px solid ${theme.colorBorder}`,
          backgroundColor: theme.colorBgContainer,
        }}
      >
        <div style={{ display: "flex", gap: 8 }}>
          <TextArea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message... (Enter to send, Shift+Enter for new line)"
            autoSize={{ minRows: 1, maxRows: 4 }}
            disabled={isLoading}
            style={{ flex: 1 }}
            aria-label="Chat message input"
            role="textbox"
            aria-multiline="true"
          />
          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={handleSendMessage}
            loading={isLoading}
            disabled={!inputValue.trim()}
            aria-label="Send message"
            title="Send message (Enter)"
          />
        </div>
      </div>
    </div>
  );
});

ChatInterface.displayName = "ChatInterface";

export default ChatInterface;
