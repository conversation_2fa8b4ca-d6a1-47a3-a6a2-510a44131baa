package jsonutils

import (
	"encoding/json"
	"errors"
	"strings"

	"github.com/itchyny/gojq"
)

// TrimToEnclosure trims the string to the content whithin enclosures {} or []
// This function try to extract the JSON object from the string
func TrimToEnclosure(input string) (string, error) {
	// Find the first opening brace/bracket
	startIndex := strings.IndexAny(input, "[{")
	if startIndex == -1 {
		return "", errors.New("no opening bracket or brace found")
	}

	// Find the last closing brace/bracket
	endIndex := strings.LastIndexAny(input, "]}")
	if endIndex == -1 {
		return "", errors.New("no closing bracket or brace found")
	}

	// Ensure braces/brackets are correctly paired
	opening := input[startIndex]
	closing := input[endIndex]
	if (opening == '{' && closing != '}') || (opening == '[' && closing != ']') {
		return "", errors.New("mismatched brackets or braces")
	}

	// Extract the substring between the first opening and the last closing brace/bracket
	return input[startIndex : endIndex+1], nil
}

// QueryJSONString queries the JSON string with the given jq query
func QueryJSONString(jsonString string, query string) (any, error) {
	q, err := gojq.Parse(query)
	if err != nil {
		return nil, err
	}
	var jsonMap map[string]interface{}
	err = json.Unmarshal([]byte(jsonString), &jsonMap)
	if err != nil {
		return "", err
	}
	iter := q.Run(jsonMap)
	for {
		v, ok := iter.Next()
		if !ok {
			break
		}
		return v, nil
	}
	return nil, nil
}
